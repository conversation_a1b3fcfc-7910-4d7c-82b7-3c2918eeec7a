.\"
.\" Copyright (c) 2012, <PERSON> <<EMAIL>>
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFFieldPassCount 3TIFF "July 26, 2012" "libtiff"
.SH NAME
TIFFFieldPassCount \- Get whether to pass a count to TIFFGet/SetField
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.BI "int TIFFFieldPassCount(const TIFFField* " fip ")"
.SH DESCRIPTION
.BR TIFFFieldPassCount
returns true (nonzero) if
.BR TIFFGetField
and
.BR TIFFSetField
expect a
.I count
value to be passed before the actual data pointer.
.P
.I fip
is a field information pointer previously returned by
.BR TIFFFindField ,
.BR TIFFFieldWithTag ,
or
.BR TIFFFieldWithName .
.P
When a
.I count
is required, it will be of type
.BR uint32
when
.BR TIFFFieldReadCount
reports
.BR TIFF_VARIABLE2 ,
and of type
.BR uint16
otherwise.  (This distinction is critical for use of
.BR TIFFGetField ,
but normally not so for use of
.BR TIFFSetField .)
.br
.SH "RETURN VALUES"
.br
.BR TIFFFieldPassCount
returns an integer that is always 1 (true) or 0 (false).
.br
.SH "SEE ALSO"
.BR libtiff (3TIFF),
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
