.\"
.\" Copyright (c) 1988-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFINFO 1 "November 2, 2005" "libtiff"
.SH NAME
tiffinfo \- print information about
.SM TIFF
files
.SH SYNOPSIS
.B tiffinfo
[
.I options
]
.I "input.tif \&..."
.SH DESCRIPTION
.I Tiffinfo
displays information about files created according
to the Tag Image File Format, Revision 6.0.
By default, the contents of each
.SM TIFF
directory in each file
is displayed, with the value of each tag shown symbolically
(where sensible).
.SH OPTIONS
.TP
.B \-c
Display the colormap and color/gray response curves, if present.
.TP
.B \-D
In addition to displaying the directory tags,
read and decompress all the data in each image (but not display it).
.TP
.B \-d
In addition to displaying the directory tags,
print each byte of decompressed data in hexadecimal.
.TP
.B \-j
Display any \s-2JPEG\s0-related tags that are present.
.TP
.B \-o
Set the initial
.SM TIFF
directory according to the specified file offset.
The file offset may be specified using the usual C-style syntax;
i.e. a leading ``0x'' for hexadecimal and a leading ``0'' for octal.
.TP
.B \-s
Display the offsets and byte counts for each data strip in a directory.
.TP
.B \-z
Enable strip chopping when reading image data.
.TP
.B \-#
Set the initial
.SM TIFF
directory to
.IR # .
.SH "SEE ALSO"
.BR pal2rgb (1),
.BR tiffcp (1),
.BR tiffcmp (1),
.BR tiffmedian (1),
.BR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
