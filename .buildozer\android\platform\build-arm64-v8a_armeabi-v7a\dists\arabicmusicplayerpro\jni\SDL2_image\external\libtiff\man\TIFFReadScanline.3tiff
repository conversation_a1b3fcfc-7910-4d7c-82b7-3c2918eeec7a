.\"
.\" Copyright (c) 1988-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFReadScanline 3TIFF "October 15, 1995" "libtiff"
.SH NAME
TIFFReadScanline \- read and decode a scanline of data from an open
.SM TIFF
file
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.BI "int TIFFReadScanline(TIFF *" tif ", tdata_t " buf ", uint32 " row ", tsample_t " sample ")"
.SH DESCRIPTION
Read the data for the specified row into the (user supplied) data buffer
.IR buf .
The data are returned decompressed and, in the native byte- and bit-ordering,
but are otherwise packed (see further below). The buffer must be large enough
to hold an entire scanline of data. Applications should call the routine
.IR TIFFScanlineSize
to find out the size (in bytes) of a scanline buffer.
The
.I row
parameter is always used by
.IR TIFFReadScanline ;
the
.I sample
parameter is used only if data are organized in separate planes (\c
.IR PlanarConfiguration =2).
.SH NOTES
The library attempts to hide bit- and byte-ordering differences between the
image and the native machine by converting data to the native machine order.
Bit reversal is done if the
.I FillOrder
tag is opposite to the native machine bit order. 16- and 32-bit samples are
automatically byte-swapped if the file was written with a byte order opposite
to the native machine byte order,
.PP
In C++ the
.I sample
parameter defaults to 0.
.SH "RETURN VALUES"
.IR TIFFReadScanline
returns \-1 if it detects an error; otherwise 1 is returned.
.SH DIAGNOSTICS
All error messages are directed to the
.IR TIFFError (3TIFF)
routine.
.PP
.BR "Compression algorithm does not support random access" .
Data was requested in a non-sequential order from a file that uses a
compression algorithm and that has
.I RowsPerStrip
greater than one.
That is, data in the image is stored in a compressed form, and with multiple
rows packed into a strip. In this case, the library does not support random
access to the data. The data should either be accessed sequentially, or the
file should be converted so that each strip is made up of one row of data.
.SH BUGS
Reading subsampled YCbCR data does not work correctly because, for 
.IR PlanarConfiguration =2
the size of a scanline is not calculated on a per-sample basis, and for
.IR PlanarConfiguration =1
the library does not unpack the block-interleaved samples; use the strip- and
tile-based interfaces to read these formats.
.SH "SEE ALSO"
.BR TIFFOpen (3TIFF),
.BR TIFFReadEncodedStrip (3TIFF),
.BR TIFFReadRawStrip (3TIFF),
.BR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
