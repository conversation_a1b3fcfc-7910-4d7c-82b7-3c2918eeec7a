.\"
.\" Copyright (c) 1988-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFWriteTile 3TIFF "November 29, 1999" "libtiff"
.SH NAME
TIFFWriteTile \- encode and write a tile of data to an open
.SM TIFF
file
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.BI "tsize_t TIFFWriteTile(TIFF *" tif ", tdata_t " buf ", uint32 " x ", uint32 " y ", uint32 " z ", tsample_t " sample ")"
.SH DESCRIPTION
Write the data for the tile
.I containing
the specified coordinates. The data in
.I buf
are is (potentially) compressed, and written to the indicated file, normally
being appended to the end of the file. The buffer must be contain an entire
tile of data. Applications should call the routine
.IR TIFFTileSize
to find out the size (in bytes) of a tile buffer. The
.I x
and
.I y
parameters are always used by
.IR TIFFWriteTile .
The
.I z
parameter is used if the image is deeper than 1 slice (\c
.IR ImageDepth >1).
The
.I sample
parameter is used only if data are organized in separate planes (\c
.IR PlanarConfiguration =2).
.SH "RETURN VALUES"
.IR TIFFWriteTile
returns \-1 if it detects an error; otherwise the number of bytes in the tile
is returned.
.SH DIAGNOSTICS
All error messages are directed to the
.BR TIFFError (3TIFF)
routine.
.SH "SEE ALSO"
.BR TIFFCheckTile (3TIFF),
.BR TIFFComputeTile (3TIFF),
.BR TIFFOpen (3TIFF),
.BR TIFFReadTile (3TIFF),
.BR TIFFWriteScanline (3TIFF),
.BR TIFFWriteEncodedTile (3TIFF),
.BR TIFFWriteRawTile (3TIFF),
.BR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
