- 7/4/2019: version 1.0.3
  This is a binary compatible release.
  * resize fixes for Nx1 sizes and the addition of non-opaque alpha values for
    odd sizes (issues #418, #434)
  * lossless encode/decode performance improvements
  * lossy compression performance improvement at low quality levels with flat
    content (issue #432)
  * python swig files updated to support python 3
  Tool updates:
    vwebp will now preserve the aspect ratio of images that exceed monitor
    resolution by scaling the image to fit (issue #433)

- 1/14/2019: version 1.0.2
  This is a binary compatible release.
  * (Windows) unicode file support in the tools (linux and mac already had
    support, issue #398)
  * lossless encoder speedups
  * lossy encoder speedup on ARM
  * lossless multi-threaded security fix (chromium:917029)

- 11/2/2018: version 1.0.1
  This is a binary compatible release.
  * lossless encoder speedups
  * big-endian fix for alpha decoding (issue #393)
  * gif2webp fix for loop count=65535 transcode (issue #382)
  * further security related hardening in libwebp & libwebpmux
    (issues #383, #385, #386, #387, #388, #391)
    (oss-fuzz #9099, #9100, #9105, #9106, #9111, #9112, #9119, #9123, #9170,
              #9178, #9179, #9183, #9186, #9191, #9364, #9417, #9496, #10349,
              #10423, #10634, #10700, #10838, #10922, #11021, #11088, #11152)
  * miscellaneous bug & build fixes (issues #381, #394, #396, #397, #400)

- 4/2/2018: version 1.0.0
  This is a binary compatible release.
  * lossy encoder improvements to avoid chroma shifts in various circumstances
    (issues #308, #340)
  * big-endian fixes for decode, RGBA import and WebPPictureDistortion
  Tool updates:
    gifwebp, anim_diff - default duration behavior (<= 10ms) changed to match
                         web browsers, transcoding tools (issue #379)
    img2webp, webpmux - allow options to be passed in via a file (issue #355)

- 11/24/2017: version 0.6.1
  This is a binary compatible release.
  * lossless performance and compression improvements + a new 'cruncher' mode
    (-m 6 -q 100)
  * ARM performance improvements with clang (15-20% w/ndk r15c, issue #339)
  * webp-js: emscripten/webassembly based javascript decoder
  * miscellaneous bug & build fixes (issue #329, #332, #343, #353, #360, #361,
    #363)
  Tool updates / additions:
    added webpinfo - prints file format information (issue #330)
    gif2webp - loop behavior modified to match Chrome M63+ (crbug.com/649264);
               '-loop_compatibility' can be used for the old behavior

- 1/26/2017: version 0.6.0
  * lossless performance and compression improvements
  * miscellaneous performance improvements (SSE2, NEON, MSA)
  * webpmux gained a -duration option allowing for frame timing modification
  * new img2webp utility allowing a sequence of images to be converted to
    animated webp
  * API changes:
    - libwebp:
      WebPPictureSharpARGBToYUVA
      WebPPlaneDistortion
    - libwebpmux / gif2webp:
      WebPAnimEncoderOptions: kmax <= 0 now disables keyframes, kmax == 1
                              forces all keyframes. See mux.h and the gif2webp
                              manpage for details.

- 12/13/2016: version 0.5.2
  This is a binary compatible release.
  This release covers CVE-2016-8888 and CVE-2016-9085.
  * further security related hardening in the tools; fixes to
    gif2webp/AnimEncoder (issues #310, #314, #316, #322), cwebp/libwebp (issue
    #312)
  * full libwebp (encoder & decoder) iOS framework; libwebpdecoder
    WebP.framework renamed to WebPDecoder.framework (issue #307)
  * CMake support for Android Studio (2.2)
  * miscellaneous build related fixes (issue #306, #313)
  * miscellaneous documentation improvements (issue #225)
  * minor lossy encoder fixes and improvements

- 6/14/2016: version 0.5.1
  This is a binary compatible release.
  * miscellaneous bug fixes (issues #280, #289)
  * reverted alpha plane encoding with color cache for compatibility with
    libwebp 0.4.0->0.4.3 (issues #291, #298)
  * lossless encoding performance improvements
  * memory reduction in both lossless encoding and decoding
  * force mux output to be in the extended format (VP8X) when undefined chunks
    are present (issue #294)
  * gradle, cmake build support
  * workaround for compiler bug causing 64-bit decode failures on android
    devices using clang-3.8 in the r11c NDK
  * various WebPAnimEncoder improvements

- 12/17/2015: version 0.5.0
  * miscellaneous bug & build fixes (issues #234, #258, #274, #275, #278)
  * encoder & decoder speed-ups on x86/ARM/MIPS for lossy & lossless
    - note! YUV->RGB conversion was sped-up, but the results will be slightly
      different from previous releases
  * various lossless encoder improvements
  * gif2webp improvements, -min_size option added
  * tools fully support input from stdin and output to stdout (issue #168)
  * New WebPAnimEncoder API for creating animations
  * New WebPAnimDecoder API for decoding animations
  * other API changes:
    - libwebp:
      WebPPictureSmartARGBToYUVA() (-pre 4 in cwebp)
      WebPConfig::exact (-exact in cwebp; -alpha_cleanup is now the default)
      WebPConfig::near_lossless (-near_lossless in cwebp)
      WebPFree() (free'ing webp allocated memory in other languages)
      WebPConfigLosslessPreset()
      WebPMemoryWriterClear()
    - libwebpdemux: removed experimental fragment related fields and functions
    - libwebpmux: WebPMuxSetCanvasSize()
  * new libwebpextras library with some uncommon import functions:
    WebPImportGray/WebPImportRGB565/WebPImportRGB4444

- 10/15/15: version 0.4.4
  This is a binary compatible release.
  * rescaling out-of-bounds read fix (issue #254)
  * various build fixes and improvements (issues #253, #259, #262, #267, #268)
  * container documentation update
  * gif2webp transparency fix (issue #245)

- 3/3/15: version 0.4.3
  This is a binary compatible release.
  * Android / gcc / iOS / MSVS build fixes and improvements
  * lossless decode fix (issue #239 -- since 0.4.0)
  * documentation / vwebp updates for animation
  * multi-threading fix (issue #234)

- 10/13/14: version 0.4.2
  This is a binary compatible release.
  * Android / gcc build fixes
  * (Windows) fix reading from stdin and writing to stdout
  * gif2webp: miscellaneous fixes
  * fix 'alpha-leak' with lossy compression (issue #220)
  * the lossless bitstream spec has been amended to reflect the current code

- 7/24/14: version 0.4.1
  This is a binary compatible release.
  * AArch64 (arm64) & MIPS support/optimizations
  * NEON assembly additions:
    - ~25% faster lossy decode / encode (-m 4)
    - ~10% faster lossless decode
    - ~5-10% faster lossless encode (-m 3/4)
  * dwebp/vwebp can read from stdin
  * cwebp/gif2webp can write to stdout
  * cwebp can read webp files; useful if storing sources as webp lossless

- 12/19/13: version 0.4.0
  * improved gif2webp tool
  * numerous fixes, compression improvement and speed-up
  * dither option added to decoder (dwebp -dither 50 ...)
  * improved multi-threaded modes (-mt option)
  * improved filtering strength determination
  * New function: WebPMuxGetCanvasSize
  * BMP and TIFF format output added to 'dwebp'
  * Significant memory reduction for decoding lossy images with alpha.
  * Intertwined decoding of RGB and alpha for a shorter
    time-to-first-decoded-pixel.
  * WebPIterator has a new member 'has_alpha' denoting whether the frame
    contains transparency.
  * Container spec amended with new 'blending method' for animation.

- 6/13/13: version 0.3.1
  This is a binary compatible release.
  * Add incremental decoding support for images containing ALPH and ICCP chunks.
  * Python bindings via swig for the simple encode/decode interfaces similar to
    Java.

- 3/20/13: version 0.3.0
  This is a binary compatible release.
  * WebPINewRGB/WebPINewYUVA accept being passed a NULL output buffer
    and will perform auto-allocation.
  * default filter option is now '-strong -f 60'
  * encoding speed-up for lossy methods 3 to 6
  * alpha encoding can be done in parallel to lossy using 'cwebp -mt ...'
  * color profile, metadata (XMP/EXIF) and animation support finalized in the
    container.
  * various NEON assembly additions
  Tool updates / additions:
    * gif2webp added
    * vwebp given color profile & animation support
    * cwebp can preserve color profile / metadata with '-metadata'

- 10/30/12: version 0.2.1
  * Various security related fixes
  * cwebp.exe: fix import errors on Windows XP
  * enable DLL builds for mingw targets

- 8/3/12: version 0.2.0
  * Add support for ARGB -> YUVA conversion for lossless decoder
    New functions: WebPINewYUVA, WebPIDecGetYUVA
  * Add stats for lossless and alpha encoding
  * Security related hardening: allocation and size checks
  * Add PAM output support to dwebp

- 7/19/12: version 0.1.99
  * This is a pre-release of 0.2.0, not an rc to allow for further
    incompatible changes based on user feedback.
  * Alpha channel encode/decode support.
  * Lossless encoder/decoder.
  * Add TIFF input support to cwebp.
  Incompatible changes:
    * The encode ABI has been modified to support alpha encoding.
    * Deprecated function WebPINew() has been removed.
    * Decode function signatures have changed to consistently use size_t over
      int/uint32_t.
    * decode_vp8.h is no longer installed system-wide.
    * cwebp will encode the alpha channel if present.

- 9/19/11: version 0.1.3
  * Advanced decoding APIs.
  * On-the-fly cropping and rescaling of images.
  * SSE2 instructions for decoding performance optimizations on x86 based
    platforms.
  * Support Multi-threaded decoding.
  * 40% improvement in Decoding performance.
  * Add support for RGB565, RGBA4444 & ARGB image colorspace.
  * Better handling of large picture encoding.

- 3/25/11: version 0.1.2
  * Incremental decoding: picture can be decoded byte-by-byte if needs be.
  * lot of bug-fixes, consolidation and stabilization

- 2/23/11: initial release of version 0.1, with the new encoder
- 9/30/10: initial release version with only the lightweight decoder
