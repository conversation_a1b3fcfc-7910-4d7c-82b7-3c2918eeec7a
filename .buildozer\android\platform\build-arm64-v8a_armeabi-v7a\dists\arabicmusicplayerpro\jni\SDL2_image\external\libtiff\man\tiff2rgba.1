.\"
.\" Copyright (c) 1988-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFF2RGBA 1 "November 2, 2005" "libtiff"
.SH NAME
tiff2rgba \- convert a 
.SM TIFF
image to RGBA color space
.SH SYNOPSIS
.B tiff2rgba
[
.I options
]
.I input.tif
.I output.tif
.SH DESCRIPTION
.I Tiff2rgba
converts a wide variety of TIFF images into an RGBA TIFF image.  This 
includes the ability to translate different color spaces and photometric
interpretation into RGBA, support for alpha blending, and translation
of many different bit depths into a 32bit RGBA image.
.P
Internally this program is implemented using the
.I TIFFReadRGBAImage()
function, and it suffers any limitations of that image.  This includes
limited support for > 8 BitsPerSample images, and flaws with some
esoteric combinations of BitsPerSample, photometric interpretation, 
block organization and planar configuration.  
.P
The generated images are stripped images with four samples per pixel 
(red, green, blue and alpha) or if the
.B \-n
flag is used, three samples
per pixel (red, green, and blue).  The resulting images are always planar
configuration contiguous.  For this reason, this program is a useful utility
for transform exotic TIFF files into a form ingestible by almost any TIFF
supporting software. 
.SH OPTIONS
.TP
.B \-c
Specify a compression scheme to use when writing image data:
.B "\-c none"
for no compression (the default),
.B "\-c packbits"
for the PackBits compression algorithm,
.B "\-c zip"
for the Deflate compression algorithm,
.B "\-c jpeg"
for the JPEG compression algorithm,
and
.B "\-c lzw"
for Lempel-Ziv & Welch.
.TP
.B \-r
Write data with a specified number of rows per strip;
by default the number of rows/strip is selected so that each strip
is approximately 8 kilobytes.
.TP
.B \-b
Process the image one block (strip/tile) at a time instead of by reading
the whole image into memory at once.  This may be necessary for very large
images on systems with limited RAM.
.TP
.B \-n
Drop the alpha component from the output file, producing a pure RGB file.
Currently this does not work if the
.B \-b
flag is also in effect.
.TP
.BI \-M " size"
Set maximum memory allocation size (in MiB). The default is 256MiB.
Set to 0 to disable the limit.
.SH "SEE ALSO"
.BR tiff2bw (1),
.BR TIFFReadRGBAImage (3t),
.BR libtiff (3)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
