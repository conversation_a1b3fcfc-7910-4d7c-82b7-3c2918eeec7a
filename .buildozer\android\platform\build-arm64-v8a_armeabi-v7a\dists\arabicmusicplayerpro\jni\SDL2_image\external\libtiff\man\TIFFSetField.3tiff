.\"
.\" Copyright (c) 1988-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFSetField 3TIFF "October 29, 2004" "libtiff"
.SH NAME
TIFFSetField, TIFFVSetField \- set the value(s) of a tag in a
.SM TIFF
file open for writing
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.BI "int TIFFSetField(TIFF *" tif ", ttag_t " tag ", " ... ")"
.sp
.B "#include <stdarg.h>"
.sp
.BI "int TIFFVSetField(TIFF *" tif ", ttag_t " tag ", va_list " ap ")"
.SH DESCRIPTION
.IR TIFFSetField
sets the value of a field
or pseudo-tag in the current directory associated with
the open
.SM TIFF
file
.IR tif .
(A
.I pseudo-tag 
is a parameter that is used to control the operation of the
.SM TIFF
library but whose value is not read or written to the underlying file.)
To set the value of a field
the file must have been previously opened for writing with
.IR TIFFOpen (3TIFF);
pseudo-tags can be set whether the file was opened for reading
or writing.
The field is identified by
.IR tag ,
one of the values defined in the include file
.B tiff.h
(see also the table below).
The actual value is specified using a variable argument list,
as prescribed by the
.IR stdarg (3)
interface (\c
or, on some machines, the
.IR varargs (3)
interface.)
.PP
.IR TIFFVSetField
is functionally equivalent to
.IR TIFFSetField
except that it takes a pointer to a variable
argument list.
.I TIFFVSetField
is useful for writing routines that are layered
on top of the functionality provided by
.IR TIFFSetField .
.PP
The tags understood by
.IR libtiff ,
the number of parameter values, and the
expected types for the parameter values are shown below.
The data types are:
.I char*
is null-terminated string and corresponds to the
.SM ASCII
data type;
.I uint16
is an unsigned 16-bit value;
.I uint32
is an unsigned 32-bit value;
.I uint16*
is an array of unsigned 16-bit values.
.I void*
is an array of data values of unspecified type.

Consult the
.SM TIFF
specification for information on the meaning of each tag.
.PP
.nf
.ta \w'TIFFTAG_CONSECUTIVEBADFAXLINES'u+2n +\w'Count'u+2n +\w'TIFFFaxFillFunc \(dg'u+2n
\fITag Name\fP	\fICount\fP	\fITypes\fP	\fINotes\fP
.sp 5p
TIFFTAG_ARTIST	1	char*
TIFFTAG_BADFAXLINES	1	uint32
TIFFTAG_BITSPERSAMPLE	1	uint16	\(dg
TIFFTAG_CLEANFAXDATA	1	uint16
TIFFTAG_COLORMAP	3	uint16*	1<<BitsPerSample arrays
TIFFTAG_COMPRESSION	1	uint16	\(dg
TIFFTAG_CONSECUTIVEBADFAXLINES	1	uint32
TIFFTAG_COPYRIGHT	1	char*
TIFFTAG_DATETIME	1	char*
TIFFTAG_DOCUMENTNAME	1	char*
TIFFTAG_DOTRANGE	2	uint16
TIFFTAG_EXTRASAMPLES	2	uint16,uint16*	\(dg count & types array
TIFFTAG_FAXFILLFUNC	1	TIFFFaxFillFunc	G3/G4 compression pseudo-tag
TIFFTAG_FAXMODE	1	int	\(dg G3/G4 compression pseudo-tag
TIFFTAG_FILLORDER	1	uint16	\(dg
TIFFTAG_GROUP3OPTIONS	1	uint32	\(dg
TIFFTAG_GROUP4OPTIONS	1	uint32	\(dg
TIFFTAG_HALFTONEHINTS	2	uint16
TIFFTAG_HOSTCOMPUTER	1	char*
TIFFTAG_ICCPROFILE	2	uint32,void*	count, profile data
TIFFTAG_IMAGEDEPTH	1	uint32	\(dg
TIFFTAG_IMAGEDESCRIPTION	1	char*
TIFFTAG_IMAGELENGTH	1	uint32
TIFFTAG_IMAGEWIDTH	1	uint32	\(dg
TIFFTAG_INKNAMES	2	uint16, char*
TIFFTAG_INKSET	1	uint16	\(dg
TIFFTAG_JPEGCOLORMODE	1	int	\(dg JPEG pseudo-tag
TIFFTAG_JPEGQUALITY	1	int	JPEG pseudo-tag
TIFFTAG_JPEGTABLES	2	uint32*,void*	\(dg count & tables
TIFFTAG_JPEGTABLESMODE	1	int	\(dg JPEG pseudo-tag
TIFFTAG_MAKE	1	char*
TIFFTAG_MATTEING	1	uint16	\(dg
TIFFTAG_MAXSAMPLEVALUE	1	uint16
TIFFTAG_MINSAMPLEVALUE	1	uint16
TIFFTAG_MODEL	1	char*
TIFFTAG_ORIENTATION	1	uint16
TIFFTAG_PAGENAME	1	char*
TIFFTAG_PAGENUMBER	2	uint16
TIFFTAG_PHOTOMETRIC	1	uint16
TIFFTAG_PHOTOSHOP	?	uint32,void*	count, data
TIFFTAG_PLANARCONFIG	1	uint16	\(dg
TIFFTAG_PREDICTOR	1	uint16	\(dg
TIFFTAG_PRIMARYCHROMATICITIES	1	float*	6-entry array
TIFFTAG_REFERENCEBLACKWHITE	1	float*	\(dg 6-entry array
TIFFTAG_RESOLUTIONUNIT	1	uint16
TIFFTAG_RICHTIFFIPTC	2	uint32,void*	count, data
TIFFTAG_ROWSPERSTRIP	1	uint32	\(dg must be > 0
TIFFTAG_SAMPLEFORMAT	1	uint16	\(dg
TIFFTAG_SAMPLESPERPIXEL	1	uint16	\(dg value must be <= 4
TIFFTAG_SMAXSAMPLEVALUE	1	double
TIFFTAG_SMINSAMPLEVALUE	1	double
TIFFTAG_SOFTWARE	1	char*
TIFFTAG_STONITS	1	double	\(dg
TIFFTAG_SUBFILETYPE	1	uint32
TIFFTAG_SUBIFD	2	uint16,uint64*	count & offsets array
TIFFTAG_TARGETPRINTER	1	char*
TIFFTAG_THRESHHOLDING	1	uint16
TIFFTAG_TILEDEPTH	1	uint32	\(dg
TIFFTAG_TILELENGTH	1	uint32	\(dg must be a multiple of 8
TIFFTAG_TILEWIDTH	1	uint32	\(dg must be a multiple of 8
TIFFTAG_TRANSFERFUNCTION	1 or 3\(dd uint16*	1<<BitsPerSample entry arrays
TIFFTAG_WHITEPOINT	1	float*	2-entry array
TIFFTAG_XMLPACKET	2	uint32,void*	count, data
TIFFTAG_XPOSITION	1	float
TIFFTAG_XRESOLUTION	1	float
TIFFTAG_YCBCRCOEFFICIENTS	1	float*	\(dg 3-entry array
TIFFTAG_YCBCRPOSITIONING	1	uint16	\(dg
TIFFTAG_YCBCRSAMPLING	2	uint16	\(dg
TIFFTAG_YPOSITION	1	float
TIFFTAG_YRESOLUTION	1	float
.fi
.sp 5p
\(dg Tag may not have its values changed once data is written.
.br
.fi
\(dd
If
.I SamplesPerPixel
is one, then a single array is passed; otherwise three arrays should be
passed.
.fi
* The contents of this field are quite complex.  See 
.BR "The ICC Profile Format Specification" ,
Annex B.3 "Embedding ICC Profiles in TIFF Files"
(available at http://www.color.org) for an explanation.
.br
.SH "RETURN VALUES"
1 is returned if the operation was successful.
Otherwise, 0 is returned if an error was detected.
.SH DIAGNOSTICS
All error messages are directed to the
.BR TIFFError (3TIFF)
routine.
.PP
\fB%s: Cannot modify tag "%s" while writing\fP.
Data has already been written to the file, so the
specified tag's value can not be changed.
This restriction is applied to all tags that affect
the format of written data.
.PP
\fB%d: Bad value for "%s"\fP.
An invalid value was supplied for the named tag.
.SH "SEE ALSO"
.BR TIFFOpen (3TIFF),
.BR TIFFGetField (3TIFF),
.BR TIFFSetDirectory (3TIFF),
.BR TIFFWriteDirectory (3TIFF),
.BR TIFFReadDirectory (3TIFF),
.BR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
