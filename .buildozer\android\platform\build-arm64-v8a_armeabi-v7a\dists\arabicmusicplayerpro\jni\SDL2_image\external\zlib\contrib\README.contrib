All files under this contrib directory are UNSUPPORTED. They were
provided by users of zlib and were not tested by the authors of zlib.
Use at your own risk. Please contact the authors of the contributions
for help about these, not the zlib authors. Thanks.


ada/        by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
        Support for Ada
        See http://zlib-ada.sourceforge.net/

blast/      by <PERSON> <<EMAIL>>
        Decompressor for output of PKWare Data Compression Library (DCL)

delphi/     by <PERSON><PERSON><PERSON> <<EMAIL>>
        Support for Delphi and C++ Builder

dotzlib/    by <PERSON> <hen<PERSON>@ravn.com>
        Support for Microsoft .Net and Visual C++ .Net

gcc_gvmat64/by <PERSON> <<EMAIL>>
        GCC Version of x86 64-bit (AMD64 and Intel EM64t) code for x64
        assembler to replace longest_match() and inflate_fast()

infback9/   by <PERSON> <<EMAIL>>
        Unsupported diffs to infback to decode the deflate64 format

iostream/   by <PERSON> <<EMAIL>>
        A C++ I/O streams interface to the zlib gz* functions

iostream2/  by <PERSON><PERSON>øvset <<EMAIL>>
        Another C++ I/O streams interface

iostream3/  by <PERSON> Schwardt <<EMAIL>>
            and Kevin Ruland <<EMAIL>>
        Yet another C++ I/O streams interface

minizip/    by Gilles Vollant <<EMAIL>>
        Mini zip and unzip based on zlib
        Includes Zip64 support by Mathias Svensson <<EMAIL>>
        See http://www.winimage.com/zLibDll/minizip.html

pascal/     by Bob Dellaca <<EMAIL>> et al.
        Support for Pascal

puff/       by Mark Adler <<EMAIL>>
        Small, low memory usage inflate.  Also serves to provide an
        unambiguous description of the deflate format.

testzlib/   by Gilles Vollant <<EMAIL>>
        Example of the use of zlib

untgz/      by Pedro A. Aranda Gutierrez <<EMAIL>>
        A very simple tar.gz file extractor using zlib

vstudio/    by Gilles Vollant <<EMAIL>>
        Building a minizip-enhanced zlib with Microsoft Visual Studio
        Includes vc11 from kreuzerkrieg and vc12 from davispuh
