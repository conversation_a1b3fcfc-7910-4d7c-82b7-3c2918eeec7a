*.l[ao]
*.[ao]
*.pc
.deps
.libs
/aclocal.m4
/ar-lib
/autom4te.cache
/compile
/config.*
/configure
/depcomp
/dist
/install-sh
/libtool
/ltmain.sh
/missing
/mkinstalldirs
/stamp-h1
Makefile
Makefile.in
examples/anim_diff
examples/[cdv]webp
examples/gif2webp
examples/img2webp
examples/webpmux
src/webp/config.h*
src/webp/stamp-h1
/output
/doc/output
*.idb
*.pdb
/iosbuild
/WebP.framework
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
.gradle
/build
extras/get_disto
extras/webp_quality
