# CMake build for libtiff
#
# Copyright © 2015 Open Microscopy Environment / University of Dundee
# Written by <PERSON> <<EMAIL>>
#
# Permission to use, copy, modify, distribute, and sell this software and
# its documentation for any purpose is hereby granted without fee, provided
# that (i) the above copyright notices and this permission notice appear in
# all copies of the software and related documentation, and (ii) the names of
# <PERSON> and Silicon Graphics may not be used in any advertising or
# publicity relating to the software without the specific, prior written
# permission of <PERSON> and Silicon Graphics.
#
# THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND,
# EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY
# WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.
#
# IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
# ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
# OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
# WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF
# LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE
# OF THIS SOFTWARE.

set(man1_MANS
  fax2ps.1
  fax2tiff.1
  pal2rgb.1
  ppm2tiff.1
  raw2tiff.1
  tiff2bw.1
  tiff2pdf.1
  tiff2ps.1
  tiff2rgba.1
  tiffcmp.1
  tiffcp.1
  tiffcrop.1
  tiffdither.1
  tiffdump.1
  tiffgt.1
  tiffinfo.1
  tiffmedian.1
  tiffset.1
  tiffsplit.1)

set(man3_MANS
  libtiff.3tiff
  TIFFbuffer.3tiff
  TIFFClose.3tiff
  TIFFcodec.3tiff
  TIFFcolor.3tiff
  TIFFDataWidth.3tiff
  TIFFError.3tiff
  TIFFFieldDataType.3tiff
  TIFFFieldName.3tiff
  TIFFFieldPassCount.3tiff
  TIFFFieldReadCount.3tiff
  TIFFFieldTag.3tiff
  TIFFFieldWriteCount.3tiff
  TIFFFlush.3tiff
  TIFFGetField.3tiff
  TIFFmemory.3tiff
  TIFFOpen.3tiff
  TIFFPrintDirectory.3tiff
  TIFFquery.3tiff
  TIFFReadDirectory.3tiff
  TIFFReadEncodedStrip.3tiff
  TIFFReadEncodedTile.3tiff
  TIFFReadRawStrip.3tiff
  TIFFReadRawTile.3tiff
  TIFFReadRGBAImage.3tiff
  TIFFReadRGBAStrip.3tiff
  TIFFReadRGBATile.3tiff
  TIFFReadScanline.3tiff
  TIFFReadTile.3tiff
  TIFFRGBAImage.3tiff
  TIFFSetDirectory.3tiff
  TIFFSetField.3tiff
  TIFFsize.3tiff
  TIFFstrip.3tiff
  TIFFswab.3tiff
  TIFFtile.3tiff
  TIFFWarning.3tiff
  TIFFWriteDirectory.3tiff
  TIFFWriteEncodedStrip.3tiff
  TIFFWriteEncodedTile.3tiff
  TIFFWriteRawStrip.3tiff
  TIFFWriteRawTile.3tiff
  TIFFWriteScanline.3tiff
  TIFFWriteTile.3tiff)

install(FILES ${man1_MANS}
        DESTINATION "${CMAKE_INSTALL_FULL_MANDIR}/man1")
install(FILES ${man3_MANS}
        DESTINATION "${CMAKE_INSTALL_FULL_MANDIR}/man3")

extra_dist(${man1_MANS} ${man3_MANS})
