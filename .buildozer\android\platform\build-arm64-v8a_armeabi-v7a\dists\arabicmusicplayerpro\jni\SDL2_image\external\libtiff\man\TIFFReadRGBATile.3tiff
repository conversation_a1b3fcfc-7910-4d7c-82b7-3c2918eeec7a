.\"
.\" Copyright (c) 1991-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFReadRGBATile 3TIFF "December 10, 1998" "libtiff"
.SH NAME
TIFFReadRGBATile \- read and decode an image tile into a fixed-format raster
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.B "#define TIFFGetR(abgr)	((abgr) & 0xff)"
.br
.B "#define TIFFGetG(abgr)	(((abgr) >> 8) & 0xff)"
.br
.B "#define TIFFGetB(abgr)	(((abgr) >> 16) & 0xff)"
.br
.B "#define TIFFGetA(abgr)	(((abgr) >> 24) & 0xff)"
.sp
.BI "int TIFFReadRGBATile(TIFF *" tif ", uint32 " x ", uint32 " y ", uint32 *" raster ")"
.SH DESCRIPTION
.IR TIFFReadRGBATile
reads a single tile of a tile-based image into memory, storing the result in
the user supplied RGBA
.IR raster .
The raster is assumed to be an array of width times length 32-bit entries,
where width is the width of a tile (TIFFTAG_TILEWIDTH) and length is the
height of a tile (TIFFTAG_TILELENGTH). 

.PP
The 
.IR x
and 
.IR y
values are the offsets from the top left corner to the top left corner of the
tile to be read.  They must be an exact multiple of the tile width and length. 

.PP
Note that the raster is assume to be organized such that the pixel at location
(\fIx\fP,\fIy\fP) is \fIraster\fP[\fIy\fP*\fIwidth\fP+\fIx\fP]; with the
raster origin in the 
.I lower-left hand corner
of the tile. That is bottom to top organization.  Edge tiles which partly fall
off the image will be filled out with appropriate zeroed areas.

.PP
Raster pixels are 8-bit packed red, green, blue, alpha samples. The macros
.IR TIFFGetR ,
.IR TIFFGetG ,
.IR TIFFGetB ,
and
.I TIFFGetA
should be used to access individual samples. Images without Associated Alpha
matting information have a constant Alpha of 1.0 (255).
.PP
See the 
.IR TIFFRGBAImage (3TIFF) 
page for more details on how various image types are converted to RGBA values.
.SH NOTES
Samples must be either 1, 2, 4, 8, or 16 bits. Colorimetric samples/pixel must
be either 1, 3, or 4 (i.e.
.I SamplesPerPixel
minus
.IR ExtraSamples ).
.PP
Palette image colormaps that appear to be incorrectly written as 8-bit values
are automatically scaled to 16-bits.
.PP
.I TIFFReadRGBATile
is just a wrapper around the more general
.IR TIFFRGBAImage (3TIFF)
facilities.  It's main advantage over the similar 
.IR TIFFReadRGBAImage() 
function is that for large images a single buffer capable of holding the whole
image doesn't need to be allocated, only enough for one tile.  The 
.IR TIFFReadRGBAStrip() 
function does a similar operation for stripped images.
.SH "RETURN VALUES"
1 is returned if the image was successfully read and converted.
Otherwise, 0 is returned if an error was encountered.
.SH DIAGNOSTICS
All error messages are directed to the
.IR TIFFError (3TIFF)
routine.
.PP
.BR "Sorry, can not handle %d-bit pictures" .
The image had
.I BitsPerSample
other than 1, 2, 4, 8, or 16.
.PP
.BR "Sorry, can not handle %d-channel images" .
The image had
.I SamplesPerPixel
other than 1, 3, or 4.
.PP
\fBMissing needed "PhotometricInterpretation" tag\fP.
The image did not have a tag that describes how to display the data.
.PP
\fBNo "PhotometricInterpretation" tag, assuming RGB\fP.
The image was missing a tag that describes how to display it, but because it
has 3 or 4 samples/pixel, it is assumed to be
.SM RGB.
.PP
\fBNo "PhotometricInterpretation" tag, assuming min-is-black\fP.
The image was missing a tag that describes how to display it,
but because it has 1 sample/pixel, it is assumed to be a grayscale
or bilevel image.
.PP
.BR "No space for photometric conversion table" .
There was insufficient memory for a table used to convert
image samples to 8-bit
.SM RGB.
.PP
\fBMissing required "Colormap" tag\fP.
A Palette image did not have a required
.I Colormap
tag.
.PP
.BR "No space for tile buffer" .
There was insufficient memory to allocate an i/o buffer.
.PP
.BR "No space for strip buffer" .
There was insufficient memory to allocate an i/o buffer.
.PP
.BR "Can not handle format" .
The image has a format (combination of
.IR BitsPerSample ,
.IR SamplesPerPixel ,
and
.IR PhotometricInterpretation )
that
.I TIFFReadRGBAImage
can not handle.
.PP
.BR "No space for B&W mapping table" .
There was insufficient memory to allocate a table used to map
grayscale data to
.SM RGB.
.PP
.BR "No space for Palette mapping table" .
There was insufficient memory to allocate a table used to map data to 8-bit
.SM RGB.
.SH "SEE ALSO"
.BR TIFFOpen (3TIFF),
.BR TIFFRGBAImage (3TIFF),
.BR TIFFReadRGBAImage (3TIFF),
.BR TIFFReadRGBAStrip (3TIFF),
.BR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
