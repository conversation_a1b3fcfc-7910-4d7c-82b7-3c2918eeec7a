# Tag Image File Format (TIFF) Software
#
# Copyright (C) 2004, <PERSON><PERSON> <<EMAIL>>
#
# Permission to use, copy, modify, distribute, and sell this software and 
# its documentation for any purpose is hereby granted without fee, provided
# that (i) the above copyright notices and this permission notice appear in
# all copies of the software and related documentation, and (ii) the names of
# <PERSON>r and Silicon Graphics may not be used in any advertising or
# publicity relating to the software without the specific, prior written
# permission of <PERSON> and Silicon Graphics.
# 
# THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
# EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
# WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
# 
# IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
# ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
# OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
# WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
# LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
# OF THIS SOFTWARE.

# Process this file with automake to produce Makefile.in.

LIBPORT = $(top_builddir)/port/libport.la
LIBTIFF = $(top_builddir)/libtiff/libtiff.la
libtiffincludedir = $(includedir)

EXTRA_DIST = \
	CMakeLists.txt \
	Makefile.vc \
	SConstruct \
	libtiff.def \
	libtiff.map \
	libtiffxx.map \
	tif_config.h-vms \
	tif_config.h.cmake.in \
	tif_config.vc.h \
	tif_config.wince.h \
	tiffconf.h.cmake.in \
	tiffconf.vc.h \
	tiffconf.wince.h

libtiffinclude_HEADERS = \
	tiff.h \
	tiffio.h \
	tiffvers.h

if HAVE_CXX
libtiffinclude_HEADERS += tiffio.hxx
endif

noinst_HEADERS = \
	t4.h \
	tif_dir.h \
	tif_predict.h \
	tiffiop.h \
	uvcode.h

nodist_libtiffinclude_HEADERS = \
	tiffconf.h

libtiff_la_SOURCES = \
	tif_aux.c \
	tif_close.c \
	tif_codec.c \
	tif_color.c \
	tif_compress.c \
	tif_dir.c \
	tif_dirinfo.c \
	tif_dirread.c \
	tif_dirwrite.c \
	tif_dumpmode.c \
	tif_error.c \
	tif_extension.c \
	tif_fax3.c \
	tif_fax3sm.c \
	tif_flush.c \
	tif_getimage.c \
	tif_jbig.c \
	tif_jpeg.c \
	tif_jpeg_12.c \
	tif_luv.c \
	tif_lzma.c \
	tif_lzw.c \
	tif_next.c \
	tif_ojpeg.c \
	tif_open.c \
	tif_packbits.c \
	tif_pixarlog.c \
	tif_predict.c \
	tif_print.c \
	tif_read.c \
	tif_strip.c \
	tif_swab.c \
	tif_thunder.c \
	tif_tile.c \
	tif_version.c \
	tif_warning.c \
	tif_webp.c \
	tif_write.c \
	tif_zip.c \
	tif_zstd.c

libtiffxx_la_SOURCES = \
	tif_stream.cxx

if WIN32_IO
EXTRA_DIST += tif_unix.c
libtiff_la_SOURCES += tif_win32.c
else
EXTRA_DIST += tif_win32.c
libtiff_la_SOURCES += tif_unix.c
endif

lib_LTLIBRARIES = libtiff.la
if HAVE_CXX
lib_LTLIBRARIES += libtiffxx.la
endif

libtiff_la_LDFLAGS = \
	-no-undefined \
	-version-info $(LIBTIFF_VERSION_INFO)
if HAVE_RPATH
libtiff_la_LDFLAGS += $(LIBDIR)
endif
if HAVE_LD_VERSION_SCRIPT
libtiff_la_LDFLAGS += -Wl,--version-script=$(srcdir)/libtiff.map
endif
if HAVE_DARWIN_EXPORTS
libtiff_la_LDFLAGS += -Wl,-exported_symbols_list,$(srcdir)/libtiff.exp
endif
libtiff_la_LIBADD = $(LIBPORT)

libtiffxx_la_LDFLAGS = \
	-no-undefined \
	-version-info $(LIBTIFF_VERSION_INFO)
if HAVE_RPATH
libtiffxx_la_LDFLAGS += $(LIBDIR)
endif
if HAVE_LD_VERSION_SCRIPT
libtiffxx_la_LDFLAGS += -Wl,--version-script=$(srcdir)/libtiffxx.map
endif
libtiffxx_la_LIBADD = $(LIBTIFF) $(LIBPORT)
libtiffxx_la_DEPENDENCIES = libtiff.la

#
# The finite state machine tables used by the G3/G4 decoders
# are generated by the mkg3states program.  On systems without
# make these rules have to be manually carried out.
#
noinst_PROGRAMS = mkg3states
mkg3states_SOURCES = mkg3states.c tif_fax3.h
mkg3states_LDADD = $(LIBPORT)

faxtable: mkg3states
	(rm -f tif_fax3sm.c && ./mkg3states -b -c const tif_fax3sm.c)

