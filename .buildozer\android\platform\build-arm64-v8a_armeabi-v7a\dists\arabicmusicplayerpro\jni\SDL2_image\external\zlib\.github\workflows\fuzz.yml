name: OSS-Fuzz
on: [pull_request]
jobs:
  Fuzzing:
    runs-on: ubuntu-latest
    steps:
    - name: Build Fuzzers
      uses: google/oss-fuzz/infra/cifuzz/actions/build_fuzzers@master
      with:
        oss-fuzz-project-name: 'zlib'
        dry-run: false

    - name: Run Fuzzers
      uses: google/oss-fuzz/infra/cifuzz/actions/run_fuzzers@master
      with:
        oss-fuzz-project-name: 'zlib'
        fuzz-seconds: 300
        dry-run: false

    - name: Upload Crash
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: artifacts
        path: ./out/artifacts
