.\"
.\" Copyright (c) 1988-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFReadEncodedStrip 3TIFF "October 15, 1995" "libtiff"
.SH NAME
TIFFReadEncodedStrip \- read and decode a strip of data from an open
.SM TIFF
file
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.BI "tmsize_t TIFFReadEncodedStrip(TIFF *" tif ", uint32 " strip ", void *" buf ", tmsize_t " size ")"
.SH DESCRIPTION
Read the specified strip of data and place up to
.I size
bytes of decompressed information in the (user supplied) data buffer.
.SH NOTES
The value of
.I strip
is a ``raw strip number.''
That is, the caller must take into account whether or not the data are
organized in separate planes (\c
.IR PlanarConfiguration =2).
To read a full strip of data the data buffer should typically be at least as
large as the number returned by
.BR TIFFStripSize (3TIFF).
If the -1 passed in
.I size
parameter, the whole strip will be read. You should be sure you have enough
space allocated for the buffer.
.PP
The library attempts to hide bit- and byte-ordering differences between the
image and the native machine by converting data to the native machine order.
Bit reversal is done if the
.I FillOrder
tag is opposite to the native machine bit order. 16- and 32-bit samples are
automatically byte-swapped if the file was written with a byte order opposite
to the native machine byte order,
.SH "RETURN VALUES"
The actual number of bytes of data that were placed in
.I buf
is returned;
.IR TIFFReadEncodedStrip
returns \-1 if an error was encountered.
.SH DIAGNOSTICS
All error messages are directed to the
.BR TIFFError (3TIFF)
routine.
.SH "SEE ALSO"
.BR TIFFOpen (3TIFF),
.BR TIFFReadRawStrip (3TIFF),
.BR TIFFReadScanline (3TIFF),
.BR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
