2ad0916d update NEWS
1287362b bump version to 1.0.3
7b968cc2 update AUTHORS
9d6988f4 Fix the oscillating prediction problem at low quality
312f74d0 makefile.unix: allow *_LIBS to be overridden w/EXTRA_LIBS
92dbf237 filters_sse2,cosmetics: shorten some long lines
a277d197 filters_sse2.c: quiet integer sanitizer warnings
804540f1 Fix cpufeatures in CMake.
bf00c15b Add CMake option for bittrace.
a788b498 filters_sse2.c: quiet integer sanitizer warnings
e6a92c5e filters.c: quiet integer sanitizer warnings
ec1cc40a lossless.c: remove U32 -> S8 conversion warnings
1106478f remove conversion U32 -> S8 warnings
812a6b49 lossless_enc: fix some conversion warning
4627c1c9 lossless_enc,TransformColorBlue: quiet uint32_t conv warning
c84673a6 lossless_enc_sse{2,41}: quiet signed conv warnings
776a7757 dec_sse2: quiet signed conv warnings
bd39c063 Merge "thread_utils: release mutex before signaling"
0550576f Merge "(alpha_processing,enc}_sse2: quiet signed conv warnings"
6682f2c4 thread_utils: release mutex before signaling
e78dea75 (alpha_processing,enc}_sse2: quiet signed conv warnings
9acf18ba iosbuild.sh: add WebP{Demux,Mux}.framework
b9be7e65 vwebp: remove the -fit option (and make it default)
1394a2bb Merge "README.webp_js: update Emscripten.cmake note"
dd3e7f8a README.webp_js: update Emscripten.cmake note
32cf8801 predictor_enc,GetBestGreenRedToBlue: quiet implicit conv warnings
e1c8acb5 Merge "vwebp: add a -fit option"
cbd23dd5 vwebp: add a -fit option
2e672351 bit_writer_utils,Flush: quiet implicit conversion warnings
1326988d swig: update libwebp_python_wrap.c
0e7f8548 update generated swig files
17ed1438 Merge "PutLE{16,24}: quiet implicit conversion warnings"
24686538 PutLE{16,24}: quiet implicit conversion warnings
153bb3a0 fix some clang-7 warnings:
ab2dc893 Rescaler: fix rounding error
aa65f89a HistogramCombineStochastic: fix free of uninit value
af0bac64 Merge "encode.h: mention 'exact' default in WebPEncodeLossless*"
6d2e11ec encode.h: mention 'exact' default in WebPEncodeLossless*
8c3f04fe AndroidCPUInfo: reorder terms in conditional
fcfd9c71 BitTrace: if BITTRACE is > 0, record and print syntax bits used
067031ea Speedups for unused Huffman groups.
01ac46ba libwebp: Display "libjpeg error:" in imageio/jpegdec
d9a662e1 WebPRescalerGetScaledDimensions: round scaled dimension up
62eb3f08 libwebp: Fix missing '{' in README
e05f785a Merge "unicode,INIT_WARGV: add missing cast"
63c9a69f tag the VP8LHashPix() function for potential uint roll-over
2b7214ab unicode,INIT_WARGV: add missing cast
bf424b46 tag the GetPixPairHash64() function for potential uint roll-over
7d05d6ca Have the color cache computation be u32-bit only.
6bcf8769 Remove BINARYEN_METHOD in wasm settings.
2b98df90 update ChangeLog (tag: v1.0.2-rc1, tag: v1.0.2)
61e372b7 update NEWS
7ae658a0 bump version to 1.0.2
51c4907d update AUTHORS
666bd6c6 man/cwebp.1: refine near-lossless text
561cdce5 Clarify the doc about GetFeatures.
aec2cf02 near_lossless: fix fuzzing-detected integer overflow
928a75de webp: Fix VP8LBitWriterClone() bug
5173d4ee neon IsFlat
5b081219 IsFlat: inline when possible
381b7b54 IsFlat: use int for thresh
6ed15ea1 fix unprobable leak in webp_sdl.c
22bbb24e Merge "IsFlat: return int"
8b3fb238 Merge tag 'v1.0.1'
f435de95 IsFlat: return int
41521aed utils.h: only define WEBP_NEED_LOG_TABLE_8BIT when needed
9f4d4a3f neon: GetResidualCost
0fd7514b neon: SetResidualCoeffs
f95a996c Simpler histogram clustering.
e85d3313 update ChangeLog (tag: v1.0.1-rc2, tag: v1.0.1)
fa8210e4 Fix pair update in stochastic entropy merging.
fd198f73 add codereview.settings
825389ac README.mux: add a reference to the AnimDecoder API
3be698c3 CMake: fix webp_js compilation
485ff86f Fix pair update in stochastic entropy merging.
4cd0582d CMake: fix webp_js compilation
4cbb4caf update NEWS
f5a5918d bump version to 1.0.1
d61385db Speed-up: Make sure we only initialize histograms when needed.
6752904b Speed-up: Make sure we only initialize histograms when needed.
0c570316 update AUTHORS
301a2dda img2webp: add help note about arguments from a file
f0abab92 Speedups for empty histograms.
f2dfd925 Split HistogramAdd to only have the high level logic in C.
06b7bc7d Fix compilation on windows and clang-cl+ninja.
b6284d82 img2webp: add help note about arguments from a file
decf6f6b Speedups for empty histograms.
dea3e899 Split HistogramAdd to only have the high level logic in C.
632798ae Merge "Fix compilation on windows and clang-cl+ninja."
dc1a9518 Merge "libwebp: Unicode command tools on Windows"
9cf9841b libwebp: Unicode command tools on Windows
98179495 remove some minor TODOs
a376e7b9 Fix compilation on windows and clang-cl+ninja.
cbf82cc0 Remove AVX2 files.
5030e902 Merge "TIFF decoder: remove unused KINV definition"
ac543311 Remove a few more useless #defines
123d3306 TIFF decoder: remove unused KINV definition
ef1094b0 Merge "- install pkg-config files during the CMake build"
b911fbc9 libwebp: Remove duplicate GIFDisplayError in anim_util
eee00b66 - install pkg-config files during the CMake build
ac3ec8c9 Merge "Clean-up the common sources in dsp."
3e13da7b Clean-up the common sources in dsp.
5c395f1d libwebp: cmake-format all
e7a69729 libwebp: Add extras targets in CMakeLists.txt
e52485d6 libwebp: Rename macros in webpmux.c
92dc0f09 clean-up MakeInputImageCopy()
39952de2 VP8IteratorImport: add missing 'const'
382af7a2 clean-up WebPBlendAlpha
14d020f6 libwebp: Use ExUtilGet*() in anim_diff
0d92ff25 libwebp: remove useless variable in gif2webp
556cb1b4 Merge "CMake: Set WEBP_BUILD_GIF2WEBP to off"
da26ee49 CMake: Set WEBP_BUILD_GIF2WEBP to off
b2a867c0 cwebp: Don't premultiply during -resize if -exact
637141bc pngdec: fix build w/libpng < 1.4.x
bc5092b1 pngdec: set memory functions
50d8345a Fix CMake math library.
6aa3e8aa Fix math library on Visual Studio.
d71df4e2 Fix math library finding in CMake.
de08d727 cosmetics: normalize include guard comment
009562b4 vwebp: Fix bug when Dispose then NoBlend frames
423f2579 Fix up CMake to create targets.
907208f9 Wait for all threads to be done in DecodeRemaining.
4649b3c4 vwebp: Add background color display option
78ad57a3 Fix bad glClearColor parameters
da96d8d9 Allow for a non-initialized alpha decompressor in DoRemap.
2563db47 fix rescaling rounding inaccuracy
211f37ee fix endian problems in pattern copy
5f0f5c07 Make sure partition #0 is read before VP8 data in IDecode.
de98732b fix GetColorf() bug
4338cd36 misc fixes in libwebpmux
e00af13e fix signatures after a9ceda7ff1
a9ceda7f Speed-up chunk list operations.
2281bbf6 Merge "Better handling of bogus Huffman codes."
39cb9aad Better handling of bogus Huffman codes.
89cc9d37 Merge "fix read-overflow while parsing VP8X chunk"
95fd6507 fix read-overflow while parsing VP8X chunk
9e729fe1 Fix VP8IoTeardownHook being called twice on worker sync failure
29fb8562 Merge "muxread,anmf: fail on multiple image chunks"
eb82ce76 muxread,anmf: fail on multiple image chunks
1344a2e9 fix alpha-filtering crash when image width is larger than radius
be738c6d muxread,ChunkVerifyAndAssign: validate chunk_size
2c70ad76 muxread,CreateInternal: fix riff size checks
569001f1 Fix for thread race heap-use-after-free
c56a02d9 Android.mk: use LOCAL_EXPORT_C_INCLUDES w/public libs
15795596 CMakeLists.txt,cosmetics: normalize if() formatting
1a44c233 Merge "cmake: add support for webpmux"
e9569ad7 Merge "configure,*am,cosmetics: s/WANT_/BUILD_/"
35c7de6f cmake: add support for webpmux
0f25e61c WebpToSDL(): fix the return value in case of error
5d8985de configure,*am,cosmetics: s/WANT_/BUILD_/
895fd28f Merge "man/Makefile.am: add img2webp.1"
5cf3e2af man/Makefile.am: add img2webp.1
2a9de5b9 Add build rules for anim_diff & anim_dump utils.
71ed73cf fix invalid check for buffer size
af0e4fbb gif2webp: fix transcode of loop count=65535
dce5d764 Limit memory allocation when reading invalid Huffman codes.
f9df0081 Merge "cmake: quiet glut deprecation warnings on OS X"
dc39b16f webpmux.1: correct grammar
c7aa1264 cwebp.c: fix a missing \n
53aa51e9 Merge tag 'v1.0.0'
698b8844 update ChangeLog (tag: v1.0.0)
8d510751 webp-container-spec: correct frame duration=0 note
e6b2164e vwebp: Copy Chrome's behavior w/frame duration == 0
094b3b28 cmake: quiet glut deprecation warnings on OS X
71c39a06 webp-container-spec: correct frame duration=0 note
fd3d5756 vwebp: Copy Chrome's behavior w/frame duration == 0
b0c966fb Build vwebp from CMake.
d20b7707 update ChangeLog (tag: v1.0.0-rc3)
0d5fad46 add WEBP_DSP_INIT / WEBP_DSP_INIT_FUNC
d77bf512 add WEBP_DSP_INIT / WEBP_DSP_INIT_FUNC
c1cb86af fix 16b overflow in SSE2
e577feb7 makefile.unix: add DEBUG flag for compiling w/ debug-symbol
99be34b3 cwebp,get_disto: fix bpp output
e122e511 cwebp,get_disto: fix bpp output
f5565ca8 cmake: Make sure we use near-lossless by default.
d898dc14 fix bug in WebPImport565: alpha value was not set
1c8f358d Fix CMake with WASM.
a0215fb7 webp_js: fix webp_js demo html
882784b0 update ChangeLog (tag: v1.0.0-rc2)
2f930e08 Revert "Use proper targets for CMake."
8165e8fb Use proper targets for CMake.
3f157dd5 Remove some very hard TODOs.
abb47760 Merge "Use proper targets for CMake."
cd758a17 {de,}mux/Makefile.am: add missing headers
e155dda0 Use proper targets for CMake.
b892b8ba makefile.unix,dist: use ascii for text output
64a57d05 add -version option to anim_dump,anim_diff and img2webp
994be82d Merge "Remove some very hard TODOs."
4033e1d7 Remove some very hard TODOs.
fc1b8e3a webp_js: fix webp_js demo html
15aa48d9 update ChangeLog (tag: v1.0.0-rc1)
e607dabc update AUTHORS
38410c08 [CFI] Remove function pointer casts
978eec25 [CFI] Remove function pointer casts
c57b2736 bump version to 1.0.0
cba28853 update NEWS
c909d531 Merge "remove some deprecation warning on MacOSX"
217443c7 remove some deprecation warning on MacOSX
b672bdfa configure: quiet glut deprecation warnings on OS X
daa9fcaf configure: use sdl-config if available
dd174cae Merge "imagedec: support metadata reading for WebP image decoding"
641cedcc imagedec: support metadata reading for WebP image decoding
065b2ce1 anim_diff: add a couple missing newlines in Help()
c4cc1147 Merge "gif2webp: force low duration frames to 100ms"
09333097 gif2webp: force low duration frames to 100ms
e03f0ec3 sharp_yuv: use 14b fixed-point precision for gamma
b2db361c image_enc,WebPWritePNG: move locals after setjmp
74e82ec6 Merge "WebPPictureDistortion: fix big-endian results order"
645d04ca Merge "cwebp,get_disto: report bpp"
120f58c3 Merge "lossless*sse2: improve non-const 16-bit vector creation"
a7fe9412 WebPPictureDistortion: fix big-endian results order
e26fe066 cwebp,get_disto: report bpp
9df64e28 Merge changes Id5b4a1a4,Ia20ce844
8043504f lossless*sse2: improve non-const 16-bit vector creation
1e3dfc48 Import: extract condition from loop
3b07d327 Import,RGBA: fix for BigEndian import
551948e4 Remove unused argument in VP8LBitsEntropy.
3005237a ReadWebP: fix for big-endian
499c395a Merge "anim_diff: expose the -max_diff option"
f69dcd69 Merge "remove WEBP_EXPERIMENTAL_FEATURES"
07d884d5 anim_diff: expose the -max_diff option
f4dd9256 remove WEBP_EXPERIMENTAL_FEATURES
94a8377b extract the command-line parsing helpers to example_util
fc09e6e2 PNM decoder: prevent unsupported depth=2 PAM case.
6de58603 MIPS64: Fix defined-but-not-used errors with WEBP_REDUCE_CSP
cbde5728 gif2webp: add support for reading from stdin
cf1c5054 Add an SSE4 version of some lossless color transforms.
45a8b5eb Fix lint error with man page.
cff38e8f Merge "PNG decoder: handle gAMA chunk"
59cb1a48 Merge "enable dc error-diffusion always"
78318b30 PNG decoder: handle gAMA chunk
664c21dd Merge "remove some TODOs"
815652de enable dc error-diffusion always
aec45cec remove some TODOs
5715dfce fix block-count[] increment in case of large image
c2d04f3e enable DC error-diffusion always for multi-pass
96bf07c5 use DC error diffusion for U/V at low-quality
1c59020b fix missing sse41 targets in makefile.unix
7a8e814b cosmetics: s/color_space/colorspace/
05f6fe24 upsampling: rm asserts w/REDUCE_CSP+OMIT_C_CODE
b4cf5597 Merge "Upsampling SSE2/SSE4 speedup."
ccbeb32c Makefile.vc: add missing sse41 files
55403a9a Upsampling SSE2/SSE4 speedup.
807b53c4 Implement the upsampling/yuv functions in SSE41
84101a81 Fix wasm WebP compilation
8bebd2a3 fix warning on MSVC
a7f93fe3 webpmux: allow reading argument from a file
b69f18a7 gif2webp.1: fix -loop_compatibility layout
72d530c0 Merge "fix lossless decoding w/WEBP_REDUCE_SIZE"
296c7dc4 fix lossless decoding w/WEBP_REDUCE_SIZE
0d5d029c Merge "ImgIoUtilReadFile: fix file leak upon error"
ae568ce7 ImgIoUtilReadFile: fix file leak upon error
796b5a8a Merge tag 'v0.6.1'
6b7a95fd update ChangeLog (tag: v0.6.1)
f66955de WEBP_REDUCE_CSP: restrict colorspace support
1af0df76 Merge "WEBP_REDUCE_CSP: restrict colorspace support"
6de20df0 WEBP_REDUCE_CSP: restrict colorspace support
a289d8e7 update ChangeLog (tag: v0.6.1-rc2)
c10a493c vwebp: disable double buffering on windows & mac
0d4466c2 webp_to_sdl.c: fix file mode
1b27bf8b WEBP_REDUCE_SIZE: disable all rescaler code
126be109 webpinfo: add -version option
0df22b9e WEBP_REDUCE_SIZE: disable all rescaler code
9add62b5 bump version to 0.6.1
d3e26144 update NEWS
2edda639 README: add webpinfo section
9ca568ef Merge "right-size some tables"
31f1995c Merge "SSE2 implementation of HasAlphaXXX"
a80c46bd SSE2 implementation of HasAlphaXXX
083507f2 right-size some tables
2e5785b2 anim_utils.c: remove warning when !defined(WEBP_HAVE_GIF)
b299c47e add WEBP_REDUCE_SIZE
f593d71a enc: disable pic->stats/extra_info w/WEBP_DISABLE_STATS
541179a9 Merge "predictor_enc: fix build w/--disable-near-lossless"
5755a7ec predictor_enc: fix build w/--disable-near-lossless
eab5bab7 add WEBP_DISABLE_STATS
8052c585 remove some petty TODOs from vwebp.
c245343d move LOAD8x4 and STORE8x2 closer to their use location
b9e734fd dec,cosmetics: normalize function naming style
c188d546 dec: harmonize function suffixes
28c5ac81 dec_sse41: harmonize function suffixes
e65b72a3 Merge "introduce WebPHasAlpha8b and WebPHasAlpha32b"
b94cee98 dec_sse2: remove HE8uv_SSE2
44a0ee3f introduce WebPHasAlpha8b and WebPHasAlpha32b
aebf59ac Merge "WebPPictureAllocARGB: align argb allocation"
c184665e WebPPictureAllocARGB: align argb allocation
3daf7509 WebPParseHeaders: remove obsolete animation TODO
80285d97 cmake: avoid security warnings under msvc
650eac55 cmake: don't set -Wall with MSVC
c462cd00 Remove useless code.
01a98217 Merge "remove WebPWorkerImpl declaration from the header"
3c49fc47 Merge "thread_utils: fix potentially bad call to Execute"
fde2782e thread_utils: fix potentially bad call to Execute
2a270c1d remove WebPWorkerImpl declaration from the header
f1f437cc remove mention of 'lossy-only parameters' from the doc
3879074d Merge "WebPMemToUint32: remove ptr cast to int"
04b029d2 WebPMemToUint32: remove ptr cast to int
b7971d0e dsp: avoid defining _C functions w/NEON builds
6ba98764 webpdec: correct alloc size check w/use_argb
5cfb3b0f normalize include guards
f433205e Merge changes Ia17c7dfc,I75423abb,Ia2f716b4,I161caa14,I4210081a, ...
8d033b14 {dec,enc}_neon: harmonize function suffixes x2
0295e981 upsampling_neon: harmonize function suffixes
d572c4e5 yuv_neon: harmonize function suffixes
ab9c2500 rescaler_neon: harmonize function suffixes
93e0ce27 lossless_neon: harmonize function suffixes
22fbc50e lossless_enc_neon: harmonize function suffixes
447875b4 filters_neon,cosmetics: fix indent
e51bdd43 remove unused VP8TokenToStats() function
785da7ea enc_neon: harmonize function suffixes
bc1a251f dec_neon: harmonize function suffixes
61e535f1 dsp/lossless: workaround gcc-4.8 bug on arm
68b2eab7 cwebp: fix alpha reporting w/lossless & metadata
30042faa WebPDemuxGetI: add doc details around WebPFormatFeature
0a17f471 Merge "WIP: list includes as descendants of the project dir"
a4399721 WIP: list includes as descendants of the project dir
08275708 Merge "Make sure we reach the full range for alpha blending."
d361a6a7 yuv_sse2: harmonize function suffixes
6921aa6f upsampling_sse2: harmonize function suffixes
08c67d3e ssim_sse2: harmonize function suffixes
582a1b57 rescaler_sse2: harmonize function suffixes
2c1b18ba lossless_sse2: harmonize function suffixes
0ac46e81 lossless_enc_sse2: harmonize function suffixes
bc634d57 enc_sse2: harmonize function suffixes
bcb7347c dec_sse2: harmonize function suffixes
e14ad93c Make sure we reach the full range for alpha blending.
7038ca8d demux,StoreFrame: restore hdr size check to min req
fb3daad6 cpu: fix ssse3 check
be590e06 Merge "Fix CMake redefinition for HAVE_CPU_FEATURES_H"
35f736e1 Fix CMake redefinition for HAVE_CPU_FEATURES_H
a5216efc Fix integer overflow warning.
a9c8916b decode.h,WebPIDecGetRGB: clarify output ptr validity
3c74c645 gif2webp: handle 1-frame case properly + fix anim_diff
c7f295d3 Merge "gif2webp: introduce -loop_compatibility option"
b4e04677 gif2webp: introduce -loop_compatibility option
f78da3de add LOCAL_CLANG_PREREQ and avoid WORK_AROUND_GCC w/3.8+
01c426f1 define WEBP_USE_INTRINSICS w/gcc-4.9+
8635973d use sdl-config (if available) to determine the link flags
e9459382 use CPPFLAGS before CFLAGS
4a9d788e Merge "Android.mk,mips: fix clang build with r15"
4fbdc9fb Android.mk,mips: fix clang build with r15
a80fcc4a ifdef code not used by Chrome/Android.
3993af12 Fix signed integer overflows.
f66f94ef anim_dump: small tool to dump frames from animated WebP
6eba857b Merge "rationalize the Makefile.am"
c5e34fba function definition cleanup
3822762a rationalize the Makefile.am
501ef6e4 configure style fix: animdiff -> anim_diff
f8bdc268 Merge "protect against NULL dump_folder[] value in ReadAnimatedImage()"
23bfc652 protect against NULL dump_folder[] value in ReadAnimatedImage()
8dc3d71b cosmetics,ReadAnimatedWebP: correct function comment
5bd40066 Merge changes I66a64a0a,I4d2e520f
7945575c cosmetics,webpinfo: remove an else after a return
8729fa11 cosmetics,cwebp: remove an else after a return
f324b7f9 cosmetics: normalize fn proto & decl param names
869eb369 CMake cleanups.
289e62a3 Remove declaration of unimplemented VP8ApplyNearLosslessPredict
20a94186 pnmdec,PAM: validate depth before calculating bytes_per_px
34130afe anim_encode: fix integer overflow
42c79aa6 Merge "Encoder: harmonize function suffixes"
b09307dc Encoder: harmonize function suffixes
bed0456d Merge "SSIM: harmonize the function suffix"
54f6a3cf lossless_sse2.c: fix some missed suffix changes
088f1dcc SSIM: harmonize the function suffix
86fc4dd9 webpdec: use ImgIoUtilCheckSizeArgumentsOverflow
08ea9ecd imageio: add ability restrict max image size
6f9daa4a jpegdec,ReadError: fix leaks on error
a0f72a4f VP8LTransformColorFunc: drop an non-respected 'const' from the signature.
8c934902 Merge "Lossess dec: harmonize the function suffixes"
622242aa Lossess dec: harmonize the function suffixes
1411f027 Lossless Enc: harmonize the function suffixes
24ad2e3c add const to two variables
46efe062 Merge "Allow the lossless cruncher to work for alpha."
8c3f9a47 Speed-up LZ77.
1aef4c71 Allow the lossless cruncher to work for alpha.
b8821dbd Improve the box LZ77 speed.
7beed280 add missing ()s to macro parameters
6473d20b Merge "fix Android standalone toolchain build"
dcefed95 Merge "build.gradle: fix arm64 build"
0c83a8bc Merge "yuv: harmonize suffix naming"
c6d1db4b fix Android standalone toolchain build
663a6d9d unify the ALTERNATE_CODE flag usage
73ea9f27 yuv: harmonize suffix naming
c71b68ac build.gradle: fix arm64 build
c4568b47 Rescaler: harmonize the suffix naming
6cb13b05 Merge "alpha_processing: harmonize the naming suffixes to be _C()"
83a3e69a Merge "simplify WEBP_EXTERN macro"
7295fde2 Merge "filters: harmonize the suffixes naming to _SSE2(), _C(), etc."
8e42ba4c simplify WEBP_EXTERN macro
331ab34b cost*.c: harmonize the suffix namings
b161f670 filters: harmonize the suffixes naming to _SSE2(), _C(), etc.
dec5e4d3 alpha_processing: harmonize the naming suffixes to be _C()
6878d427 fix memory leak in SDL_Init()
461ae555 Merge "configure: fix warnings in sdl check"
62486a22 configure: test for -Wundef
92982609 dsp.h: fix -Wundef w/__mips_dsp_rev
0265cede configure: fix warnings in sdl check
88c73d8a backward_references_enc.h: fix WINDOW_SIZE_BITS check
4ea49f6b rescaler_sse2.c: fix WEBP_RESCALER_FIX -> _RFIX typo
1b526638 Clean-up some CMake
87f57a4b Merge "cmake: fix gif lib detection when cross compiling"
b34a9db1 cosmetics,dec_sse2: remove some redundant comments
471c5755 cmake: fix gif lib detection when cross compiling
c793417a cmake: disable gif2webp if gif lib isn't found
dcbc1c88 cmake: split gif detection from IMG deps
66ad84f0 Merge "muxread: remove unreachable code"
50ec3ab7 muxread: remove unreachable code
7d67a164 Lossy encoding: smoothen transparent areas to improve compression
e50650c7 Merge "fix signature for DISABLE_TOKEN_BUFFER compilation"
671d2567 fix signature for DISABLE_TOKEN_BUFFER compilation
d6755580 cpu.cmake: use unique flag to test simd disable flags
28914528 Merge "Remove the argb* files."
8acb4942 Remove the argb* files.
3b62347b README: correct cmake invocation note
7ca0df13 Have the SSE2 version of PackARGB use common code.
7b250459 Merge "Re-use the transformed image when trying several LZ77 in lossless."
e132072f Re-use the transformed image when trying several LZ77 in lossless.
5d7a50ef Get code to compile in C++.
7b012987 configure: test for -Wparentheses-equality
f0569adb Fix man pages for multi-threading.
f1d5a397 multithread cruncher: only copy stats when picture->stats != NULL
f8c2ac15 Multi-thread the lossless cruncher.
a88c6522 Merge "Integrate a new LZ77 looking for matches in the neighborhood of a pixel only."
8f6df1d0 Unroll Predictors 10, 11 and 12.
355c3d1b Integrate a new LZ77 looking for matches in the neighborhood of a pixel only.
a1779a01 Refactor LZ77 handling in preparation for a new method.
67de68b5 Android.mk/build.gradle: fix mips build with clang from r14b
f209a548 Use the plane code and not the distance when computing statistics.
b903b80c Split cost-based backward references in its own file.
498cad34 Cosmetic changes in backward reference.
e4eb4587 lossless, VP8LTransformColor_C: make sure no overflow happens with colors.
af6deaff webpinfo: handle alpha flag mismatch
7caef29b Fix typo that creeped in.
39e19f92 Merge "near lossless: fix unsigned int overflow warnings."
9bbc0891 near lossless: fix unsigned int overflow warnings.
e1118d62 Merge "cosmetics,FindClosestDiscretized: use uint in mask creation"
186bc9b7 Merge "webpinfo: tolerate ALPH+VP8L"
b5887297 cosmetics,FindClosestDiscretized: use uint in mask creation
f1784aee near_lossless,FindClosestDiscretized: use unsigned ops
0d20abb3 webpinfo: tolerate ALPH+VP8L
972104b3 webpmux: tolerate false positive Alpha flag
dd7e83cc tiffdec,ReadTIFF: ensure data_size is < tsize_t max
d988eb7b tiffdec,MyRead: quiet -Wshorten-64-to-32 warning
dabda707 webpinfo: add support to parse Alpha bitstream
4c117643 webpinfo: correct background color output, BGRA->ARGB
defc98d7 Doc: clarify the role of quality in WebPConfig.
d78ff780 Merge "Fix code to compile with C++."
c8f14093 Fix code to compile with C++.
497dc6a7 pnmdec: sanitize invalid header output
d78e5867 Merge "configure: test for -Wconstant-conversion"
481e91eb Merge "pnmdec,PAM: set bytes_per_px based on depth when missing"
93b12753 configure: test for -Wconstant-conversion
645f0c53 pnmdec,PAM: set bytes_per_px based on depth when missing
e9154605 Merge "vwebp: activate GLUT double-buffering"
818d795b vwebp: activate GLUT double-buffering
d63e6f4b Add a man page for webpinfo
4d708435 Merge "NEON: implement ConvertRGB24ToY/BGR24/ARGB/RGBA32ToUV/ARGBToUV"
faf42213 NEON: implement ConvertRGB24ToY/BGR24/ARGB/RGBA32ToUV/ARGBToUV
b4d576fa Install man pages with CMake.
cbc1b921 webpinfo: add features to parse bitstream header
e644c556 Fix bad bit writer initialization.
b62cdad2 Merge "Implement a cruncher for lossless at method 6."
da3e4dfb use the exact constant for the gamma transfer function
a9c701e0 Merge "tiffdec: fix EXTRASAMPLES check"
adab8ce0 Implement a cruncher for lossless at method 6.
1b92b237 Merge "Fix VP8ApplyNearLossless to respect const and stride."
1923ff02 tiffdec: fix EXTRASAMPLES check
97cce5ba tiffdec: only request EXTRASAMPLES w/> 3 samples/px
0dcd85b6 Fix VP8ApplyNearLossless to respect const and stride.
f7682189 yuv: rationalize the C/SSE2 function naming
52245424 NEON implementation of some Sharp-YUV420 functions
690efd82 Avoid several backward reference copies.
4bb1f607 src/dec/vp8_dec.h, cosmetics: fix comments
285748be cmake: build/install webpinfo
78fd199c backward_references_enc.c: clear -Wshadow warnings
ae836410 WebPLog2FloorC: clear -Wshadow warning
d0b7404e Merge "WASM support"
134e314f WASM support
c08adb6f Merge "VP8LEnc: remove use of BitsLog2Ceiling()"
28c37ebd VP8LEnc: remove use of BitsLog2Ceiling()
2cb58ab2 webpinfo: output format as a human readable string
bb175a93 Merge "rename some symbols clashing with MSVC headers"
39eda658 Remove a duplicated pixel hash implementation.
36b8274d rename some symbols clashing with MSVC headers
274daf54 Add webpinfo tool.
ec5036e4 add explicit reference to /usr/local/{lib,inc}
18f0dfac Merge "fix TIFF encoder regarding rgbA/RGBA"
4e2b0b50 Merge "webpdec.h: fix a doc typo"
e2eeabff Merge "Install binaries, libraries and headers in CMake."
836607e6 webpdec.h: fix a doc typo
9273e441 fix TIFF encoder regarding rgbA/RGBA
17e3c11f Add limited PAM decoding support
5f624871 Install binaries, libraries and headers in CMake.
976adac1 Merge "lossless incremental decoding: fix missing eos_ test"
f8fad4fa lossless incremental decoding: fix missing eos_ test
27415d41 Merge "vwebp_sdl: fix the makefile.unix"
49566182 Merge "ImgIoUtilWriteFile(): use ImgIoUtilSetBinaryMode"
6f75a51b Analyze the transform entropy on the whole image.
a5e4e3af Use palette only if we can in entropy analysis.
75a9c3c4 Improve compression by better entropy analysis.
39cf6f4f vwebp_sdl: fix the makefile.unix
699b0416 ImgIoUtilWriteFile(): use ImgIoUtilSetBinaryMode
7d985bd1 Fix small entropy analysis bug.
6e7caf06 Optimize the color cache size.
833c9219 More efficient stochastic histogram merge.
5183326b Refactor the greedy histogram merge.
99f6f462 Merge "histogram_enc.c,MyRand: s/ul/u/ for unsigned constants"
80a22186 ssim.c: remove dead include
a128dfff histogram_enc.c,MyRand: s/ul/u/ for unsigned constants
693bf74e move the SSIM calculation code in ssim.c / ssim_sse2.c
10d791ca Merge "Fix the random generator in HistogramCombineStochastic."
fa63a966 Fix the random generator in HistogramCombineStochastic.
16be192f VP8LSetBitPos: remove the eos_ setting
027151ca don't erase the surface before blitting.
4105d565 disable WEBP_USE_XXX optimisations when EMSCRIPTEN is defined
9ee32a75 Merge "WebP-JS: emscripten-based Javascript decoder"
ca9f7b7d WebP-JS: emscripten-based Javascript decoder
868aa690 Perform greedy histogram merge in a unified way.
5b393f2d Merge "fix path typo for vwebp_sdl in Makefile.vc"
e0012bea CMake: only use libwebpdecoder for building dwebp
84c2a7b0 fix path typo for vwebp_sdl in Makefile.vc
1b0e4abf Merge "Add a flag to disable SIMD optimizations."
32263250 Add a flag to disable SIMD optimizations.
b494fdec optimize the ARGB->ARGB Import to use memcpy
f1536039 Merge "ReadWebP: decode directly into a pre-allocated buffer"
e69ed291 ReadWebP: decode directly into a pre-allocated buffer
57d8de8a Merge "vwebp_sdl: simple viewer based on SDL"
5cfd4ebc LZ77 interval speedups. Faster, smaller, simpler.
1e7ad88b PNM header decoder: add some basic numerical validation
17c7890c Merge "Add a decoder only library for WebP in CMake."
be733786 Merge "Add clang build fix for MSA"
03cda0e4 Add a decoder only library for WebP in CMake.
aa893914 Add clang build fix for MSA
31a92e97 Merge "imageio: add limited PNM support for reading"
dcf9d82a imageio: add limited PNM support for reading
6524fcd6 vwebp_sdl: simple viewer based on SDL
6cf24a24 get_disto: fix reference file read
43d472aa Merge tag 'v0.6.0'
50d1a848 update ChangeLog (tag: v0.6.0, origin/0.6.0)
20a7fea0 extras/Makefile.am: fix libwebpextras.la reference
415f3ffe update ChangeLog (tag: v0.6.0-rc3)
3c6d1224 update NEWS
ee4a4141 update AUTHORS
32ed856f Fix "all|no frames are keyframes" settings.
1c3190b6 Merge "Fix "all|no frames are keyframes" settings."
f4dc56fd disable GradientUnfilter_NEON
4f3e3bbd disable GradientUnfilter_NEON
2dc0bdca Fix "all|no frames are keyframes" settings.
0d8e0588 img2webp: treat -loop as a no-op w/single images
b0450139 ReadImage(): restore size reporting
0ad3b4ef update ChangeLog (tag: v0.6.0-rc2)
6451709e img2webp,get_disto: fix image decode w/WIC builds
92504d21 get_disto: make ReadPicture() return a bool
c3e4b3a9 update NEWS
3363eb6d man/img2webp.1: fix formatting warning
4d1312f2 update NEWS
36c42ea4 bump version to 0.6.0
bb498a51 update AUTHORS
84cef16f Makefile.vc: fix CFG=debug-dynamic build
919f9e2f Merge "add .rc files for windows dll versioning"
f1ae8af4 Merge ".gitignore: add img2webp"
4689ce16 cwebp: add a -sharp_yuv option for 'sharp' RGB->YUV conversion
79bf46f1 rename the pretentious SmartYUV into SharpYUV
eb1dc89a silently expose use_delta_palette in the WebPConfig API
c85b0dde .gitignore: add img2webp
43d3f01a add .rc files for windows dll versioning
668e1dd4 src/{dec,enc,utils}: give filenames a unique suffix
0e6b7f33 Merge "iosbuild.sh: only add required headers to framework"
29ed6f9a iosbuild.sh: only add required headers to framework
71c53f1a NEON: speed-up strong filtering
73f567ea Merge "get_disto: remove redundant reader check"
9e14276f Merge "makefiles: prune get_disto & webp_quality deps"
99965bac Merge "Makefile.vc: add get_disto.exe, webp_quality.exe"
d4912238 get_disto: remove redundant reader check
ea482409 makefiles: prune get_disto & webp_quality deps
2ede5a19 Makefile.vc: add get_disto.exe, webp_quality.exe
a345068a ARM: speed up bitreader by avoiding tables
1dc82a6b Merge "introduce a generic GetCoeffs() function pointer"
8074b89e introduce a generic GetCoeffs() function pointer
749a45a5 Merge "NEON: implement alpha-filters (horizontal/vertical/gradient)"
74c053b5 Merge "NEON: fix overflow in SSE NxN calculation"
0a3aeff7 Merge "dsp: WebPExtractGreen function for alpha decompression"
1de931c6 NEON: implement alpha-filters (horizontal/vertical/gradient)
9b3aca40 NEON: fix overflow in SSE NxN calculation
1c07a3c6 dsp: WebPExtractGreen function for alpha decompression
9ed5e3e5 use pointers for WebPRescaler's in WebPDecParams
db013a8d Merge "ARM: don't use USE_GENERIC_TREE"
fcd4784d use a 8b table for C-version for clz()
fbb5c473 ARM: don't use USE_GENERIC_TREE
8fda5612 Merge "add a kSlowSSSE3 feature for CPUInfo"
86bbd245 add a kSlowSSSE3 feature for CPUInfo
7c2779e9 Get code to fully compile in C++.
250c3586 Merge "When compiling as C++, avoid narrowing warnings."
c0648ac2 When compiling as C++, avoid narrowing warnings.
0d55f60c 40% faster ApplyAlphaMultiply_SSE2
49d0280d NEON: implement several alpha-processing functions
48b1e85f SSE2: 15% faster alpha-processing functions
e3b8abbc fix warning from static analysis.
28fe054e SSE2: 30% faster ApplyAlphaMultiply()
f44acd25 Merge "Properly compute the optimal color cache size."
527844fe Properly compute the optimal color cache size.
be0ef639 fix a comment typo
8874b162 Fix a non-deterministic color cache size computation.
d712e20d Do not allow a color cache size bigger than the number of colors.
ecff04f6 re-introduce some comments in Huffman Cost.
259e9828 replace 'ptr + y * stride' by 'ptr += stride'
00b08c88 Merge "NEON: 5% faster conversion to RGB565 and RGBA4444"
0e7f4447 Merge "NEON: faster fancy upsampling"
b016cb91 NEON: faster fancy upsampling
1cb63801 Call the C function to finish off lossless SSE loops only when necessary.
875fafc1 Implement BundleColorMap in SSE2.
3674d49e Merge "remove Clang warnings with unused arch arguments."
f04eb376 Merge tag 'v0.5.2'
341d711c NEON: 5% faster conversion to RGB565 and RGBA4444
abb54827 remove Clang warnings with unused arch arguments.
ece9684f update ChangeLog (tag: v0.5.2-rc2, tag: v0.5.2, origin/0.5.2)
aa7744ca anim_util: quiet implicit conv warnings in 32-bit
d9120271 jpegdec: correct ContextFill signature
24eb3940 Remove some errors when compiling the code as C++.
a4a8e5f3 vwebp: clear canvas during resize w/o animation
67c25ad5 vwebp: clear canvas during resize w/o animation
a4bbe4b3 fix indentation
31ca2a80 tiffdec: restore libtiff 3.9.x compatibility
b2f77b57 update NEWS
5ab6d9de AnimEncoder: avoid freeing uninitialized memory pointer.
f29bf582 WebPAnimEncoder: If 'minimize_size' and 'allow_mixed' on, try lossy + lossless.
3ebe1c00 AnimEncoder: avoid freeing uninitialized memory pointer.
df780e0e fix a potential overflow with MALLOC_LIMIT
58fc5078 Merge "PredictorSub: implement fully-SSE2 version"
9cc42167 PredictorSub: implement fully-SSE2 version
0aa1f35c remove dependency of imageio/ to stopwatch.h
cb9ec84b Merge "remove the dependency to stop_watch.[ch] in imageio"
dc0c01fb Merge "anim_util: quiet implicit conv warnings in 32-bit"
827d3c50 Merge "fix a potential overflow with MALLOC_LIMIT"
1e2e25b0 anim_util: quiet implicit conv warnings in 32-bit
218460cd bump version to 0.5.2
de7d654d update AUTHORS & .mailmap
273367c1 Merge "dsp/lossless.c,cosmetics: fix indent"
76bbcf2e fix a potential overflow with MALLOC_LIMIT
8ac1abfe Merge "jpegdec: correct ContextFill signature"
cb215aed remove the dependency to stop_watch.[ch] in imageio
2423017a dsp/lossless.c,cosmetics: fix indent
74a12b10 iosbuild.sh: add WebPDecoder.framework + encoder
a9cc7621 Merge "iosbuild.sh: add WebPDecoder.framework + encoder"
fbba5bc2 optimize predictor #1 in plain-C For some reason, gcc has hard time inlining this one...
9ae0b3f6 Merge "SSE2: slightly (~2%) faster Predictor #1"
c1f97bd7 SSE2: slightly (~2%) faster Predictor #1
ea664b89 SSE2: 10% faster Predictor #11
be7dcc08 AnimEncoder: Correctly skip a frame when sub-rectangle is empty.
40885830 Fix assertions in WebPRescalerExportRow()
1d5046d1 iosbuild.sh: add WebPDecoder.framework + encoder
cec72014 jpegdec: correct ContextFill signature
8f38c72e fix a typo in WebPPictureYUVAToARGB's doc
33ca93f9 systematically call WebPDemuxReleaseIterator() on dec->prev_iter_
76e19073 doc: use two's complement explicitly for uint8->int8 conversion
f91ba963 Anim_encoder: correctly handle enc->prev_candidate_undecided_
25d74e65 WebPPictureDistortion(): free() -> WebPSafeFree()
03f1c008 mux/Makefile.am: add missing -lm
58410cd6 fix bug in RefineUsingDistortion()
e168af8c fix filtering auto-adjustment
ed9dec41 fix doc and code snippet for WebPINewDecoder() doc
3c49178f prevent 32b overflow for very large canvas_width / height
9595f290 fix anim_util.c compilation when HAVE_GIF is not defined.
7ec9552c Make gif transparent color to be transparent black
b3fb8bb6 slightly faster Predictor #11 in NEON
9871335f Add a CMake option for WEBP_SWAP_16BIT_CSP.
0ae32226 Fix missing cpu-features for Android.
ab4c8056 cpu.cmake: improve webp_check_compiler_flag output
eec5fa3a Provide support for CMake on Android studio 2.2.
004d5690 Split the main CMake file.
4fe5d588 Android.mk: use -fvisibility=hidden
bd63a31a vwebp: ensure setenv() is available in stdlib.h
363a5681 vwebp: handle window resizing properly
a0d2753f lower WEBP_MAX_ALLOCABLE_MEMORY default
31fe11a5  fix infinite loop in case of PARTITION0 overflow
532215dd Change the rule of picking UV mode in MBAnalyzeBestUVMode()
9c75dbd3 cwebp.1: improve some grammar
af2e05cb vwebp: Clear previous frame when a key triggers a redraw
26ffa296 Add descriptions of default configuration in help info.
7416280d Fix an unsigned integer overflow error in enc/cost.h
13cf1d2e Do token recording and counting in a single loop
eb9a4b97 Reset segment id if we decide not to update segment map
42ebe3b7 configure: fix NEON flag detection under gcc 6
76ebbfff NEON: implement predictor #13
95b12a08 Merge "Revert Average3 and Average4"
54ab2e75 Revert Average3 and Average4
fe12330c 3-5% faster Predictor #5, #6, #7 and #10 for NEON
fbfb3bef ~2% faster predictor #10 for NEON
d4b7d801 lossless_sse2: use the local functions
a5e3b225 Lossless decoder SSE2 improvements.
58a1f124 ~2% faster predictor #12 in NEON.
906c3b63 Merge "Implement lossless transforms in NEON."
d23abe4e Implement lossless transforms in NEON.
2e6cb6f3 Give more flexibility to the predictor generating macro.
28e0bb70 Merge "Fix race condition in multi-threading initialization."
64704530 Fix race condition in multi-threading initialization.
bded7848 img2webp: fix default -lossless value and use pic.argb=1
0e61a513 Merge "img2webp: convert a sequence of images to an animated webp"
1cc79e92 AnimEncoder: Correctly skip a frame when sub-rectangle is empty.
03f40955 img2webp: convert a sequence of images to an animated webp
ea72cd60 add missing 'extern' keyword for predictor dcl
67879e6d SSE implementation of decoding predictors.
34aee990 Merge "vwebp: make 'd' key toggle the debugging of fragments"
a41296ae Fix potentially uninitialized value.
c85adb33 vwebp: make 'd' key toggle the debugging of fragments
4239a148 Make the lossless predictors work on a batch of pixels.
bc18ebad fix extra 'const's in signatures
71e2f5ca Remove memcpy in lossless decoding.
7474d46e Do not use a register array in SSE.
67748b41 Improve latency of FTransform2.
16951b19 Merge "Provide an SSE implementation of ConvertBGRAToRGB"
6540cd0e Provide an SSE implementation of ConvertBGRAToRGB
de568abf Android.mk: use -fvisibility=hidden
3c2a61b0 remove some unneeded casts
9ac063c3 add dsp functions for SmartYUV
22efabdd Merge "smart_yuv: switch to planar instead of packed r/g/b processing"
1d6e7bf3 smart_yuv: switch to planar instead of packed r/g/b processing
0a3838ca fix bug in RefineUsingDistortion()
c0699515 webpmux -duration: set default 'end' value equal to 'start'
83cbfa09 Import: use relative pointer offsets
a1ade40e PreprocessARGB: use relative pointer offsets
fd4d090f ConvertWRGBToYUV: use relative pointer offsets
9daad459 ImportYUVAFromRGBA: use relative pointer offsets
f90c60d1 Merge "add a "-duration duration,start,end" option to webpmux"
3f182d36 add a "-duration duration,start,end" option to webpmux
342e15f0 Import: use relative pointer offsets
1147ab4e PreprocessARGB: use relative pointer offsets
e4cd4daf fix filtering auto-adjustment
e7152856 fix doc and code snippet for WebPINewDecoder() doc
de9fa507 ConvertWRGBToYUV: use relative pointer offsets
deb1b831 ImportYUVAFromRGBA: use relative pointer offsets
c284780f imageio_util: add ImgIoUtilCheckSizeArgumentsOverflow
e375080d gifdec,Remap: avoid out of bounds colormap read
c222a053 additional fix for stride type as size_t
bb233617 fix potential overflow when width * height * 4 >= (1<<32)
883d41fb gif2webp: fix crash with NULL extension data
cac9a36a gifdec,Remap: avoid out of bounds colormap read
4595e01f Revert "gifdec,Remap: avoid out of bounds colormap read"
fb52d443 gifdec: make some constants unsigned
f048d38d gifdec,Remap: avoid out of bounds colormap read
31b1e343 fix SSIM metric ... by ignoring too-dark area
2f51b614 introduce WebPPlaneDistortion to compute plane distortion
0104d730 configure: fix NEON flag detection under gcc 6
265abbe9 Merge "additional fix for stride type as size_t"
f7601aa6 Merge "Introduce a generic WebPGetImageReader(type) function"
ce873320 Introduce a generic WebPGetImageReader(type) function
2a2773ea imageio/*dec,Read*: add input parameter checks
9f5c8eca additional fix for stride type as size_t
4eb5df28 remove unused stride fields from VP8Iterator
11bc423a MIN_LENGTH cleanups.
273d035a Merge "fix a typo in WebPPictureYUVAToARGB's doc"
4db82a17 Merge "fix potential overflow when width * height * 4 >= (1<<32)"
e2affacc fix potential overflow when width * height * 4 >= (1<<32)
dc789ada fix a typo in WebPPictureYUVAToARGB's doc
539f5a68 Fix non-included header in config.c.
aaf2a6a6 systematically call WebPDemuxReleaseIterator() on dec->prev_iter_
20ef9915 Merge "imageio_util: add ImgIoUtilCheckSizeArgumentsOverflow"
bc86b7a8 imageio_util: add ImgIoUtilCheckSizeArgumentsOverflow
806f6279 gif2webp: fix crash with NULL extension data
68ae5b67 Add libwebp/src/mux/animi.h
28ce3043 Remove some errors when compiling the code as C++.
b34abcb8 Favor keeping the areas locally similar in spatial prediction mode selection
ba843a92 fix some SSIM calculations
51b71fd2 Merge "vwebp: ensure setenv() is available in stdlib.h"
fb01743a get_disto: fix the r/g/b order for luma calculation
bfab8947 vwebp: ensure setenv() is available in stdlib.h
9310d192 vwebp: handle window resizing properly
f79450ca Speedup ApplyMap.
cfdda7c6 Merge "prevent 32b overflow for very large canvas_width / height"
e36396ba Merge "get_disto: new option to compute SSIM map and convert to gray"
18a9a0ab Add an API to import a color-mapped image.
30d43706 Speed-up Combined entropy for palettized histograms.
36aa087b get_disto: new option to compute SSIM map and convert to gray
86a84b35 2x faster SSE2 implementation of SSIMGet
b8384b53 lower WEBP_MAX_ALLOCABLE_MEMORY default
1c364400 prevent 32b overflow for very large canvas_width / height
eee0cce1 Merge "Small LZ77 speedups."
5f1caf29 Small LZ77 speedups.
1effde7b fix anim_util.c compilation when HAVE_GIF is not defined.
a2fe9bf4 Speedup TrellisQuantizeBlock().
573cce27 smartYUV improvements
21e7537a  fix infinite loop in case of PARTITION0 overflow
053a1565 Merge "Change the rule of picking UV mode in MBAnalyzeBestUVMode()"
1377ac2e Change the rule of picking UV mode in MBAnalyzeBestUVMode()
7c1fb7d0 fix uint32_t initialization (0. -> 0)
bfff0bf3 speed-up SSIM calculation
64577de8 De-VP8L-ize GetEntropUnrefinedHelper.
a7be7328 Merge "refactor the PSNR / SSIM calculation code"
50c3d7da refactor the PSNR / SSIM calculation code
d6228aed indentation fix after I7055d3ee3bd7ed5e78e94ae82cb858fa7db3ddc0
dd538b19 Remove unused declaration.
6cc48b17 Move some lossless logic out of dsp.
78363e9e Merge "Remove a redundant call to InitLeft() in VP8IteratorReset()"
ffd01929 Refactor VP8IteratorNext().
c4f6d9c9 Remove a redundant call to InitLeft() in VP8IteratorReset()
c27d8210 Merge "smartYUV: simplify main loop"
07795296 smartYUV: simplify main loop
c9b45863 Split off common lossless dsp inline functions.
490ae5b1 smartYUV: improve initial state for faster convergence
894232be smartYUV: fix and simplify the over-zealous stop criterion
8de08483 Remove unused code in webpi.h
41cab7fe imageio/Android.mk: correct imagedec dependencies
82c91c70 Merge "libimageenc.a: extract image-saving code from dwebp"
af1ad3e2 libimageenc.a: extract image-saving code from dwebp
dd7309e3 Merge "doc: use two's complement explicitly for uint8->int8 conversion"
6105777e Merge "add gif2webp to CMake"
13ae011e doc: use two's complement explicitly for uint8->int8 conversion
4bda0cfb add gif2webp to CMake
6029c7fe Merge "remove mention of fragment, frgm, FRGM, etc."
545c147f remove mention of fragment, frgm, FRGM, etc.
5b46f7fc cwebp.1: improve some grammar
9e478f80 dec/vp8l.c: add assertions in EmitRescaledRowsRGBA/YUVA
43bd8958 Make gif transparent color to be transparent black
0887fc2d Merge "get_disto: add a '-o file' option to save a diff map"
0de48e18 get_disto: add a '-o file' option to save a diff map
0a57ad0d cosmetics: WebPSafeAlloc -> WebPSafeMalloc
0a4699bc Merge "WebPPictureDistortion(): free() -> WebPSafeFree()"
29fedbf5 Anim_encoder: correctly handle enc->prev_candidate_undecided_
32dead4e WebPPictureDistortion(): free() -> WebPSafeFree()
85cd5d06 Smarter LZ77 for uniform regions.
6585075f Change PixelsAreSimilar() to handle black pixels correctly.
c0a27fd2 vwebp: Clear previous frame when a key triggers a redraw
57a5e3b6 webp_quality should return '0' in case of success.
7f1b897b Faster stochastic histogram merging.
48c810b8 Merge "remove WEBP_FORCE_ALIGNED and use memcpy() instead."
3884972e remove WEBP_FORCE_ALIGNED and use memcpy() instead.
485cac1a switch libimagedec.a and libimageio_util.a to avoid undefined symbol
005e15b1 Merge "{extras,mux}/Makefile.am: add missing -lm"
6ab496ed fix some 'unsigned integer overflow' warnings in ubsan
8a4ebc6a Revert "fix 'unsigned integer overflow' warnings in ubsan"
9d4f209f Merge changes I25711dd5,I43188fab
e44f5248 fix 'unsigned integer overflow' warnings in ubsan
27b5d991 Fix assertions in WebPRescalerExportRow()
74f6f9e7 Add descriptions of default configuration in help info.
aaf2530c {extras,mux}/Makefile.am: add missing -lm
1269dc7c Refactor VP8LColorCacheContains()
40872fb2 dec_neon,NeedsHev: micro optimization
7b54e26b Add a CMake option for WEBP_SWAP_16BIT_CSP.
d2223d8d Fix missing cpu-features for Android.
bf16a4b4 Merge "cpu.cmake: improve webp_check_compiler_flag output"
ee1057e3 cpu.cmake: improve webp_check_compiler_flag output
b551e587 cosmetics: add {}s on continued control statements
d2e4484e dsp/Makefile.am: put msa source in correct lib
c7f66c82 Merge "utils/thread.c,cosmetics: join a few lines"
98d8f295 Merge "examples/Makefile.am,cosmetics: sort binary targets"
39f4ffbc utils/thread.c,cosmetics: join a few lines
a86ce2b1 Merge "extras/Makefile.am: don't install libwebpextras"
6fa9fe24 extras/Makefile.am: don't install libwebpextras
0b2c58a9 Fix an unsigned integer overflow error in enc/cost.h
d7ce4a2e examples/Makefile.am,cosmetics: sort binary targets
386e4ba2 Reset segment id if we decide not to update segment map
7b87e848 Merge "Add MSA optimized YUV to RGB upsampling functions"
d3ddacb6 Add MSA optimized YUV to RGB upsampling functions
eb98d8d8 webp_quality: detect lossless format and features
ebee57f4 move imageio/example_util.[hc] (back to) examples/
99542bbf webpdec: s/ExUtil//
da573cf4 imageio_util: s/ExUtil/ImgIoUtil/
bdda5bd4 split example_util.h
15ed462b .gitignore: add extras/{get_disto,webp_quality}
7be57489 Merge "VP8EstimateQuality(): roughty estimate webp bitstream quality factor"
57020525 Makefile.vc: add missing imageio target
e8ab6a82 VP8EstimateQuality(): roughty estimate webp bitstream quality factor
fee7b3d6 Merge "'extras/get_disto' example: compute PSNR between two files"
1e7d4401 'extras/get_disto' example: compute PSNR between two files
4cecab63 pngdec.c,jpegdec.[hc]: remove unnecessary includes
259f0434 makefile.unix: normalize image decode lib name
ed34c39b fix: examples/libexample_dec.a => imageio/libexample_dec.a
33d8d0d4 Merge "move examples/{example_util,image_dec} to imageio/"
c960b82e Merge "extras.h: correct include guard"
fe3cd28a Merge ".gitignore: add .gradle, /build"
45fbeba5 Merge "Do token recording and counting in a single loop"
4f33c820 .gitignore: add .gradle, /build
c379b55a move examples/{example_util,image_dec} to imageio/
5108d9aa extras.h: correct include guard
ad497fbc move src/extras to the top-level
0c0fb832 Do token recording and counting in a single loop
9ac74f92 Add MSA optimized rescaling functions
cb19dbc1 Add MSA optimized color transform functions
3f4042b5 WebPAnimEncoder: If 'minimize_size' and 'allow_mixed' on, try lossy + lossless.
5e2eb89e cosmetics,dsp/*msa.c: associate '*' with the type
5b60db5c FastMBAnalyze() for quick i16/i4 decision
567e6977 Add MSA optimized CollectHistogram function
c54ab8dd Add MSA optimized quantization functions
ec6f68c5 Merge "Remove QuantizeBlockWHT() in enc.c"
2a5c417c Apply the RLE heuristic to LZ77.
91b59e88 Remove QuantizeBlockWHT() in enc.c
fe572737 Add MSA optimized SSE functions
6b53ca87 cosmetics,(dec|enc)_sse2.c: fix indent
b15d00d9 Merge "Add MSA optimized encoder IntraChromaPreds function"
afe3cec8 Add MSA optimized encoder IntraChromaPreds function
fc8cad9f reduce the number of malloc/free cycles in huffman.c
7b4b05e0 Add MSA optimized encoder Intra16Preds function
c18787a0 Add MSA optimized encoder Intra4Preds function
479d1908 webpmux: Also print compression info per frame.
a80e8cfd Provide support for CMake on Android studio 2.2.
6c628410 Split the main CMake file.
bbb6ecd9 Merge "Add MSA optimized distortion functions"
7915396f Add MSA optimized distortion functions
652e944f Merge "build.gradle: remove tab"
c0991a14 io,EmitRescaledAlphaYUV: factor out a common expr
48bf5ed1 build.gradle: remove tab
bfef6c9f Merge tag 'v0.5.1'
3d97bb75 update ChangeLog (tag: v0.5.1, origin/0.5.1)
deb54d91 Clarify the expected 'config' lifespan in WebPIDecode()
435308e0 Add MSA optimized encoder transform functions
dce64bfa Add MSA optimized alpha filter functions
429120d0 Add MSA optimized color transform functions
c7e2d245 update ChangeLog (tag: v0.5.1-rc5)
55b2fede normalize the macros' "do {...} while (0)" constructs
701c772e Add MSA optimized colorspace conversion functions
c7eb06f7 Fix corner case in CostManagerInit.
f918cb10 fix rescaling bug: alpha plane wasn't filled with 0xff
ab7937a5 gif2webp: normalize the number of .'s in the help message
3cdec847 vwebp: normalize the number of .'s in the help message
bdf6241e cwebp: normalize the number of .'s in the help message
06a38c7b fix rescaling bug: alpha plane wasn't filled with 0xff
319e37be Improve lossless compression.
6a197937 Add MSA optimized intra pred chroma functions
447adbce 'our bug tracker' -> 'the bug tracker'
97b9e644 normalize the number of .'s in the help message
293d786f Added MSA optimized intra prediction 16x16 functions
0afa0ce2 Added MSA optimized intra prediction 4x4 functions
a6621bac Added MSA optimized simple edge filtering functions
bb50bf42 pngdec,ReadFunc: throw an error on invalid read
38063af1 decode.h,WebPGetInfo: normalize function comment
1ebf193c Added MSA optimized chroma edge filtering functions
9ad2352d Merge "Added MSA optimized edge filtering functions"
60751096 Added MSA optimized edge filtering functions
9e8e1b7b Inline GetResidual for speed.
7d58d1b7 Speed-up uniform-region processing.
8ec7032b simplify HistogramCombineEntropyBin()
23e29cb1 Merge "Fix a boundary case in BackwardReferencesHashChainDistanceOnly." into 0.5.1
472a049b remove bin_map[] allocation altogether
0bb23b2c free -> WebPSafeFree()
a977b4b5 Merge "rewrite the bin_map clustering to use less memory"
3591ba66 rewrite the bin_map clustering to use less memory
e6ac450c utils.[hc]: s/MAX_COLOR_COUNT/MAX_PALETTE_SIZE/
e7b91772 Merge "DecodeImageData(): change the incorrect assert" into 0.5.1
2abfa54f DecodeImageData(): change the incorrect assert
5a48fcd8 Merge "configure: test for -Wfloat-conversion"
0174d18d Fix a boundary case in BackwardReferencesHashChainDistanceOnly.
6a9c262a Merge "Added MSA optimized transform functions"
cfbcc5ec Make sure to consider small distances in LZ77.
5e60c42a Added MSA optimized transform functions
3dc28d76 configure: test for -Wfloat-conversion
f2a0946a add some asserts to delimit the perimeter of CostManager's operation
9a583c66 fix invalid-write bug for alpha-decoding
f66512db make gradlew executable
6fda58f1 backward_references: quiet double->int warning
a48cc9d2 Merge "Fix a compression regression for images with long uniform regions." into 0.5.1
cc2720c1 Merge "Revert an LZ77 boundary constant." into 0.5.1
059aab4f Fix a compression regression for images with long uniform regions.
b0c7e49e Check more backward matches with higher quality.
a3611513 Revert an LZ77 boundary constant.
8190374c README: fix typo
7551db44 update NEWS
0fb2269c bump version to 0.5.1
f4537610 update AUTHORS & .mailmap
3259571e Refactor GetColorPalette method.
1df5e260 avoid using tmp histogram in PreparePair()
7685123a fix comment typos
a246b921 Speedup backward references.
76d73f18 Merge "CostManager: introduce a free-list of ~10 intervals"
eab39d81 CostManager: introduce a free-list of ~10 intervals
4c59aac0 Merge "mips msa webp configuration"
043c33f1 Merge "Improve speed and compression in backward reference for lossless."
71be9b8c Merge "clarify variable names in HistogramRemap()"
0ba7fd70 Improve speed and compression in backward reference for lossless.
0481d42a CostManager: cache one interval and re-use it when possible
41b7e6b5 Merge "histogram: fix bin calculation"
96c3d624 histogram: fix bin calculation
fe9e31ef clarify variable names in HistogramRemap()
ce3c8247 disable near-lossless quantization if palette is used
e11da081 mips msa webp configuration
5f8f998d mux: Presence of unknown chunks should trigger VP8X chunk output.
cadec0b1 Merge "Sync mips32 and dsp_r2 YUV->RGB code with C verison"
d9637758 Compute the hash chain once and for all for lossless compression.
50a48665 Sync mips32 and dsp_r2 YUV->RGB code with C verison
eee788e2 Merge "introduce a common signature for all image reader function"
d77b877c introduce a common signature for all image reader function
ca8d9519 remove some obsolete TODOs
ae2a7222 collect all decoding utilities from examples/ in libexampledec.a
0b8ae852 Merge "Move DitherCombine8x8 to dsp/dec.c"
77cad885 Merge "ReadWebP: avoid conversion to ARGB if final format is YUVA"
ab8d6698 ReadWebP: avoid conversion to ARGB if final format is YUVA
f8b7ce9e Merge "test pointer to NULL explicitly"
5df6f214 test pointer to NULL explicitly
77f21c9c Move DitherCombine8x8 to dsp/dec.c
c9e6d865 Add gradle support
c65f41e8 Revert "Add gradle support"
bf731ede Add gradle support
08333b85 WebPAnimEncoder: Detect when canvas is modified, restore only when needed.
0209d7e6 Merge "speed-up MapToPalette() with binary search"
fdd29a3d speed-up MapToPalette() with binary search
cf4a651b Revert "Refactor GetColorPalette method."
0a27aca3 Merge changes Idfa8ce83,I19adc9c4
f25c4406 WebPAnimEncoder: Restore original canvas between multiple encodes.
169004b1 Refactor GetColorPalette method.
576362ab VP8LDoFillBitWindow: support big-endian in fast path
ac49e4e4 bit_reader.c: s/VP8L_USE_UNALIGNED_LOAD/VP8L_USE_FAST_LOAD/
d39ceb58 VP8LDoFillBitWindow: remove stale TODO
2ec2de14 Merge "Speed-up BackwardReferencesHashChainDistanceOnly."
3e023c17 Speed-up BackwardReferencesHashChainDistanceOnly.
f2e1efbe Improve near lossless compression when a prediction filter is used.
e15afbce dsp.h: fix ubsan macro name
e53c9ccb dsp.h: add WEBP_UBSAN_IGNORE_UNSIGNED_OVERFLOW
af81fdb7 utils.h: quiet -fsanitize=undefined warnings
ea0be354 dsp.h: remove utils.h include
cd276aec utils/*.c: ../utils/utils.h -> ./utils.h
c8927131 utils/Makefile.am: add some missing headers
ea24e026 Merge "dsp.h: add WEBP_UBSAN_IGNORE_UNDEF"
369e264e dsp.h: add WEBP_UBSAN_IGNORE_UNDEF
0d020a78 Merge "add runtime NEON detection"
5ee2136a Merge "add VP8LAddPixels() to lossless.h"
47435a61 add VP8LAddPixels() to lossless.h
8fa6ac68 remove two ubsan warnings
74fb56fb add runtime NEON detection
4154a839 MIPS update to new Unfilter API
c80b9fc8 Merge "cherry-pick decoder fix for 64-bit android devices"
6235147e cherry-pick decoder fix for 64-bit android devices
d41b8c43 configure: test for -Wformat-* w/-Wformat present
5f95589f Fix WEBP_ALIGN in case the argument is a pointer to a type larger than a byte.
2309fd5c replace num_parts_ by num_parts_minus_one_ (unsigned)
9629f4bc SimplifySegments: quiet -Warray-bounds warning
de47492e Merge "update the Unfilter API in dsp to process one row independently"
2102ccd0 update the Unfilter API in dsp to process one row independently
e3912d56 WebPAnimEncoder: Restore canvas before evaluating blending possibility.
6e12e1e3 WebPAnimEncoder: Fix for single-frame optimization.
602f344a Merge changes I1d03acac,Ifcb64219
95ecccf6 only apply color-mapping for alpha on the cropped area
47dd0708 anim_diff: Add an experimental option for max inter-frame diff.
aa809cfe only allocate alpha_plane_ up to crop_bottom row
31f2b8d8 WebPAnimEncoder: FlattenSimilarPixels(): look for similar
774dfbdc perform alpha filtering within the decoding loop
a4cae68d lossless decoding: only process decoded row up to last_row
238cdcdb Only call WebPDequantizeLevels() on cropped area
cf6c713a alpha: preparatory cleanup
b95ac0a2 Merge "VP8GetHeaders(): initialize VP8Io with sane value for crop/scale dimensions"
89231394 VP8GetHeaders(): initialize VP8Io with sane value for crop/scale dimensions
5828e199 use_8b_decode -> use_8b_decode_
8dca0247 fix bug in alpha.c that was triggering a memory error in incremental mode
9a950c53 WebPAnimEncoder: Disable filtering when blending is used with lossy encoding.
eb423903 WebPAnimEncoder: choose max diff for framerect based on quality.
ff0a94be WebPAnimEncoder lossy: ignore small pixel differences for frame rectangles.
f8040084 gif2webp: Remove the 'prev_to_prev_canvas' buffer.
6d8c07d3 Merge "WebPDequantizeLevels(): use stride in CountLevels()"
d96fe5e0 WebPDequantizeLevels(): use stride in CountLevels()
ec1b2407 WebPPictureImport*: check output pointer
c0768769 Merge "Revert "Re-enable encoding of alpha plane with color cache for next release.""
41f14bcb WebPPictureImport*: check src pointer
64eed387 Pass stride parameter to WebPDequantizeLevels()
97934e24 Revert "Re-enable encoding of alpha plane with color cache for next release."
e88c4ca0 fix -m 2 mode-cost evaluation (causing partition0 overflow)
4562e83d Merge "add extra meaning to WebPDecBuffer::is_external_memory"
abdb109f add extra meaning to WebPDecBuffer::is_external_memory
875aec70 enc_neon,cosmetics: break long comment
71e856cf GetMBSSIM,cosmetics: fix alignment
a90edffb fix missing 'extern' for SSIM function in dsp/
423ecaf4 move some SSIM-accumulation function for dsp/
f08e6624 Merge "Fix FindClosestDiscretized in near lossless:"
0d40cc5e enc_neon,Disto4x4: remove an unnecessary transpose
e8feb20e Fix FindClosestDiscretized in near lossless:
82006430 anim_util: quiet static analysis warning
a6f23c49 Merge "AnimEncoder: Support progress hook and user data."
a5193774 Merge "Near lossless feature: fix some comments."
da98d31c AnimEncoder: Support progress hook and user data.
33357131 Near lossless feature: fix some comments.
0beed01a cosmetics: fix indent after 2f5e898
6753f35c Merge "FTransformWHT optimization."
6583bb1a Improve SSE4.1 implementation of TTransform.
7561d0c3 FTransformWHT optimization.
7ccdb734 fix indentation after patch #328220
6ec0d2a9 clarify the logic of the error path when decoding fails.
8aa352b2 Merge "Remove an unnecessary transposition in TTransform."
db860884 Merge "remove useless #include"
9960c316 Remove an unnecessary transposition in TTransform.
6e36b511 Small speedup in FTransform.
9dbd4aad Merge "fix C and SIMD flags completion."
e60853ea Add missing common_sse2.h file to makefile.unix
696eb2b0 fix C and SIMD flags completion.
2b4fe33e Merge "fix multiple allocation for transform buffer"
2f5e8986 fix multiple allocation for transform buffer
bf2b4f11 Regroup common SSE code + optimization.
4ed650a1 force "-pass 6" if -psnr or -size is used but -pass isn't.
3ef1ce98 yuv_sse2: fix -Wconstant-conversion warning
a7a03e9f Merge changes I4852d18f,I51ccb85d
5e122bd6 gif2webp: set enc_options.verbose = 0 w/-quiet
ab3c2583 anim_encode,DefaultEncoderOptions: init verbose
8f0dee77 Merge "configure: fix builtin detection w/-Werror"
4a7b85a9 cmake: fix builtin detection w/-Werror
b74657fb configure: fix builtin detection w/-Werror
3661b980 Add a CMakeLists.txt
75f4af4d remove useless #include
6c1d7631 avoid Yoda style for comparison
8ce975ac SSE optimization for vector mismatch.
7db53831 Merge tag 'v0.5.0'
37f04949 update ChangeLog (tag: v0.5.0-rc1, tag: v0.5.0, origin/0.5.0)
7e7b6ccc faster rgb565/rgb4444/argb output
4c7f565f update NEWS
1f62b6b2 update AUTHORS
e224fdc8 update mailmap
71100500 bump version to 0.5.0
230a685e README: update help text, repo link
d48e427b Merge "demux: accept raw bitstreams"
99a01f4f Merge "Unify some entropy functions."
4b025f10 Merge "configure: disable asserts by default"
92cbddf8 Merge "fix PrintBlockInfo()"
ca509a33 Unify some entropy functions.
367bf903 fix PrintBlockInfo()
b0547ff0 move back common constants for lossless_enc*.c into the .h
fb4c7832 lossless: simpler alpha cleanup preprocessing
ba7f4b68 Merge "anim_diff: add brief description of options"
47ddd5a4 Move some codec logic out of ./dsp .
b4106c44 anim_diff: add brief description of options
357f455d yuv_sse2: fix 32-bit visual studio build
b9d80fa4 configure: disable asserts by default
7badd3da cosmetic fix: sizeof(type) -> sizeof(*var)
80ce27d3 Speed up 24-bit packing / unpacking in YUV / RGB conversions.
68eebcb0 remove a TODO about rotation
2dee2966 remove few obsolete TODO about aligned loads in SSE2
e0c0bb34 remove TODO about unused ref_lf_delta[]
9cf1cc2b remove few TODO:   * 256 -> RD_DISTO_MULT   * don't use TDisto for UV mode picking
79189645 Merge changes from topic 'demux-fragment-cleanup'
47399f92 demux: remove GetFragment()
d3cfb79a demux: remove dead fragment related TODO
ab714b8a demux, Frame: remove is_fragment_ field
b105921c yuv_sse2, cosmetics: fix indent
466c92e8 demux,WebPIterator: remove fragment_num/num_fragments
11714ff1 demux: remove WebPDemuxSelectFragment
c0f7cc47 fix for bug #280: UMR in next->bits
578beeb8 Merge "enc/Makefile.am: add missing headers"
1a819f00 makefile.unix: make visibility=hidden the default
d4f9c2ef enc/Makefile.am: add missing headers
846caff4 configure: check for -fvisibility=hidden
3f3ea2c5 demux: accept raw bitstreams
d6dad5d0 man cwebp: add precision about exactness of the 'lossless' mode
46bb1e34 Merge "gifdec: remove utils.h include"
2b882e94 Merge "Makefile.vc: define WEBP_HAVE_GIF for gifdec.c"
892b9238 Merge "man/*, AUTHORS: clarify origin of the tool"
e5687a18 Merge "fix optimized build with -mcmodel=medium"
e56e6859 Makefile.vc: define WEBP_HAVE_GIF for gifdec.c
4077d944 gifdec: remove utils.h include
b5e30dac man/*, AUTHORS: clarify origin of the tool
b275e598 fix optimized build with -mcmodel=medium
64da45a9 cosmetics, cwebp: fix indent
038a060d Merge "add disto-based refinement for UV mode (if method = 1 or 2)"
2835089d Provide an SSE2 implementation of CombinedShannonEntropy.
e6c93519 add disto-based refinement for UV mode (if method = 1 or 2)
04507dc9 Merge "fix undefined behaviour during shift, using a cast"
793c5261 Merge "wicdec: add support for reading from stdin"
d3d16397 Optimize the heap usage in HistogramCombineGreedy.
202a710b fix undefined behaviour during shift, using a cast
14d27a46 improve method #2 by merging DistoRefine() and  SimpleQuantize()
cb1ce996 Merge "10% faster table-less SSE2/NEON version of YUV->RGB conversion"
ac761a37 10% faster table-less SSE2/NEON version of YUV->RGB conversion
79fcf29a wicdec: add support for reading from stdin
015f173f Merge "cwebp: add support for stdin input"
a9947c32 cwebp: add support for stdin input
7eb01ff3 Merge "Improved alpha cleanup for the webp encoder when prediction transform is used."
fb8c9106 Merge "introduce WebPMemToUint32 and WebPUint32ToMem for memory access"
bd91af20 Merge "bit_reader: remove aarch64 BITS TODO"
6c702b81 Speed up hash chain initialization using memset.
4c60f63c make ReadPNG and ReadJPEG take a filename instead of a FILE
464ed10f bit_reader: remove aarch64 BITS TODO
d478e589 Merge "configure: update issue tracker"
69381113 Improved alpha cleanup for the webp encoder when prediction transform is used.
2c08aac8 introduce WebPMemToUint32 and WebPUint32ToMem for memory access
010ca3d1 Fix FindMatchLength with non-aligned buffers.
a90e1e3f README: add prerequisites for an autoconf build
458f0866 configure: update issue tracker
33914595 vwebp: work around the transparent background with GLUT bug
e4a7eed4 cosmetics: fix indent
08375129 Merge "Make a separate case for low_effort in CopyImageWithPrediction"
aa2eb2d4 Merge "cosmetics: fix indent"
b7551e90 cosmetics: fix indent
5bda52d4 Make a separate case for low_effort in CopyImageWithPrediction
66fa598a Merge "configure: fix intrinsics build w/older gcc"
5ae220be backward_references.c: Fixed compiler warning
1556da09 Merge "configure: restore 2 warnings"
71a17e58 configure: restore 2 warnings
9eeabc07 configure: fix intrinsics build w/older gcc
363babe2 Merge "fix some warning about unaligned 32b reads"
a1411782 Optimization in hash chain comparison for 64 bit Arrays were compared 32 bits at a time, it is now done 64 bits at a time. Overall encoding speed-up is only of 0.2% on @skal's small PNG corpus. It is of 3% on my initial 1.3 Mp desktop screenshot image.
829bd141 Combine Huffman cost and bit entropy into one loop
a7a954c8 Merge "lossless: make prediction in encoder work per scanline"
61b605b4 Merge "fix of undefined multiply (int32 overflow)"
239421c5 lossless: make prediction in encoder work per scanline
f5ca40e0 fix of undefined multiply (int32 overflow)
5cd2ef4c Merge changes from topic 'win-threading-compat'
76ce9187 Makefile.vc: enable WEBP_USE_THREAD for windows phone
d2afe974 thread: use CreateThread for windows phone
0fd0e12b thread: use WaitForSingleObjectEx if available
63fadc9f thread: use InitializeCriticalSectionEx if available
110ad583 thread: use native windows cond var if available
912c9fdf dec/webp: use GetLE(24|32) from utils
f1694481 utils/GetLE32: correct uint32 promotion
158763de Merge "always call WebPInitSamplers(), don't try to be smart"
3770f3bb Merge "cleanup the YFIX/TFIX difference by removing some code and #define"
a40f60a9 Merge "3% speed improvement for lossless webp encoder for low effort mode:"
ed1c2bc6 always call WebPInitSamplers(), don't try to be smart
b8c44f1a 3% speed improvement for lossless webp encoder for low effort mode:
997e1038 cleanup the YFIX/TFIX difference by removing some code and #define
d73d1c8b Merge "Make discarding invisible RGB values (cleanup alpha) the default."
1f9be97c Make discarding invisible RGB values (cleanup alpha) the default.
f240117b Make dwebp listen more to the -quiet flag
b37b0179 fix for issue #275: don't compare to out-of-bound pointers
21735e06 speed-up trivial one-symbol decoding case for lossless
397863bd Refactor CopyPlane() and CopyPixels() methods: put them in utils.
6ecd72f8 Re-enable encoding of alpha plane with color cache for next release.
1f7148a4 Merge "remove unused fields from WebPDecoderOptions and WebPBitstreamFeatures"
6ae395fa Merge "use ExReadFile() for ReadYUV()"
8076a00e gitignore list: add anim_diff.
1c1702d8 use ExReadFile() for ReadYUV()
775d3a37 remove unused fields from WebPDecoderOptions and WebPBitstreamFeatures
c13245c7 AnimEncoder: Add a GetError() method.
688b265d AnimDecoder API: Add a GetDemuxer() method.
1aa4e3d6 WebPAnimDecoder: add an option to enable multi-threaded decoding.
3584abca AnimDecoder: option to decode to common color modes.
afd5a62c Merge "mux.h does NOT need to include encode.h"
8550d443 Merge "migrate anim_diff tool from C++ to C89"
96201e50 migrate anim_diff tool from C++ to C89
945cfa3b mux.h does NOT need to include encode.h
8da07e8d Merge "~2x faster SSE2 RGB24toY, BGR24toY, ARGBToY|UV"
bfd3fc02 ~2x faster SSE2 RGB24toY, BGR24toY, ARGBToY|UV
02432427 man/cwebp.1, cosmetics: escape '-'s
96f5b423 man/cwebp: group lossy-only options
52fdbdfe extract some RGB24 to Luma conversion function from enc/ to dsp/
ab8c2300 add missing \n
8304179a sync NEWS with 0.4.4
5bd04a08 sync versions with 0.4.4
8f1fcc15 Merge "Move ARGB->YUV functions from dec/vp8l.c to dsp/yuv.c"
25bf2ce5 fix some warning about unaligned 32b reads
922268fd s/TIFF/WebP
fa8927ef Move ARGB->YUV functions from dec/vp8l.c to dsp/yuv.c
9b373598 Merge "for ReadXXXX() image-readers, use the value of pic->use_argb"
f7c507a5 Merge "remove unnecessary #include "yuv.h""
7861578b for ReadXXXX() image-readers, use the value of pic->use_argb
14e4043b remove unnecessary #include "yuv.h"
469ba2cd vwebp: fix incorrect clipping w/NO_BLEND
4b9186b2 update issue tracker url
d64d376c change WEBP_ALIGN_CST value to 31
f717b828 vp8l.c, cosmetics: fix indent after 95509f9
927ccdc4 Merge "fix alignment of allocated memory in AllocateTransformBuffer"
fea94b2b fix alignment of allocated memory in AllocateTransformBuffer
5aa8d61f Merge "MIPS: rescaler code synced with C implementation"
e7fb267d MIPS: rescaler code synced with C implementation
93c86ed5 Merge "format_constants.h: MKFOURCC, correct cast"
5d791d26 format_constants.h: MKFOURCC, correct cast
65726cd3 dsp/lossless: Average2, make a constant unsigned
d26d9def Use __has_builtin to check clang support
12ec204e moved ALIGN_CST into util/utils.h and renamed WEBP_ALIGN_xxx
a2640838 Merge "rescaler: ~20% faster SSE2 implementation for lossless ImportRowExpand"
3fb600d5 Merge "wicdec: fix alpha detection w/64bpp BGRA/RGBA"
67c547fd rescaler: ~20% faster SSE2 implementation for lossless ImportRowExpand
99e3f812 Merge "large re-organization of the delta-palettization code"
95509f99 large re-organization of the delta-palettization code
74fb458b fix for weird msvc warning message
ae49ad86 Merge "SSE2 implementation of ImportRowShrink"
932fd4df SSE2 implementation of ImportRowShrink
badfcbaa wicdec: fix alpha detection w/64bpp BGRA/RGBA
35cafa6c Merge "iosbuild: fix linking with Xcode 7 / iOS SDK 9"
b0c9d8af label rename: NO_CHANGE -> NoChange
b4e731cd neon-implementation for rescaler code
db1321a6 iosbuild: fix linking with Xcode 7 / iOS SDK 9
6dfa5e3e rescaler: better handling of the fxy_scale=0 special case.
55c05293 Revert "rescaler: better handling of the fxy_scale=0 special case."
9f226bf8 rescaler: better handling of the fxy_scale=0 special case.
f7b8f907 delta_palettization.*: add copyright
c1e1b710 Changed delta palette to compress better
0dd28267 Merge "Add delta_palettization feature to WebP"
48f66b66 Add delta_palettization feature to WebP
27933e2a anim_encoder: drop a frame if it has same pixels as the prev frame.
df9f6ec8 Merge "webpmux/DisplayInfo: send non-error output to stdout"
8af4993b Merge "rescaler_mips_dsp_r2: cosmetics, fix indent"
2b9d2495 Merge "rescaler: cosmetics, join two lines"
cc020a8c webpmux/DisplayInfo: send non-error output to stdout
a288e746 configure: add -Wshorten-64-to-32
c4c3cf2d pngdec: fix type conversion warnings
bef8e97d webpmux: fix type conversion warning
5a84460d rescaler_mips_dsp_r2: cosmetics, fix indent
acde0aae rescaler: cosmetics, join two lines
306ce4fd rescaler: move the 1x1 or 2x1 handling one level up
cced974b remove _mm_set_epi64x(), which is too specific
56668c9f fix warnings about uint64_t -> uint32_t conversion
76a7dc39 rescaler: add some SSE2 code
1df1d0ee rescaler: harmonize function protos
9ba1894b rescaler: simplify ImportRow logic
5ff0079e fix rescaler vertical interpolation
cd82440e VP8LAllocateHistogramSet: align histogram[] entries
a406b1dd Merge "fix memory over-allocation in lossless rescaler init"
0fde33e3 add missing const in VP8InitFrame signature
ac7d5e8d fix memory over-allocation in lossless rescaler init
017f8ccc Loosen the buffer size checks for Y/U/V/A too.
15ca5014 loosen the padding check on buffer size
d623a870 dec_neon: add whitespace around stringizing operator
29377d55 dsp/mips: cosmetics: add whitespace around XSTR macro
eebaf97f dsp/mips: add whitespace around stringizing operator
d39dc8f3 Create a WebPAnimDecoder API.
03fb7522 gif2webp: print output file size
14efabbf Android: limit use of cpufeatures
7b83adbe preparatory cosmetics for Rescaler code fix and clean-up
77fb41c2 dec/vp8l/DecodeAlphaData: remove redundant cast
90fcfcd9 Insert less hash chain entries from the beginnings of long copies.
bd55604d SSE2: add yuv444 converters, re-using yuv_sse2.c
41a5d99d add a -quiet option to 'dwebp'
80ab3edb Merge "README: update dwebp help output after 1e595fe"
32b71b2e README: update dwebp help output after 1e595fe
3ec11827 use the DispatchAlpha() call from dsp
c5f00621 incorporate bzero() into WebPRescalerInit() instead of call site
3ebcdd41 remove duplicate "#include <stdlib.h>"
1e595fe1 dwebp: add -resize as a synonym for -scale
24a96932 dec: allow 0 as a scaling dimension
b9187242 utils/rescaler: add WebPRescalerGetScaledDimensions
923e8eda Merge "update NEWS"
020fd099 Merge "WebPPictureDistortion: support ARGB format for 'pic' when computing distortion."
6a5292f6 update NEWS
56a2e9f5 WebPPictureDistortion: support ARGB format for 'pic' when computing distortion.
0ae582e4 configure: test and add -Wunreachable-code
c2f9dc06 bit_writer: convert VP8L macro values to immediates
b969f888 Reduce magic in palette reordering
acb297e9 anim_diff: add a -raw_comparison flag
155c1b22 Merge changes I76f4d6fe,*********
717e4d5a mips32/mipsDSPr2: function ImportRow rebased
7df93893 fix rescaling bug (uninitialized read, see bug #254).
5cdcd561 lossless_enc_neon: add VP8LTransformColor
a53c3369 lossless_neon: add VP8LTransformColorInverse
99131e7f Merge changes I9fb25a89,Ibc648e9e
c4556766 simplify the main loop for downscaling
2a010f99 lossless_neon: remove predictors 5-13
ca221bbc ll_enc_neon: enable VP8LSubtractGreenFromBlueAndRed
585d93db Container spec: clarify ordering of ALPH chunk.
01d61fd9 lossless: ~20 % speedup
f722c8f0 lossless: Speed up ComputeCacheEntropy by 40 %
1ceecdc8 add a VP8LColorCacheSet() method for color cache
17eb6099 lossless: Allow copying from prev row in rle-mode.
f3a7a5bf lossless: bit writer optimization
d97b9ff7 Merge changes from topic 'lossless-enc-improvements'
0250dfcc msvc: fix pointer type warning in BitsLog2Floor
52931fd5 lossless: combine the Huffman code with extra bits
c4855ca2 lossless: Inlining add literal
8e9c94de lossless: simplify HashChainFindCopy heuristics
888429f4 lossless: 0.5 % compression density improvement
7b23b198 lossless: Add zeroes into the predicted histograms.
85b44d8a lossless: encoding, don't compute unnecessary histo
d92453f3 lossless: Remove about 25 % of the speed degradation
2cce0317 Faster alpha coding for webp
5e75642e lossless: rle mode not to accept lengths smaller than 4.
84326e4a lossless: Less code for the entropy selection
16ab951a lossless: 0.37 % compression density improvement
822f113e add WebPFree() to the API
0ae2c2e4 SSE2/SSE41: optimize SSE_16xN loops
39216e59 cosmetics: fix indent after 32462a07
559e54ca Merge "SSE2: slightly faster FTransformWHT"
8ef9a63b SSE2: slightly faster FTransformWHT
f27f7735 lossless_neon: enable VP8LAddGreenToBlueAndRed
36e9c4bc SSE2: minor cosmetrics on in-loop filter code
4741fac4 dsp/lossless_*sse2: remove some unnecessary inlines
1819965e fix warning ("left shift of negative value") using a cast
70170014 SSE2: speed-up some lossless-encoding functions
abcb0128 Merge "SSE2: slightly faster (~5%) AddGreenToBlueAndRed()"
2df5bd30 Merge "Speedup to HuffmanCostCombinedCount"
9e356d6b SSE2: slightly faster (~5%) AddGreenToBlueAndRed()
fc6c75a2 SSE2: 53% faster TransformColor[Inverse]
49073da6 SSE2: 46% speed-up of TransformColor[Inverse]
32462a07 Speedup to HuffmanCostCombinedCount
f3d687e3 SSE4.1 implementation of some lossless encoding functions
bfc300c7 SSE4.1 implementation of some alpha-processing functions
7f9c98f2 Merge "sse2 in-loop: simplify SignedShift8b() a bit"
ef314a5d dec_sse2/GetNotHEV: micro optimization
a729cff9 sse2 in-loop: simplify SignedShift8b() a bit
422ec9fb simplify Load8x4() a bit
8df238ec Merge "remove some duplicate FlipSign()"
751506c4 remove some duplicate FlipSign()
65ef5afc Merge "lossless: 0.13% compression density gain"
2beef2f2 lossless: 0.13% compression density gain
3033f24c lossless: 0.06 % compression density improvement
64960da9 dec_neon: add VE8uv / VE16
14dbd87b dec_neon: add HE8uv / HE16
ac768011 introduce FTransform2 to perform two transforms at a time.
aa6065ae dec_neon: use vld1_dup(mem) rather than vdup(mem[0])
8b63ac78 Merge "dec_neon: add TM16"
f51be09e Merge "dec_neon/TrueMotion: simply left border load"
dc48196b dec_neon: add TM16
ea95b305 dec_neon/TrueMotion: simply left border load
f262d612 speed-up SetResidualSSE2
bf46d0ac fix mips2 build target
929a0fdc enc_sse2/TTransform: simplify abs calculation
17dbd058 enc_sse2/CollectHistogram: simplify abs calculation
a6c15936 dec_neon: add DC16 intra predictors
03b4f50d Makefile.vc: add anim_diff build support.
1b989874 Merge changes I9cd84125,Iee7e387f,I7548be72
acd7b5af Introduce a test tool anim_diff.
f274a96c dsp/enc_sse2: add luma4 intra predictors
040b11bd dsp/enc_sse2: add chroma intra predictors
aee021bb dsp/enc_sse2: add luma16 intra predictors
9e00a499 makefile.unix: remove superclean target
cefc9c09 makefile.unix: clean up after extras target
4c9af023 dec_neon: add DC8uvNoTopLeft
dd55b873 Merge "doc/webp-container-spec: update repo browser link"
f0486968 doc/webp-container-spec: update repo browser link
9287761d Merge "GetResidualCostSSE2: simplify abs calculation"
0e009366 dsp/cpu.c(x86): check maximum supported cpuid feature
b243a4bc GetResidualCostSSE2: simplify abs calculation
6d4602b8 Merge "fix typo: constitutes -> constitute"
5fe1fe37 fix typo: constitutes -> constitute
b83bd7c4 Merge "populate 'libwebpextras' with: import gray, rgb565 and rgb4444 functions"
b0114a32 Merge "histogram.h: cosmetics: remove unnecessary includes"
feab45ef gifdec: Move inclusion of webp/config.h to header.
dbba67d1 histogram.h: cosmetics: remove unnecessary includes
e978fec6 Merge "VP8LBitReader: fix remaining ubsan error with large shifts"
d6fe5884 Merge "ReconstructRow: move some one-time inits out of the main loop"
a21d647c ReconstructRow: move some one-time inits out of the main loop
7a01c3c3 VP8LBitReader: fix remaining ubsan error with large shifts
7fa67c9b change GetPixPairHash64() return type to uint32_t
ec1fb9f8 Merge "dsp/enc.c: cosmetics: move DST() def closer to use"
7073bfb3 Merge "split 64-mult hashing into two 32-bit multiplies"
0768b252 dsp/enc.c: cosmetics: move DST() def closer to use
6a48b8f0 Merge "fix MSVC size_t->int conversion warning"
1db07cde Merge "anim_encode: cosmetics: fix alignment"
e28271a3 anim_encode: cosmetics: fix alignment
7fe357b8 split 64-mult hashing into two 32-bit multiplies
af74c145 populate 'libwebpextras' with: import gray, rgb565 and rgb4444 functions
61214134 remove VP8Residual::cost unused field
e2544823 fix MSVC size_t->int conversion warning
b69a6c35 vwebp: don't redefine snprintf with VS2015+
0ac29c51 AnimEncoder API: Consistent use of trailing underscores in struct.
d4845550 AnimEncoder API: Use timestamp instead of duration as input to Add().
9904e365 dsp/dec_sse2: DC8uv / DC8uvNoLeft speedup
7df20497 dsp/dec_sse2: DC16 / DC16NoLeft speedup
8e515dfe Merge "makefile.unix: add some missing headers"
db12250f cosmetics: vp8enci.h: break long line
bf516a87 makefile.unix: add some missing headers
b44eda3f dsp: add DSP_INIT_STUB
03e76e96 clarify the comment about double-setting the status in SetError()
9fecdd71 remove unused EmitRGB()
43f010dd move ReconstructRow to top
82d98020 add a dec/common.h header to collect common enc/dec #defines
5d4744a2 Merge "enc_sse41: add Disto4x4 / Disto16x16"
e38886a7 mux.h: Bump up ABI version
46305ca6 configure: add --disable-<avx2|sse4.1|sse2>
2fc8b658 CPPFLAGS->CFLAGS for detecting sse4.1 in preprocessor
1a338fb3 enc_sse41: add Disto4x4 / Disto16x16
94055503 encoding SSE4.1 stub for StoreHistogram + Quantize + SSE_16xN
c64659e1 remove duplicate variables after the lossless{_enc}.c split
67ba7c7a enc_sse2: call local FTransform in CollectHistogram
18249799 dsp: s/VP8LSetHistogramData/VP8SetHistogramData/
ede5e158 cosmetics: dsp/lossless.h: reorder prototypes
553051f7 dsp/lossless: split enc/dec functions
9064adc8 Merge "conditionally add -msse4.1 in Makefile.unix"
cecf5096 dsp/yuv*.c: rework WEBP_USE_<arch> ifdef
6584d398 dsp/upsampling*.c: rework WEBP_USE_<arch> ifdef
80809422 dsp/rescaler*.c: rework WEBP_USE_<arch> ifdef
1d93ddec dsp/lossless*.c: rework WEBP_USE_<arch> ifdef
73805ff2 dsp/filters*.c: rework WEBP_USE_<arch> ifdef
fbdcef24 dsp/enc*.c: rework WEBP_USE_<arch> ifdef
66de69c1 dsp/dec*.c: rework WEBP_USE_<arch> ifdef
48e4ffd1 dsp/cost*.c: rework WEBP_USE_<arch> ifdef
29fd6f90 dsp/argb*.c: rework WEBP_USE_<arch> ifdef
80ff3813 dsp/alpha*.c: rework WEBP_USE_<arch> ifdef
bf09cf1e conditionally add -msse4.1 in Makefile.unix
e9570dd9 stub for SSE4.1 support.
4a95384b Merge "dsp: add sse4.1 detection"
cabf4bd2 dsp: add sse4.1 detection
4ecba1ab thread.h: rename interface param
b8d706c8 Merge "sync versions with 0.4.3"
ae64a711 Merge "add shell for libwebpextras"
92a5da9c sync versions with 0.4.3
9d4e2d16 Merge "~30% faster smart-yuv (-pre 4) with early-out criterion"
b1bdbbab ~30% faster smart-yuv (-pre 4) with early-out criterion
7efb9748 Merge "Disable NEON code on Native Client"
ac4f5784 Disable NEON code on Native Client
0873f85b AnimEncoder API: Support input frames in YUV(A) format.
5c176d2d add shell for libwebpextras
44bd9561 fix signature for VP8RecordCoeffTokens()
c9b8ea0e small cosmetics on TokenBuffer.
76394c09 Merge "MIPS: dspr2: added optimization for TrueMotion"
0f773693 WebPPictureRescale: add a note about 0 width/height
241bb5d9 MIPS: dspr2: added optimization for TrueMotion
6cef0e4f examples/Android.mk: add webpmux_example target
53c16ff0 Android.mk: add webpmux target
21852a00 Android.mk: add webpdemux target
8697a3bc Android.mk: add webpdecoder{,_static} targets
4a670491 Android.mk: split source lists per-directory
b5e79422 MIPS: dspr2: Added optimization for some convert functions
0f595db6 MIPS: dspr2: Added optimization for some convert functions
8a218b4a MIPS: [mips32|dspr2]: GetResidualCost rebased
ef987500 Speedup method StoreImageToBitMask by 5%.
602a00f9 fix iOS arm64 build with Xcode 6.3
23820507 1-2% faster encoding by removing an indirection in GetResidualCost()
eddb7e70 MIPS: dspr2: added otpimization for DC8uv, DC8uvNoTop and DC8uvNoLeft
73ba2915 MIPS: dspr2: added optimization for functions RD4 and LD4
c7129da5 Merge "4-5% faster encoding using SSE2 for GetResidualCost"
94380d00 MIPS: dspr2: added optimizaton for functions VE4 and DC4
2a407092 4-5% faster encoding using SSE2 for GetResidualCost
17e19862 Merge "MIPS: dspr2: added optimization for simple filtering functions"
3ec404c4 Merge "dsp: normalize WEBP_TSAN_IGNORE_FUNCTION usage"
b969f5df dsp: normalize WEBP_TSAN_IGNORE_FUNCTION usage
d7b8e711 MIPS: dspr2: added optimization for simple filtering functions
235f774e Merge "MIPS: dspr2: Added optimization for function VP8LTransformColorInverse_C"
42a8a628 MIPS: dspr2: Added optimization for function VP8LTransformColorInverse_C
b442bef3 Merge "ApplyFiltersAndEncode: only copy lossless stats"
b510fbfe doc/webp-container-spec: note MSB order for chunk diagrams
9bc0f922 ApplyFiltersAndEncode: only copy lossless stats
3030f115 Merge "dsp/mips: add some missing TSan annotations"
dfcf4593 Merge "MIPS: dspr2: Added optimization for function VP8LAddGreenToBlueAndRed_C"
55c75a25 dsp/mips: add some missing TSan annotations
2cb879f0 MIPS: dspr2: Added optimization for function VP8LAddGreenToBlueAndRed_C
e1556010 move some cost tables from enc/ to dsp/
c3a03168 Merge "picture_csp: fix build w/USE_GAMMA_COMPRESSION undefined"
39537d7c Merge "VP8LDspInitMIPSdspR2: add missing TSan annotation"
1dd419ce picture_csp: fix build w/USE_GAMMA_COMPRESSION undefined
43fd3543 VP8LDspInitMIPSdspR2: add missing TSan annotation
c7233dfc Merge "VP8LDspInit: remove memcpy"
0ec4da96 picture_csp::InitGammaTables*: add missing TSan annotations
35579a49 VP8LDspInit: remove memcpy
97f6aff8 VP8YUVInit: add missing TSan annotation
f9016d66 dsp/enc::InitTables: add missing TSan annotation
e3d9771a VP8EncDspCostInit*: add missing TSan annotations
d97c143d Merge "doc/webp-container-spec: cosmetics"
309b7908 MIPS: mips32: Added optimization for function SetResidualCoeffs
a987faed MIPS: dspr2: added optimization for function GetResidualCost
e7d3df23 doc/webp-container-spec: cosmetics
be6635e9 Merge "VP8TBufferClear: remove some misleading const's"
02971e72 Merge "VP8EmitTokens: remove unnecessary param void cast"
3b77e5a7 VP8TBufferClear: remove some misleading const's
aa139c8f VP8EmitTokens: remove unnecessary param void cast
c24d8f14 cosmetics: upsampling_sse2: add const to some casts
1829c42c cosmetics: lossless_sse2: add const to some casts
183168f3 cosmetics: enc_sse2: add const to some casts
860badca cosmetics: dec_sse2: add const to some casts
0254db97 cosmetics: argb_sse2: add const to some casts
1aadf856 cosmetics: alpha_processing_sse2: add const to some casts
1579de3c vwebp: clear canvas at the beginning of each loop
4b9fa5d0 Merge "webp-container-spec: clarify background clear on loop"
4c82284d Updated the near-lossless level mapping.
56039479 webp-container-spec: clarify background clear on loop
19f0ba0e Implement true-motion prediction in SSE2
774d4cb7 make VP8PredLuma16[] array non-const
d7eabb80 Merge "MIPS: dspr2: Added optimization for function CollectHistogram"
fe42739c Use integers for kmin/kmax for simplicity.
b9df35f7 AnimEncode API: kmax=0 should imply all keyframes.
6ce296da MIPS: dspr2: Added optimization for function CollectHistogram
2c906c40 vwebp: remove unnecessary static Help() prototype
be0fd1d5 Merge "dec/vp8: clear 'dither_' on skipped blocks"
e96170fe Merge "vwebp/animation: display last frame on end-of-loop"
0f017b56 vwebp/animation: display last frame on end-of-loop
c86b40cc enc/near_lossless.c: fix alignment
66935fb9 dec/vp8: clear 'dither_' on skipped blocks
b7de7946 Merge "lossless_neon: enable subtract green for aarch64"
77724f70 SSE2 version of GradientUnfilter
416e1cea lossless_neon: enable subtract green for aarch64
72831f6b Speedup AnalyzeAndInit for low effort compression.
a6597483 Speedup Analyze methods for lossless compression.
98c81386 Enable Near-lossless feature.
c6b24543 AnimEncoder API: Fix for kmax=1 and default kmin case.
022d2f88 add SSE2 variants for alpha filtering functions
2db15a95 Temporarily disable encoding of alpha plane with color cache.
1d575ccd Merge "Lossless decoding: Remove an unnecessary if condition."
cafa1d88 Merge "Simplify backward refs calculation for low-effort."
7afdaf84 Alpha coding: reorganize the filter/unfiltering code
4d6d7285 Simplify backward refs calculation for low-effort.
ec0d1be5 Cleaup Near-lossless code.
9814ddb6 Remove the post-transform near-lossless heuristic.
4509e32e Lossless decoding: Remove an unnecessary if condition.
f2ebc4a8 Merge "Regression fix for lossless decoding"
783a8cda Regression fix for lossless decoding
9a062b8e AnimEncoder: Bugfix for kmin = 1 and kmax = 2.
0f027a72 simplify smart RGB->YUV conversion code
0d5b334e BackwardReferencesHashChainFollowChosenPath: remove unused variable
f480d1a7 Fix to near lossless artefacts on palettized images.
d4615d08 Merge changes Ia1686828,I399fda40
cb4a18a7 rename HashChainInit into HashChainReset
f079e487 use uint16_t for chosen_path[]
da091212 MIPS: dspr2: Added optimization for function FTransformWHT
b8c20135 Merge "wicdec: (msvs) quiet some /analyze warnings"
9b228b54 wicdec: (msvs) quiet some /analyze warnings
daeb276a Merge "MIPS: dspr2: Added optimization for MultARGBRow function"
cc087424 Merge "dsp/cpu: (msvs) add include for __cpuidex"
4a82aab5 Merge changes I87544e92,I0bb6cda5
7a191398 dwebp/WritePNG: mark png variables volatile
775dfad2 dwebp: include setjmp.h w/WEBP_HAVE_PNG
47d26be7 dwebp: correct sign in format strings
f0e0677b VP8LEncodeStream: add an assert
c5f7747f VP8LColorCacheCopy: promote an int before shifting
0de5f33e dsp/cpu: (msvs) add include for __cpuidex
7d850f7b MIPS: dspr2: Added optimization for MultARGBRow function
54875293 MIPS: dspr2: added optimization for function QuantizeBlock
4fbe9cf2 dsp/cpu: (msvs) avoid immintrin.h on _M_ARM
3fd59039 simplify/reorganize arguments for CollectColorBlueTransforms
b9e356b9 Disable costly TraceBackwards for method=0.
a7e7caa4 MIPS: dspr2: added optimization for function TransformColorRed
2cb39180 Merge "MIPS: dspr2: added optimization for function TransformColorBlue"
279e6613 Merge "dsp/cpu: add include for _xgetbv() w/MSVS"
b6c0428e dsp/cpu: add include for _xgetbv() w/MSVS
d1c4ffae gif2webp: Move GIF decoding related code to a support library.
07c39559 Merge "AnimEncoder API: Add info in README.mux"
7b161973 MIPS: dspr2: added optimization for function TransformColorBlue
d7c4b02a cpu: fix AVX2 detection for gcc/clang targets
9d299469 AnimEncoder API: Add info in README.mux
d581ba40 follow-up: clean up WebPRescalerXXX dsp function
f8740f0d dsp: s/USE_INTRINSICS/WEBP_USE_INTRINSICS/
ce73abe0 Merge "introduce a separate WebPRescalerDspInit to initialize pointers"
ab66beca introduce a separate WebPRescalerDspInit to initialize pointers
205c7f26 fix handling of zero-sized partition #0 corner case
cbcdd5ff Merge "move rescaler functions to rescaler* files in src/dsp/"
bf586e88 Merge changes I230b3532,Idf3057a7
6dc79dc2 Merge "anim_encode: fix type conversion warnings"
11fce25a Merge "dec_neon: remove returns from void functions"
c4e63f99 Makefile.vc: add gif2webp target
4f43d38c enable NEON for Windows ARM builds
3f6615ac Makefile.vc: add rudimentary Windows ARM support
e7c5954c dec_neon: remove returns from void functions
f79c163b anim_encode: fix type conversion warnings
0f54f1ec Remove gif2webp_util which is no longer needed.
cbcbedd0 move rescaler functions to rescaler* files in src/dsp/
ac79ed19 webpmux: remove experimental fragment handling
e8694d4d mux: remove experimental FRGM parsing
9e92b6ea AnimEncoder API: Optimize single-frame animated images
abbae279 Merge "Move over gif2webp to the new AnimEncoder API."
a28c4b36 MIPS: move WORK_AROUND_GCC define to appropriate place
012d2c60 MIPS: dspr2: added optimization for functions SSEAxB
67720c8b Move over gif2webp to the new AnimEncoder API.
9241ecf4 MIPS: dspr2: added optimization for function Average
9422211d Merge "Tune BackwardReferencesLz77 for low_effort (m=0)."
df40057b Merge "Speedup VP8LGetHistoImageSymbols for low effort (m=0) mode."
ea08466d Tune BackwardReferencesLz77 for low_effort (m=0).
b0b973c3 Speedup VP8LGetHistoImageSymbols for low effort (m=0) mode.
c6d32927 argb_sse2: cosmetics
67f601cd make the 'last_cpuinfo_used' variable names unique
b9489861 AnimEncoder API: Init method for default options.
856f8ec1 Merge "AnimEncoder API: Remove AnimEncoderFrameOptions."
c537514d Merge "AnimEncoder API: GenerateCandidates bugfix."
dc0ce039 Merge "AnimEncoder API: Compute change rectangle for first frame too."
f00b639b Merge "AnimEncoder API: In Assemble(), always set animation parameters."
29ed796c Merge "AnimEncoder lib cleanup: prev to prev canvas not needed."
9f0dd6e5 Merge "WebPAnimEncoder API: Header and implementation"
5e56bbe0 AnimEncoder API: Remove AnimEncoderFrameOptions.
b902c3ea AnimEncoder API: GenerateCandidates bugfix.
ef3c39bb AnimEncoder API: Compute change rectangle for first frame too.
eec423ab AnimEncoder API: In Assemble(), always set animation parameters.
ae1c046e AnimEncoder lib cleanup: prev to prev canvas not needed.
4b997ae4 WebPAnimEncoder API: Header and implementation
72208bec move argb_*.o build target to encoder list
95920538 Merge "multi-thread fix: lock each entry points with a static var"
4c1b300a Merge "SSE2 implementation of VP8PackARGB"
fbcc2004 Merge "add -Wformat-nonliteral and -Wformat-security"
80d950d9 add -Wformat-nonliteral and -Wformat-security
04c20e75 Merge "MIPS: dspr2: added optimization for function Intra4Preds"
a437694a multi-thread fix: lock each entry points with a static var
ca7f60db SSE2 implementation of VP8PackARGB
72d573f6 simplify the PackARGB signature
4e2589ff demux: restore strict fragment flag check
4ba8e074 Merge "webp-container-spec: remove references to fragments"
e752f0a6 Merge "demux: remove experimental FRGM parsing"
f8abb112 Merge changes I109ec4d9,I73fe7743
ae2188a4 MIPS: dspr2: added optimization for function Intra4Preds
1f4b8642 move VP8EncDspARGBInit() call closer to where it's needed
14108d78 dec_neon: add DC8uvNoTop / DC8uvNoLeft
d8340da7 dec_neon: add DC8uv
a66e66c7 webp-container-spec: remove references to fragments
7ce8788b MIPS: dspr2: added optimization for function MakeARGB32
012e623d demux: remove experimental FRGM parsing
87c3d531 method=0: Don't evaluate any predictor
6f4fcb98 Merge "MIPS: dspr2: added optimization for function ImportRow"
24284459 replace unneeded calls to HistogramCopy() by swaps
bdf7b40c MIPS: dspr2: added optimization for function ImportRow
e66a9225 Merge "MIPS: dspr2: added optimization for function ExportRowC"
c279fec1 MIPS: dspr2: added optimization for function ExportRowC
31a9cf64 Speedup WebP lossless compression for low effort (m=0) mode with following: - Disable Cross-Color transform. - Evaluate predictors #11 (paeth), #12 and #13 only.
9275d91c MIPS: dspr2: added optimization for function TrueMotion
26106d66 Merge "enc_neon: fix building with non-Xcode clang (iOS)"
1c4e3efe unroll the kBands[] indirection to remove a dereference in GetCoeffs()
a3946b89 enc_neon: fix building with non-Xcode clang (iOS)
8ed9c00d Merge "simplify the Histogram struct, to only store max_value and last_nz"
bad77571 simplify the Histogram struct, to only store max_value and last_nz
3cca0dc7 MIPS: dspr2: Added optimization for DCMode function
37e395fd MIPS: fix functions to use generic BPS istead of hardcoded value
9475bef4 PickBestUV: fix VP8Copy16x8 invocation
441f273f Merge changes I55f8da52,Id73a1e96
4a279a68 cosmetics: add some missing != NULL comparisons
66ad3725 factorize BPS definition in dsp.h and add VP8Copy16x8
432e5b55 make ALIGN_xxx naming consistent
57606047 encoder: switch BPS to 32 instead of 16
1b66bbe9 MIPS: dspr2: added optimization for function TransformColor_C
c6d0f9e7 histogram: cosmetics
f399d307 Merge changes I6eac17e5,I32d2b514
9de9074c dec_neon: add TM8uv
8e517eca bit_reader/kVP8NewRange: range_t -> uint8_t
e1857139 dsp: initialize VP8PredChroma8 in VP8DspInit()
e0c809ad Move Entropy methods to lossless.c
a96ccf8f iosbuild: add x64_64 simulator support
a0df5510 Remove handling for WEBP_HINT_GRAPH
413dfc0c Move static method definition before its usage.
0f235665 Update BackwardRefsWithLocalCache.
d69e36ec Remove TODOs from lossless encoder code.
fdaac8e0 Optmize VP8LGetBackwardReferences LZ77 references.
2f0e2ba8 MIPS: dspr2: added optimization for function Select
a3e79a46 Merge "WebPEncode: Support encoding same pic twice (even if modified)"
e4f4dddb WebPEncode: Support encoding same pic twice (even if modified)
cbc3fbb4 Merge "Updated VP8LGetBackwardReferences and color cache."
95a9bd85 Updated VP8LGetBackwardReferences and color cache.
54f2c14c MIPS: dspr2: added optimization for function FTransform
aa42f423 MIPS: dspr2: Added optimization for function VP8LSubtractGreenFromBlueAndRed
11a25f75 Merge "FlattenSimilarBlocks should only be tried when blending is possible."
5cccdadf FlattenSimilarBlocks should only be tried when blending is possible.
95ca44a7 MIPS: dspr2: added optimization for Disto4x4
4171b672 backward_references.c: reindent after c8581b0
c8581b06 Optimize BackwardReferences for RLE encoding.
5798eee6 MIPS: dspr2: unfilters bugfix (Ie7b7387478a6b5c3f08691628ae00f059cf6d899)
4167a3f5 Optimize backwardreferences
d18554c3 Merge "webp/types.h: use inline for clang++/-std=c++11"
7489b0e7 gif2webp: Add '-min-size' option to get best compression.
77bdddf0 Speed up BackwardReferences
6638710b webp/types.h: use inline for clang++/-std=c++11
abf04205 Enable entropy based merge histo for (q<100)
572022a3 filters_mips_dsp_r2.c: disable unfilters
a28e21b1 MIPS: dspr2: Added optimization for function ClampedAddSubtractFull
18d5a1ef MIPS: dspr2: added optimization for function ClampedAddSubtractHalf
829a8c19 MIPS: dspr2: added optimization for ITransform
c94ed49e gif2webp: Use the default hint instead of WEBP_HINT_GRAPH.
653ace55 Increase the MAX_COLOR_CACHE_BITS from 9 to 10.
919220c7 Change the logic adjusting the Histogram bits.
53b096c0 Merge "Fix bug in VP8LCalculateEstimateForCacheSize."
e912bd55 Fix bug in VP8LCalculateEstimateForCacheSize.
541d7839 Merge "dec_neon: add RD4 intra predictor"
f8cd0672 Merge "Makefile.vc: add a 'legacy' RTLIBCFG option"
22881c99 dec_neon: add RD4 intra predictor
613d281e update NEWS
1304eb34 Merge "dec_neon: DC4: use pair-wise adds for top row"
34c20c06 Makefile.vc: add a 'legacy' RTLIBCFG option
7083006b Merge "dsp/dec_{neon,sse2}: VE4: normalize variable names"
0db9031c dsp/dec_{neon,sse2}: VE4: normalize variable names
b5bc1530 dec_neon: DC4: use pair-wise adds for top row
5b90d8fe Unify the API between VP8BitWriter and VP8LBitWriter
f7ada560 Merge changes I2e06907b,Ia9ed4ca6,I782282ff
5beb6bf0 Merge "dec_neon: add VE4 intra predictor"
eba6ce06 dec_neon: add DC4 intra predictor
79abfbd9 dec_neon: add TM4 intra predictor
fe395f0e dec_neon: add LD4 intra predictor
32de385e dec_neon: add VE4 intra predictor
72395ba9 Merge "Modify CostModel to allocate optimal memory."
65e5eb8a gif2webp: Support GIF_DISPOSE_RESTORE_PREVIOUS
e4c829ef gif2webp: Handle frames with odd offsets + disposal to background.
c2b5a039 Modify CostModel to allocate optimal memory.
b7a33d7e implement VE4/HE4/RD4/... in SSE2
97c76f1f make VP8PredLuma4[] non-const and initialize array in VP8DspInit()
0ea8c6c2 Merge "PrintReg: output to stderr"
d7ff2f97 Merge "stopwatch.h: fix includes"
f85ec712 PrintReg: output to stderr
54edbf65 stopwatch.h: fix includes
139142e4 Optimize BackwardReferenceHashChainFollowPath.
5f36b68d enc/backward_references.c: fix indent
e0e9960d Merge "sync version numbers to 0.4.2 release"
64ac5144 sync version numbers to 0.4.2 release
c24f8954 Simplify and speedup Backward refs computation.
d1c359ef fix shared object build with -fvisibility=hidden
a4c3a31b WEBP_TSAN_IGNORE_FUNCTION: fix gcc compat warning
f358eeb8 add code for testing random incremental decoding in dwebp
80247291 mark some init function as being safe for thread_sanitizer.
79b5bdbf bit_reader.h: cosmetics: fix a typo
6c673681 Improved near-lossless mode.
0ce27e71 enc_mips32: workaround gcc-4.9 bug
aca1b98f enc/vp8l.c: fix indent
ca005027 Evaluate non-palette compression for palette image
c8a87bb6 AssignSegments: quiet -Warray-bounds warning
32f67e30 Merge "enc_neon: initialize vectors w/vdup_n_u32"
fabc65da 1-3% faster encoding optimizing SSE_NxN functions
7534d716 enc_neon: initialize vectors w/vdup_n_u32
5f813912 Merge "Fix return code of EncodeImageInternal()"
e321abe4 Fix return code of EncodeImageInternal()
f82cb06a optimize palette ordering
f545feee don't set the alpha value for histogram index image
2d9b0a44 add WebPDispatchAlphaToGreen() to dsp
1bd4c2ad Merge "Change Entropy based Histogram Combine heuristic."
e295b8f1 Merge "iosbuild: cleanup"
1be4e760 Merge "iosbuild: output autoconf req. on failure"
d5e498d4 Change Entropy based Histogram Combine heuristic.
47a2d8e1 fix MSVC float->int conversion warning
041956f6 iosbuild: cleanup
767eb402 iosbuild: output autoconf req. on failure
35ad48b8 HistoHeapInit: correct positions allocation size
45d9635f lossless: entropy clustering for high qualities.
dc37df8c fix type warning for VS9_x64
9f7d9e6d iosbuild: make iOS 6 the minimum requirement
fdd6528b Remove unused VP8LDecoder member variable
ea3bba5a Merge "rewrite Disto4x4 in enc_neon.c with intrinsic"
f060dfc4 add lossless incremental decoding support
ab70794d rewrite Disto4x4 in enc_neon.c with intrinsic
d4471637 MIPS: dspr2: added optimization for function FilterLoop24
2aef54d4 Merge "prepare VP8LDecodeImage for incremental decode"
aed0f5a2 Merge "MIPS: dspr2: added optimization for function FilterLoop26"
28630685 prepare VP8LDecodeImage for incremental decode
248f3aed remove br->error_ field
49e15044 MIPS: dspr2: added optimization for function FilterLoop26
38128cb9 iobuild.sh: only install .h files in Headers
c792d412 Premultiply with alpha during U/V downsampling
0cc811d7 gif2webp: Background color correction
d7167ff7 Amend the lossless spec according to issue #205, #206 and #224
b901416b Record the lossless size stats.
cddd3340 Add a WebPExtractAlpha function to dsp
0716a98e fix indent after I0204949917836f74c0eb4ba5a7f4052a4797833b
f9ced95a Optimize lossless decoding for trivial(ARB) codes.
924fcfd9 Merge "webpmux: simplify InitializeConfig()"
c0a462ca webpmux: simplify InitializeConfig()
6986bb5e webpmux: fix indent
f89e1690 webpmux: fix exit status on numeric value parse error
2172cb62 Merge "webpmux: fix loop_count range check"
e3b343ec Merge "examples: warn on invalid numeric parameters"
0e23c487 webpmux: fix loop_count range check
6208338a Merge "fix loop bug in DispatchAlpha()"
d51f3e40 gif2webp: Handle frames with missing  graphic control extension
690b491a fix loop bug in DispatchAlpha()
96d43a87 examples: warn on invalid numeric parameters
3101f537 MIPS: dspr2: added optimization for TransformOne
a6bb9b17 SSE2 for inverse Mult(ARGB)Row and ApplyAlphaMultiply
d84a8ffd Remove default initialization of decoder status.
be70b86c configure: simplify libpng-config invocation
e0a99321 Rectify bug in lossless incremental decoding.
e2502a97 MIPS: dspr2: added optimization for TransformAC3
24e1072a MIPS: dspr2: added optimization for TransformDC
c0e84df8 Merge "Slightly faster lossless decoding (1%)"
8dd28bb5 Slightly faster lossless decoding (1%)
f0103595 MIPS: dspr2: added optimization for ColorIndexInverseTransforms
d3242aee make VP8LSetBitPos() set br->eos_ flag
a9decb55 Lossless decoding: fix eos_ flag condition
3fea6a28 fix erroneous dec->status_ setting
80b8099f MIPS: dspr2: add some specific mips code to commit I2c3f2b12f8df15b785fad5a9c56316e954ae0c53
e5640625 Merge "further refine the COPY_PATTERN optim for DecodeAlpha"
854509fe enc/histogram.c: reindent after f4059d0
34421964 Merge "~3-5% faster encoding optimizing PickBestIntra*()"
865069c1 further refine the COPY_PATTERN optim for DecodeAlpha
a5956228 added C-level optimization for DecodeAlphaData function
187d379d add a fallback to ALPHA_NO_COMPRESSION
a48a2d76 ~3-5% faster encoding optimizing PickBestIntra*()
a6140194 ExUtilReadFromStdin: (windows) open stdin in bin mode
e80eab1f webpmux: (windows) open stdout in binary mode
e9bfb116 cwebp: (windows) open stdout in binary mode
5927e15b example_util: add ExUtilSetBinaryMode
30f3b75b webpmux man page: Clarify some title, descriptions and examples
77d4c7e3 address cosmetic comments from patch #71380
f75dfbf2 Speed up Huffman decoding for lossless
637b3888 dsp/lossless: workaround gcc-4.9 bug on arm
8323a903 dsp.h: collect gcc/clang version test macros
e6c4b52f move static initialization of WebPYUV444Converters[] to the Init function.
49911d4d Merge "fix indentation"
f4059d0c Code cleanup for HistogramRemap.
e632b092 fix indentation
f5c04d64 Merge "add a DispatchAlpha() for SSE2 that handles 8 pixels at a time"
fc98edd9 add a DispatchAlpha() for SSE2 that handles 8 pixels at a time
73d361dd introduce VP8EncQuantize2Blocks to quantize two blocks at a time
0b21c30b MIPS: dspr2: added optimization for EmitAlphaRGB
953acd56 enc_neon: enable QuantizeBlock for aarch64
f4ae1437 MIPS: mips32: code rebase
56977154 MIPS: dspr2: added optimizations for VP8YuvTo*
2523aa73 SmartRGBYUV: fix odd-width problem with pixel replication
ee52dc4e fix some MSVC64 warning about float conversion
3fca851a cpu: check for _MSC_VER before using msvc inline asm
e2a83d71 faster RGB->YUV conversion function (~7% speedup)
de2d03e1 Merge "Add smart RGB->YUV conversion option -pre 4"
3fc4c539 Add smart RGB->YUV conversion option -pre 4
b4dc4069 MIPS: dspr2: added optimization for (un)filters
137e6090 Merge "configure: add work around for gcc-4.9 aarch64 bug"
b61c9cec MIPS: dspr2: Optimization of some simple point-sampling functions
e2b8cec0 configure: add work around for gcc-4.9 aarch64 bug
98c54107 MIPS: mips32r2: added optimization for BSwap32
dab702b3 Update PATENTS to reflect s/VP8/WebM/g
b564f7c7 Merge "MIPS: detect mips32r6 and disable mips32r1 code"
b7e5a5c4 MIPS: detect mips32r6 and disable mips32r1 code
63c2fc02 Correctly use the AC_CANONICAL_* macros
bb07022b Merge "cosmetics"
e300c9d8 cosmetics
0e519eea Merge "cosmetics: remove some extraneous 'extern's"
3ef0f08a Merge "vp8enci.h: cosmetics: fix '*' placement"
4c6dde37 bit_writer: cosmetics: rename kFlush() -> Flush()
f7b4c48b cosmetics: remove some extraneous 'extern's
b47fb00a vp8enci.h: cosmetics: fix '*' placement
b5a36cc9 add -near_lossless [0..100] experimental option
0524d9e5 dsp: detect mips64 & disable mips32 code
d3485d96 cwebp.1: fix quality description placement
29a9fe22 Merge tag 'v0.4.1'
8af27718 update ChangeLog (tag: v0.4.1, origin/0.4.1)
e09e9ff6 Record & log the image pre-processing time.
f59c0b4b iosbuild.sh: specify optimization flags
8d34ea3e update ChangeLog (tag: v0.4.1-rc1)
dbc3da66 makefile.unix: add vwebp.1 to the dist target
89a7c83c update ChangeLog
ffe67ee9 Merge "update NEWS for the next release" into 0.4.1
2def1fe6 gif2webp: dust up the help message
fb668d78 remove -noalphadither option from README/vwebp.1
e49f693b update NEWS for the next release
cd013580 Merge "update AUTHORS" into 0.4.1
268d01eb update AUTHORS
85213b9b bump version to 0.4.1
695f80ae Merge "restore mux API compatibility" into 0.4.1
862d296c restore mux API compatibility
8f6f8c5d remove the !WEBP_REFERENCE_IMPLEMENTATION tweak in Put8x8uv
d713a696 Merge changes If4debc15,I437a5d5f into 0.4.1
c2fc52e4 restore encode API compatibility
793368e8 restore decode API compatibility
b8984f31 gif2webp: fix compile with giflib 5.1.0
222f9b1a gif2webp: simplify giflib version checking
d2cc61b7 Extend MakeARGB32() to accept Alpha channel.
4595b62b Merge "use explicit size of kErrorMessages[] arrays"
157de015 Merge "Actuate memory stats for PRINT_MEMORY_INFO"
fbda2f49 JPEG decoder: delay conversion to YUV to WebPEncode() call
0b747b1b use explicit size of kErrorMessages[] arrays
3398d81a Actuate memory stats for PRINT_MEMORY_INFO
6f3202be Merge "move WebPPictureInit to picture.c"
6c347bbb move WebPPictureInit to picture.c
fb3acf19 fix configure message for multi-thread
40b086f7 configure: check for _beginthreadex
1549d620 reorder the YUVA->ARGB and ARGB->YUVA functions correctly
c6461bfd Merge "extract colorspace code from picture.c into picture_csp.c"
736f2a17 extract colorspace code from picture.c into picture_csp.c
645daa03 Merge "configure: check for -Wformat-security"
abafed86 configure: check for -Wformat-security
fbadb480 split monolithic picture.c into picture_{tools,psnr,rescale}.c
c76f07ec dec_neon/TransformAC3: initialize vector w/vcreate
bb4fc051 gif2webp: Allow single-frame animations
46fd44c1 thread: remove harmless race on status_ in End()
5a1a7264 Merge "configure: check for __builtin_bswapXX()"
6781423b configure: check for __builtin_bswapXX()
6450c48d configure: fix iOS builds
6422e683 VP8LFillBitWindow: enable fast path for 32-bit builds
4f7f52b2 VP8LFillBitWindow: respect WEBP_FORCE_ALIGNED
e458badc endian_inl.h: implement htoleXX with BSwapXX
f2664d1a endian_inl.h: add BSwap16
6fbf5345 Merge "configure: add --enable-aligned"
dc0f479d configure: add --enable-aligned
9cc69e2b Merge "configure: support WIC + OpenGL under mingw64"
257adfb0 remove experimental YUV444 YUV422 and YUV400 code
10f4257c configure: support WIC + OpenGL under mingw64
380cca4f configure.ac: add AC_C_BIGENDIAN
ee70a901 endian_inl.h: add BSwap64
47779d46 endian_inl.h: add BSwap32
d5104b1f utils: add endian_inl.h
58ab6224 Merge "make alpha-detection loop in IsKeyFrame() in good x/y order"
9d562902 make alpha-detection loop in IsKeyFrame() in good x/y order
516971b1 lossless: Remove unaligned read warning
b8b596f6 Merge "configure.ac: add an autoconf version prerequisite"
34b02f8c configure.ac: add an autoconf version prerequisite
e59f5360 neon: normalize vdup_n_* usage
6ee7160d Merge changes I0da7b3d3,Idad2f278,I4accc305
abc02f24 Merge "fix (uncompiled) typo"
bc03670f neon: add INIT_VECTOR4
6c1c632b neon: add INIT_VECTOR3
dc7687e5 neon: add INIT_VECTOR2
4536e7c4 add WebPMuxSetCanvasSize() to the mux API
824eab10 fix (uncompiled) typo
1f3e5f1e remove unused 'shift' argument and QFIX2 define
8e867051 Merge "VP8LoadNewBytes: use __builtin_bswap32 if available"
1b6a2635 Merge "Fix handling of weird GIF with canvas dimension 0x0"
1da3d461 VP8LoadNewBytes: use __builtin_bswap32 if available
1582e402 Fix handling of weird GIF with canvas dimension 0x0
b8811dac Merge "rename interface -> winterface"
db8b8b5f Fix logic in the GIF LOOP-detection parsing
25aaddc8 rename interface -> winterface
5584d9d2 make WebPSetWorkerInterface() check its arguments
a9ef7ef9 Merge "cosmetics: update thread.h comments"
c6af9991 Merge "dust up the help message"
0a8b8863 dust up the help message
a9cf3191 cosmetics: update thread.h comments
27bfeee4 QuantizeBlock SSE2 Optimization:
2bc0dc3e Merge "webpmux: warn when odd frame offsets are used"
3114ebe4 Merge changes Id8edd3c1,Id418eb96,Ide05e3be
c0726634 webpmux: warn when odd frame offsets are used
c5c6b408 Merge "add alpha dithering for lossy"
d5146784 examples/Android.mk: add cwebp
ca0fa7c7 Android.mk: move dwebp to examples/Android.mk
73d8fca0 Android.mk: add ENABLE_SHARED flag
6e93317f muxread: fix out of bounds read
8b0f6a48 Makefile.vc: fix CFLAGS assignment w/HAVE_AVX2=1
bbe32df1 add alpha dithering for lossy
79020767 Merge "make error-code reporting consistent upon malloc failure"
77bf4410 make error-code reporting consistent upon malloc failure
7a93c000 **/Makefile.am: remove unused AM_CPPFLAGS
24e30805 Add an interface abstraction to the WebP worker thread implementation
d6cd6358 Merge "fix orig_rect==NULL case"
2bfd1ffa fix orig_rect==NULL case
059e21c1 Merge "configure: move config.h to src/webp/config.h"
f05fe006 properly report back encoding error code in WebPFrameCacheAddFrame()
32b31379 configure: move config.h to src/webp/config.h
90090d99 Merge changes I7c675e51,I84f7d785
ae7661b3 makefiles: define WEBP_HAVE_AVX2 when appropriate
69fce2ea remove the special casing for res->first in VP8SetResidualCoeffs
6e61a3a9 configure: test for -msse2
b9d2efc6 rename upsampling_mips32.c to yuv_mips32.c
bdfeebaa dsp/yuv: move sse2 functions to yuv_sse2.c
46b32e86 Merge "configure: set WEBP_HAVE_AVX2 when available"
88305db4 Merge "VP8RandomBits2: prevent signed int overflow"
73fee88c VP8RandomBits2: prevent signed int overflow
db4860b3 enc_sse2: prevent signed int overflow
3fdaf4d2 Merge "real fix for longjmp warning"
385e3340 real fix for longjmp warning
230a0555 configure: set WEBP_HAVE_AVX2 when available
a2ac8a42 restore original value_/range_ field order
5e2ee56f Merge "remove libwebpdspdecode dep on libwebpdsp_avx2"
61362db5 remove libwebpdspdecode dep on libwebpdsp_avx2
42c447ae Merge "lossy bit-reader clean-up:"
479ffd8b Merge "remove unused #include's"
9754d39a Merge "strong filtering speed-up (~2-3% x86, ~1-2% for NEON)"
158aff9b remove unused #include's
09545eea lossy bit-reader clean-up:
ea8b0a17 strong filtering speed-up (~2-3% x86, ~1-2% for NEON)
6679f899 Optimize VP8SetResidualCoeffs.
ac591cf2 fix for gcc-4.9 warnings about longjmp + local variables
4dfa86b2 dsp/cpu: NaCl has no support for xgetbv
4c398699 Merge "cwebp: fallback to native webp decode in WIC builds"
33aa497e Merge "cwebp: add some missing newlines in longhelp output"
c9b340a2 fix missing WebPInitAlphaProcessing call for premultiplied colorspace output
57897bae Merge "lossless_neon: use vcreate_*() where appropriate"
6aa4777b Merge "(enc|dec)_neon: use vcreate_*() where appropriate"
0d346e41 Always reinit VP8TransformWHT instead of hard-coding
7d039fc3 cwebp: fallback to native webp decode in WIC builds
d471f424 cwebp: add some missing newlines in longhelp output
bf0e0030 lossless_neon: use vcreate_*() where appropriate
9251c2f6 (enc|dec)_neon: use vcreate_*() where appropriate
399b916d lossy decoding: correct alpha-rescaling for YUVA format
78c12ed8 Merge "Makefile.vc: add rudimentary avx2 support"
dc5b122f try to remove the spurious warning for static analysis
ddfefd62 Makefile.vc: add rudimentary avx2 support
a8911643 Merge "simplify VP8LInitBitReader()"
fdbcd44d simplify VP8LInitBitReader()
7c004287 makefile.unix: add rudimentary avx2 support
515e35cf Merge "add stub dsp/enc_avx2.c"
a05dc140 SSE2: yuv->rgb speed-up for point-sampling
178e9a69 add stub dsp/enc_avx2.c
1b99c09c Merge "configure: add a test for -mavx2"
fe728071 configure: add a test for -mavx2
e46a247c cpu: fix check for __cpuidex availability
176fda26 fix the bit-writer for lossless in 32bit mode
541784c7 dsp.h: add a check for AVX2 / define WEBP_USE_AVX2
bdb151ee dsp/cpu: add AVX2 detection
ab9f2f86 Merge "revamp the point-sampling functions by processing a full plane"
a2f8b289 revamp the point-sampling functions by processing a full plane
ef076026 use decoder's DSP functions for autofilter
2b5cb326 Merge "dsp/cpu: add AVX detection"
df08e67e dsp/cpu: add AVX detection
e2f405c9 Merge "clean-up and slight speed-up in-loop filtering SSE2"
f60957bf clean-up and slight speed-up in-loop filtering SSE2
9fc3ae46 .gitattributes: treat .ppm as binary
3da924b5 Merge "dsp/WEBP_USE_NEON: test for __aarch64__"
c7164490 Android.mk: always include *_neon.c in the build
a577b23a dsp/WEBP_USE_NEON: test for __aarch64__
54bfffca move RemapBitReader() from idec.c to bit_reader code
34168ecb Merge "remove all unused layer code"
f1e77173 remove all unused layer code
b0757db7 Code cleanup for VP8LGetHistoImageSymbols.
5fe628d3 make the token page size be variable instead of fixed 8192
f948d08c memory debug: allow setting pre-defined malloc failure points
ca3d746e use block-based allocation for backward refs storage, and free-lists
1ba61b09 enable NEON intrinsics in aarch64 builds
b9d2bb67 dsp/neon.h: coalesce intrinsics-related defines
b5c75258 iosbuild: add support for iOSv7/aarch64
9383afd5 Reduce number of memory allocations while decoding lossless.
888e63ed Merge "dsp/lossless: prevent signed int overflow in left shift ops"
8137f3ed Merge "instrument memory allocation routines for debugging"
2aa18736 instrument memory allocation routines for debugging
d3bcf72b Don't allocate VP8LHashChain, but treat like automatic object
bd6b8619 dsp/lossless: prevent signed int overflow in left shift ops
b7f19b83 Merge "dec/vp8l: prevent signed int overflow in left shift ops"
29059d51 Merge "remove some uint64_t casts and use."
e69a1df4 dec/vp8l: prevent signed int overflow in left shift ops
cf5eb8ad remove some uint64_t casts and use.
38e2db3e MIPS: MIPS32r1: Added optimization for HistogramAdd.
e0609ade dwebp: fix exit code on webp load failure
bbd358a8 Merge "example_util.h: avoid forward declaring enums"
8955da21 example_util.h: avoid forward declaring enums
6d6865f0 Added SSE2 variants for Average2/3/4
b3a616b3 make HistogramAdd() a pointer in dsp
c8bbb636 dec_neon: relocate some inline-asm defines
4e393bb9 dec_neon: enable intrinsics-only functions
ba99a922 dec_neon: use positive tests for USE_INTRINSICS
69058ff8 Merge "example_util: add ExUtilDecodeWebPIncremental"
a7828e8b dec_neon: make WORK_AROUND_GCC conditional on version
3f3d717a Merge "enc_neon: enable intrinsics-only functions"
de3cb6c8 Merge "move LOCAL_GCC_VERSION def to dsp.h"
1b2fe14d example_util: add ExUtilDecodeWebPIncremental
ca49e7ad Merge "enc_neon: move Transpose4x4 to dsp/neon.h"
ad900abd Merge "fix warning about size_t -> int conversion"
4825b436 fix warning about size_t -> int conversion
42b35e08 enc_neon: enable intrinsics-only functions
f937e012 move LOCAL_GCC_VERSION def to dsp.h
5e1a17ef enc_neon: move Transpose4x4 to dsp/neon.h
c7b92a5a dec_neon: (WORK_AROUND_GCC) delete unused Load4x8
8e5f90b0 Merge "make ExUtilLoadWebP() accept NULL bitstream param."
05d4c1b7 Merge "cwebp: add webpdec"
ddeb6ac8 cwebp: add webpdec
35d7d095 Merge "Reduce memory footprint for encoding WebP lossless."
0b896101 Reduce memory footprint for encoding WebP lossless.
f0b65c9a make ExUtilLoadWebP() accept NULL bitstream param.
9c0a60cc Merge "dwebp: move webp decoding to example_util"
1d62acf6 MIPS: MIPS32r1: Added optimization for HuffmanCost functions.
4a0e7390 dwebp: move webp decoding to example_util
c0220460 Merge "Bugfix: Incremental decode of lossy-alpha"
8c7cd722 Bugfix: Incremental decode of lossy-alpha
7955152d MIPS: fix error with number of registers.
b1dabe37 Merge "Move the HuffmanCost() function to dsp lib"
75b12006 Move the HuffmanCost() function to dsp lib
2772b8bd MIPS: fix assembler error revealed by clang's debug build
6653b601 enc_mips32: fix unused symbol warning in debug
8dec1209 enc_mips32: disable ITransform(One) in debug builds
98519dd5 enc_neon: convert Disto4x4 to intrinsics
fe9317c9 cosmetics:
953b0746 enc_neon: cosmetics
a9fc697c Merge "WIP: extract the float-calculation of HuffmanCost from loop"
3f84b521 Merge "replace some mult-long (vmull_u8) with mult-long-accumulate (vmlal_u8)"
4ae0533f MIPS: MIPS32r1: Added optimizations for ExtraCost functions.
b30a04cf WIP: extract the float-calculation of HuffmanCost from loop
a8fe8ce2 Merge "NEON intrinsics version of CollectHistogram"
95203d2d NEON intrinsics version of CollectHistogram
7ca2e74b replace some mult-long (vmull_u8) with mult-long-accumulate (vmlal_u8)
41c6efbd fix lossless_neon.c
8ff96a02 NEON intrinsics version of FTransform
0214f4a9 Merge "MIPS: MIPS32r1: Added optimizations for FastLog2"
baabf1ea MIPS: MIPS32r1: Added optimizations for FastLog2
3d49871d NEON functions for lossless coding
3fe02915 MIPS: MIPS32r1: Added optimizations for SSE functions.
c503b485 Merge "fix the gcc-4.6.0 bug by implementing alternative method"
abe6f487 fix the gcc-4.6.0 bug by implementing alternative method
5598bdec enc_mips32.c: fix file mode
2b1b4d5a MIPS: MIPS32r1: Add optimization for GetResidualCost
f0a1f3cd Merge "MIPS: MIPS32r1: Added optimization for FTransform"
7231f610 MIPS: MIPS32r1: Added optimization for FTransform
869eaf6c  ~30% encoding speedup: use NEON for QuantizeBlock()
f758af6b enc_neon: convert FTransformWHT to intrinsics
7dad095b MIPS: MIPS32r1: Added optimization for Disto4x4 (TTransform)
2298d5f3 MIPS: MIPS32r1: Added optimization for QuantizeBlock
e88150c9 Merge "MIPS: MIPS32r1: Add optimization for ITransform"
de693f25 lossless_neon: disable VP8LConvert* functions
4143332b NEON intrinsics for encoding
0ca2914b MIPS: MIPS32r1: Add optimization for ITransform
71bca5ec dec_neon: use vst_lane instead of vget_lane
bf061052 Intrinsics NEON version of TransformOne
19c6f1ba Merge "dec_neon: use vld?_lane instead of vset?_lane"
7a94c0cf upsampling_neon: drop NEON suffix from local functions
d14669c8 upsampling_sse2: drop SSE2 suffix from local functions
2ca42a4f enc_sse2: drop SSE2 suffix from local functions
d038e619 dec_sse2: drop SSE2 suffix from local functions
fa52d752 dec_neon: use vld?_lane instead of vset?_lane
c520e77d cosmetic: fix long line
4b0f2dae Merge "add intrinsics NEON code for chroma strong-filtering"
e351ec07 add intrinsics NEON code for chroma strong-filtering
aaf734b8 Merge "Add SSE2 version of forward cross-color transform"
c90a902e Add SSE2 version of forward cross-color transform
bc374ff3 Use histogram_bits to initalize transform_bits.
2132992d Merge "Add strong filtering intrinsics (inner and outer edges)"
5fbff3a6 Add strong filtering intrinsics (inner and outer edges)
d4813f0c Add SSE2 function for Inverse Cross-color Transform
26029568 dec_neon: add strong loopfilter intrinsics
cca7d7ef Merge "add intrinsics version of SimpleHFilter16NEON()"
1a05dfa7 windows: fix dll builds
d6c50d8a Merge "add some colorspace conversion functions in NEON"
4fd7c82e SSE2 variants of Subtract-Green: Rectify loop condition
97e5fac3 add some colorspace conversion functions in NEON
b9a7a45f add intrinsics version of SimpleHFilter16NEON()
daccbf40 add light filtering NEON intrinsics
af444608 fix typo in STORE_WHT
6af6b8e1 Tune HistogramCombineBin for large images.
af93bdd6 use WebPSafe[CM]alloc/WebPSafeFree instead of [cm]alloc/free
51f406a5 lossless_sse2: relocate VP8LDspInitSSE2 proto
0f4f721b separate SSE2 lossless functions into its own file
514fc251 VP8LConvertFromBGRA: use conversion function pointers
6d2f3527 dsp/dec: TransformDCUV: use VP8TransformDC
defc8e1b Merge "fix out-of-bound read during alpha-plane decoding"
fbed3643 Merge "dsp: reuse wht transform from dec in encoder"
d8467084 Merge "Add SSE2 version of ARGB -> BGR/RGB/... conversion functions"
207d03b4 fix out-of-bound read during alpha-plane decoding
d1b33ad5 2-5% faster trellis with clang/MacOS (and ~2-3% on ARM)
369c26dd Add SSE2 version of ARGB -> BGR/RGB/... conversion functions
df230f27 dsp: reuse wht transform from dec in encoder
80e218d4 Android.mk: fix build with APP_ABI=armeabi-v7a-hard
59daf083 Merge "cosmetics:"
53622008 cosmetics:
3e7f34a3 AssignSegments: quiet array-bounds warning
3c2ebf58 Merge "UpdateHistogramCost: avoid implicit double->float"
cf821c82 UpdateHistogramCost: avoid implicit double->float
312e638f Extend the search space for GetBestGreenRedToBlue
1c58526f Fix few nits
fef22704 Optimize and re-structure VP8LGetHistoImageSymbols
068b14ac Optimize lossless decoding.
5f0cfa80 Do a binary search to get the optimum cache bits.
24ca3678 Merge "allow 'cwebp -o -' to emit output to stdout"
e12f874e allow 'cwebp -o -' to emit output to stdout
2bcad89b allow some more stdin/stout I/O
84ed4b3a fix cwebp.1 typos after patch #69199
65b99f1c add a -z option to cwebp, and WebPConfigLosslessPreset() function
30176619 4-5% faster trellis by removing some unneeded calculations.
687a58ec histogram.c: reindent after b33e8a0
06d456f6 Merge "~3-4% faster lossless encoding"
c60de260 ~3-4% faster lossless encoding
42eb06fc Merge "few cosmetics after patch #69079"
82af8264 few cosmetics after patch #69079
b33e8a05 Refactor code for HistogramCombine.
ca1bfff5 Merge "5-10% encoding speedup with faster trellis (-m 6)"
5aeeb087 5-10% encoding speedup with faster trellis (-m 6)
82ae1bf2 cosmetics: normalize VP8GetCPUInfo checks
e3dd9243 Merge "Refactor GetBestPredictorForTile for future tuning."
206cc1be Refactor GetBestPredictorForTile for future tuning.
3cb84062 Merge "speed-up trellis quant (~5-10% overall speed-up)"
b66f2227 Merge "lossy encoding: ~3% speed-up"
4287d0d4 speed-up trellis quant (~5-10% overall speed-up)
390c8b31 lossy encoding: ~3% speed-up
9a463c4a Merge "dec_neon: convert TransformWHT to intrinsics"
e8605e96 Merge "dec_neon: add ConvertU8ToS16"
4aa3e412 MIPS: MIPS32r1: rescaler bugfix
c16cd99a Speed up lossless encoder.
9d6b5ff1 dec_neon: convert TransformWHT to intrinsics
2ff0aae2 dec_neon: add ConvertU8ToS16
77a8f919 fix compilation with USE_YUVj flag
4acbec1b Merge changes I3b240ffb,Ia9370283,Ia2d28728
2719bb7e dec_neon: TransformAC3: work on packed vectors
b7b60ca1 dec_neon: add SaturateAndStore4x4
b7685d73 Rescale: let ImportRow / ExportRow be pointer-to-function
e02f16ef dec_neon.c: convert TransformDC to intrinsics
9cba963f add missing file
8992ddb7 use static clipping tables
0235d5e4 1-2% faster quantization in SSE2
b2fbc36c fix VC12-x64 warning
6e37cb94 Merge "cosmetics: backward_references.c: reindent after a7d2ee3"
a42ea974 cosmetics: backward_references.c: reindent after a7d2ee3
6c327442 Merge "fix missing __BIG_ENDIAN__ definition on some platform"
a8b6aad1 fix missing __BIG_ENDIAN__ definition on some platform
fde2904b Increase initial buffer size for VP8L Bit Writer.
a7d2ee39 Optimize cache estimate logic.
7fb6095b Merge "dec_neon.c: add TransformAC3"
bf182e83 VP8LBitWriter: use a bit-accumulator
3f40b4a5 Merge "MIPS: MIPS32r1: clang macro warning resolved"
1684f4ee WebP Decoder: Mark some truncated bitstreams as invalid
acbedac4 MIPS: MIPS32r1: clang macro warning resolved
228e4877 dec_neon.c: add TransformAC3
393f89b7 Android.mk: avoid gcc-specific flags with clang
32aeaf11 revamp VP8LColorSpaceTransform() a bit
0c7cc4ca Merge "Don't dereference NULL, ensure HashChain fully initialized"
391316fe Don't dereference NULL, ensure HashChain fully initialized
926ff402 WEBP_SWAP_16BIT_CSP: remove code dup
1d1cd3bb Fix decode bug for rgbA_4444/RGBA_4444 color-modes.
939e70e7 update AUTHORS file
8934a622 cosmetics: *_mips32.c
dd438c9a MIPS: MIPS32r1: Optimization of some simple point-sampling functions. PATCH [6/6]
53520911 Added support for calling sampling functions via pointers.
d16c6974 MIPS: MIPS32r1: Optimization of filter functions. PATCH [5/6]
04336fc7 MIPS: MIPS32r1: Optimization of function TransformOne. PATCH [4/6]
92d8fc7d MIPS: MIPS32r1: Optimization of function WebPRescalerImportRow. PATCH [3/6]
bbc23ff3 parse one row of intra modes altogether
a2f608f9 Merge "MIPS: MIPS32r1: Optimization of function WebPRescalerExportRow. [2/6]"
88230854 MIPS: MIPS32r1: Optimization of function WebPRescalerExportRow. [2/6]
c5a5b028 decode mt+incremental: fix segfault in debug builds
9882b2f9 always use fast-analysis for all methods.
000adac0 Merge "autoconf: update ax_pthread.m4"
2d2fc37d update .gitignore
5bf4255a Merge "Make it possible to avoid automagic dependencies"
c1cb1933 disable NEON for arm64 platform
73a304e9 Make it possible to avoid automagic dependencies
4d493f8d MIPS: MIPS32r1: Decoder bit reader function optimized. PATCH [1/6]
c741183c make WebPCleanupTransparentArea work with argb picture
5da18552 add a decoding option to flip image vertically
00c3c4e1 Merge "add man/vwebp.1"
2c6bb428 add man/vwebp.1
ea59a8e9 Merge "Merge tag 'v0.4.0'"
7574bed4 fix comments related to array sizes
0b5a90fd dwebp.1: fix option formatting
effcb0fd Merge tag 'v0.4.0'
7c76255d autoconf: update ax_pthread.m4
fff2a11b make -short work with -print_ssim, -print_psnr, etc.
68e7901d update ChangeLog (tag: v0.4.0-rc1, tag: v0.4.0, origin/0.4.0)
256e4333 update NEWS description with new general features
29625340 Merge "gif2webp: don't use C99 %zu" into 0.4.0
3b9f9dd0 gif2webp: don't use C99 %zu
b5b2e3c7 cwebp: fix metadata output w/lossy+alpha
ad26df1a makefile.unix: clean up libgif2webp_util.a
c3b45570 update Changelog
ca841121 Merge "bump version to 0.4.0" into 0.4.0
8c524db8 bump version to 0.4.0
eec2398c update AUTHORS & .mailmap
b9bbf6a1 update NEWS for 0.4.0
c72e0811 Merge "dec/webp.c: don't wait for data before reporting w/h"
5ad65314 dec/frame.c: fix formatting
f7fc4bc8 dec/webp.c: don't wait for data before reporting w/h
66a32af5 Merge "NEON speed up"
26d842eb NEON speed up
f307f98b Merge "webpmux: let -- stop parameter parsing"
fe051da7 Merge "README: add a section on gif2webp"
6fd2bd62 Merge "manpage pedantry"
4af19007 README: add a section on gif2webp
6f36ade9 manpage pedantry
f9016cb9 README: update dwebp options
b4fa0a47 webpmux: let -- stop parameter parsing
a9a20acf gif2webp: Add a multi-threaded encode option
495bef41 fix bug in TrellisQuantize
605a7127 simplify __cplusplus ifdef
33109f99 Merge "drop: ifdef __cplusplus checks from C files"
7f9de0b9 Merge changes I994a5587,I8467bb71,I13b50688,I1e2c9c7b
5459030b gif2webp: let -- stop parameter parsing
a4b0aa06 vwebp: let -- stop parameter parsing
98af68fe cwebp: let -- stop parameter parsing
a33831e2 dwebp: let -- stop parameter parsing
36301249 add some checks on error paths
ce4c7139 Merge "autoconf: add --disable-wic"
5227d991 drop: ifdef __cplusplus checks from C files
f6453559 dwebp.1: fix typo
f91034f2 Merge "cwebp: print metadata stats when no output file is given"
d4934553 gif2webp: Backward compatibility for giflib version <= 4.1.3
4c617d32 gif2webp: Disable output of ICC profile by default
73b731fb introduce a special quantization function for WHT
41c0cc4b Make Forward WHT transform use 32bit fixed-point calculation
a3359f5d Only compute quantization params once
70490437 cwebp: print metadata stats when no output file is given
d513bb62 * fix off-by-one zthresh calculation * remove the sharpening for non luma-AC coeffs * adjust the bias a little bit to compensate for this
ad9dec0c Merge "cosmetics: dwebp: fix local function name format"
f737f037 Merge "dwebp: remove a dead store"
3c3a70da Merge "makefile.unix: install binaries in $(DESTDIR)/bin/"
150b655f Merge "Android.mk: add some release compile flags"
dbebd33b cosmetics: dwebp: fix local function name format
27749951 dwebp: remove a dead store
a01e04fe autoconf: add --disable-wic
5009b227 makefile.unix: install binaries in $(DESTDIR)/bin/
bab30fca Merge "fix -print_psnr / ssim options"
ebef7fb3 fix -print_psnr / ssim options
cb637855 Merge "fix bug due to overzealous check in WebPPictureYUVAToARGB()"
8189885b Merge "EstimateBestFilter: use an int to iterate WEBP_FILTER_TYPE"
4ad7d335 Android.mk: add some release compile flags
c12e2369 cosmetics: fix a few typos
6f104034 fix bug due to overzealous check in WebPPictureYUVAToARGB()
3f6c35c6 EstimateBestFilter: use an int to iterate WEBP_FILTER_TYPE
cc55790e Merge changes I8bb7a4dc,I2c180051,I021a014f,I8a224a62
c536afb5 Merge "cosmetics: fix some typos"
cbdd3e6e add a -dither dithering option to the decoder
e8124012 Updated iosbuild.sh for XCode 5.x
4931c329 cosmetics: fix some typos
05aacf77 mux: add some missing casts
617d9348 enc/vp8l: add a missing cast
46db2865 idec: add some missing casts
b524e336 ErrorStatusLossless: correct return type
cb261f79 fix a descaling bug for vertical/horizontal U/V interpolation
bcb3955c Merge changes I48968468,I181bc736
73f52133 gif2webp: Add a mixed compression mode
6198715e demux: split chunk parsing from ParseVP8X
d2e3f4e6 demux: add a tail pointer for chunks
87cffcc3 demux: cosmetics: s/has_frames/is_animation/
e18e6677 demux: strictly enforce the animation flag
c4f39f4a demux: cosmetics: remove a useless break
61cb884d demux: (non-exp) fail if the fragmented flag is set
ff379db3 few % speedup of lossless encoding
df3649a2 remove all disabled code related to P-frames
6d0cb3de Merge "gif2webp: kmin = 0 should suppress key-frame addition."
36555983 gif2webp: kmin = 0 should suppress key-frame addition.
7708e609 Merge "detect flatness in blocks and favor DC prediction"
06b1503e Merge "add comment about the kLevelsFromDelta[][] LUT generation"
5935259c add comment about the kLevelsFromDelta[][] LUT generation
e3312ea6 detect flatness in blocks and favor DC prediction
ebc9b1ee Merge "VPLBitReader bugfix: Catch error if bit_pos > LBITS too."
96ad0e0a VPLBitReader bugfix: Catch error if bit_pos > LBITS too.
a014e9c9 tune quantization biases toward higher precision
1e898619 add helpful PrintBlockInfo() function
596a6d73 make use of 'extern' consistent in function declarations
c8d48c6e Merge "extract random utils to their own file util/random.[ch]"
98aa33cf extract random utils to their own file util/random.[ch]
432a723e Merge "swig: add basic go bindings"
fab618b5 Merge "rename libwebp.i -> libwebp.swig"
e4e7fcd6 swig: add basic go bindings
d3408720 Merge "fast auto-determined filtering strength"
f8bfd5cd fast auto-determined filtering strength
ac0bf951 small clean-up in ExpandMatrix()
1939607e rename libwebp.i -> libwebp.swig
43148b6c filtering: precompute ilimit and hev_threshold
18f992ec simplify f_inner calculation a little
241d11f1 add missing const
86c0031e add a 'format' field to WebPBitstreamFeatures
dde91fde Demux: Correct the extended format validation
5d6c5bd2 add entry for '-resize' option in cwebp's man
7c098d18 Use some gamma-curve range compression when computing U/V average
0b2b0504 Use deterministic random-dithering during RGB->YUV conversion
8a2fa099 Add a second multi-thread method
7d6f2da0 Merge "up to 20% faster multi-threaded decoding"
266f63ea Merge "libwebp.jar: build w/Java 1.6 for Android compat"
0532149c up to 20% faster multi-threaded decoding
38efdc2e Simplify the gif2webp tool: move the optimization details to util
de899516 libwebp.jar: build w/Java 1.6 for Android compat
cb221552 Decode a full row of bitstream before reconstructing
dca8a4d3 Merge "NEON/simple loopfilter: avoid q4-q7 registers"
9e84d901 Merge "NEON/TransformWHT: avoid q4-q7 registers"
fc10249b NEON/simple loopfilter: avoid q4-q7 registers
2f09d63e NEON/TransformWHT: avoid q4-q7 registers
77585a2b Merge "use a macrofunc for setting NzCoeffs bits"
d155507c Merge "use HINT_GRAPH as image_hint for gif source"
9c561646 Merge "only print GIF_DISPOSE_WARNING once"
05879865 use HINT_GRAPH as image_hint for gif source
0b28d7ab use a macrofunc for setting NzCoeffs bits
f9bbc2a0 Special-case sparse transform
00125196 gif2webp: detect and flatten uniformly similar blocks
0deaf0fa only print GIF_DISPOSE_WARNING once
6a8c0eb7 Merge "small optimization in segment-smoothing loop"
f7146bc1 small optimization in segment-smoothing loop
5a7533ce small gif2webp fix
4df0c89e Merge changes Ic697660c,I27285521
5b2e6bd3 Android.mk: add a dwebp target
f910a84e Android.mk: update build flags
63f9aba4 special-case WHT transform when there's only DC
80911aef Merge "7-8% faster decoding by rewriting GetCoeffs()"
606c4304 gif2webp: Improved compression for lossy animated WebP
fb887f7f gif2webp: Different kmin/kmax defaults for lossy and lossless
2a981366 7-8% faster decoding by rewriting GetCoeffs()
92d47e4c improve VP8L signature detection by checking the version bits too
5cd43e43 Add -incremental option to dwebp
54b8e3f6 webpmux: DisplayInfo(): remove unnecessary error checks.
40ae3520 fix memleak in WebPIDelete()
d9662658 mux.h doc: WebPMuxGetFrame() can return WEBP_MUX_MEMORY_ERROR too.
0e6747f8 webpmux -info: display dimensions and has_alpha per frame
d78a82c4 Sanity check for underflow
8498f4bf Merge "remove -Wshadow warnings"
e89c6fc8 Avoid a potential memleak
3ebe1757 Merge "break down the proba 4D-array into some handy structs"
6a44550a break down the proba 4D-array into some handy structs
2f5e8934 remove -Wshadow warnings
bf3a29b3 Merge "add proper WEBP_HAVE_GIF and WEBP_HAVE_GL flags"
2b0a7593 Merge "fix some warnings from static analysis"
22dd07ce mux.h: Some doc corrections
79ff0346 add proper WEBP_HAVE_GIF and WEBP_HAVE_GL flags
d51f45f0 fix some warnings from static analysis
d134307b fix conversion warning on MSVC
d538cea8 gif2webp: Support a 'min' and 'max'  key frame interval
80b54e1c allow search with token buffer loop and fix PARTITION0 problem
b7d4e042 add VP8EstimateTokenSize()
10fddf53 enc/quant.c: silence a warning
399cd456 Merge "fix compile error on ARM/gcc"
9f24519e encoder: misc rate-related fixes
c663bb21 Merge "simplify VP8IteratorSaveBoundary() arg passing"
fa46b312 Demux.h: Correct a method name reference
f8398c9d fix compile error on ARM/gcc
f691f0e4 simplify VP8IteratorSaveBoundary() arg passing
42542be8 up to 6% faster encoding with clang compiler
93402f02 multi-threaded segment analysis
7e2d6595 Merge "remove the PACK() bit-packing tricks"
c13fecf9 remove the PACK() bit-packing tricks
2fd091c9 Merge "use NULL for lf_stats_ testing, not bool"
b11c9d62 dwebp: use default dct_method
4bb8465f Merge "(de)mux.h: wrap pseudo-code in /* */"
cfb56b17 make -pass option work with token buffers
5416aab4 (de)mux.h: wrap pseudo-code in /* */
35dba337 use NULL for lf_stats_ testing, not bool
733a7faa enc->Iterator memory cleanup
e81fac86 Add support for "no blend" in webpmux binary
3b80bc48 gif2webp: Separate out each step into a method
bef7e9cc Add doc precision about demux object keeping pointers to data.
61405a14 dwebp: enable stdout output with WIC
6eabb886 Merge "Animated WebP: add "do no blend" option to spec"
be20decb fix compilation for BITS 24
e58cc137 Merge "dwebp: s/unsigned char/uint8_t/"
72501d43 dwebp: s/unsigned char/uint8_t/
2c9633e8 Merge "gif2webp: Insert independent frames at regular intervals."
f0d6a14b gif2webp: Insert independent frames at regular intervals.
b25a6fbf yuv.h: fix indent
ede3602e Merge "cosmetics: fix indent"
3a65122a dwebp: fix stdout related output
388a7249 cosmetics: fix indent
4c7322c8 Merge "dsp: msvc compatibility"
d50c7e32 Merge "5-7% faster SSE2 versions of YUV->RGB conversion functions"
b8ab7847 Merge "simplify upsampler calls: only allow 'bottom' to be NULL"
df6cebfa 5-7% faster SSE2 versions of YUV->RGB conversion functions
ad6ac32d simplify upsampler calls: only allow 'bottom' to be NULL
a5e8afaf output to stdout if file name is "-"
f358450f dsp: msvc compatibility
43a7c8eb Merge "cosmetics"
4c5f19c1 Merge "bit_reader.h: cosmetics"
f72fab70 cosmetics
14dd5e78 fix const-ness
b20aec49 Merge "Support for 'do not blend' option in vwebp"
dcf65222 Support for 'do not blend' option in vwebp
d5bad033 Animated WebP: add "do no blend" option to spec
a2f5f73d Merge "Support for "Do not blend" in mux and demux libraries"
e081f2f3 Pack code & extra_bits to Struct (VP8LPrefixCode).
6284854b Support for "Do not blend" in mux and demux libraries
f486aaa9 Merge "slightly faster ParseIntraMode"
d1718632 slightly faster ParseIntraMode
3ceca8ad bit_reader.h: cosmetics
69257f70 Create LUT for PrefixEncode.
988b7084 add WebPWorkerExecute() for convenient bypass
06e24987 Merge "VP8EncIterator clean-up"
de4d4ad5 VP8EncIterator clean-up
7bbe9529 Merge "cosmetics: thread.c: drop a redundant comment"
da411485 cosmetics: thread.c: drop a redundant comment
feb4b6e6 thread.h: #ifdef when checking WEBP_USE_THREAD
8924a3a7 thread.c: drop WebPWorker prefix from static funcs
1aed8f2a Merge "fix indent"
4038ed15 fix indent
1693fd9b Demux: A new state WEBP_DEMUX_PARSE_ERROR
8dcae8b3 fix rescaling-with-alpha inaccuracy
11249abf Merge changes I9b4dc36c,I4e0eef4d
52508a1f Mux: support parsing unknown chunks within a frame/fragment.
05db0572 WebPMuxSetChunk: remove unused variable
8ba1bf61 Stricter check for presence of alpha when writing lossless images
a03c3516 Demux: WebPIterator now also denotes if the frame has alpha.
6df743a3 Decoder: handle fragments case correctly too.
faa4b07e Support for unknown chunks in mux library
7d60bbc6 Speed up HashChainFindCopy function.
66740140 Speedup Alpha plane encoding.
b7346a1e 0.1 % speedup to decoding
c606182e webp-container-spec: Tighten language added by last
a34a5029 pngdec: output error messages from libpng
e84c625d Merge "Detect canvas and image size mismatch in decoder."
f626fe2e Detect canvas and image size mismatch in decoder.
f5fbdee0 demux: stricter image bounds check
30c8158a add extra assert in Huffman decode code
8967b9f3 SSE2 for lossless decoding (critical) functions.
699d80ea Jump-lookup for Huffman coding
c34307ab fix some VS9 warnings about type conversion
eeada35c pngdec: add missing include
54b65108 gif2webp: If aligning to even offsets, extra pixels should be transparent
0bcf5ce3 Merge "remove a malloc() in case we're using only FILTER_NONE for alpha"
2c07143b remove a malloc() in case we're using only FILTER_NONE for alpha
a4d5f59d Faster lossless decoding
fd53bb75 Merge "alternate LUT-base reverse-bits code"
d1c166ef Merge "Container spec: a clarification on background color."
fdb91779 Rename a method
5e967532 Container spec: a clarification on background color.
30e77d0f Merge branch '0.3.0'
1b631e29 alternate LUT-base reverse-bits code
24cc307a ~20% faster lossless decoding
313d853f Speedup for decoding lossless WebP photographs:
24ee098a change the bytes_per_pixels_ field into more evocative use_8b_decode
2a04b034 update ChangeLog (tag: v0.3.1-rc2, tag: v0.3.1)
7288950b Regression fix for alpha channels using color cache:
2e377b53 wicdec: silence a format warning
ad9e42a6 muxedit: silence some uninitialized warnings
3307c163 Don't set alpha-channel to 0xff for alpha->green uplift
5130770c Merge "wicdec: silence a format warning"
a37eff47 Regression fix for alpha channels using color cache:
241cf99b Merge "muxedit: silence some uninitialized warnings"
c8f9c84d Regression fix for alpha unfiltering:
14cd5c6c muxedit: silence some uninitialized warnings
a368db81 dec/vp8l: quiet vs9 x64 type conversion warning
ffae9f31 wicdec: silence a format warning
8cf0701e Alpha encoding: never filter in case of NO_COMPRESSION
825e73b1 update ChangeLog (tag: v0.3.1-rc1)
abf6f691 update NEWS
5a92c1a5 bump version to 0.3.1
86daf77c store top Y/U/V samples in packed fashion
67bc353e Revert "add WebPBlendAlpha() function to blend colors against background"
068db59e Intertwined decoding of alpha and RGB
38cc0114 Simplify forward-WHT + SSE2 version
3fa595a5 Support decoding upto given row in DECODE_DATA_FUNC
520f005f DequantizeLevels(): Add 'row' and 'num_rows' args
47374b82 Alpha unfilter for given set of rows
f32097e0 probe input file and quick-check for WebP format.
a2aed1d0 configure: improve gl/glut library test
c7e89cbb update copyright text
a00380d2 configure: remove use of AS_VAR_APPEND
a94a88dd fix EXIF parsing in PNG
a71e5d84 add doc precision for WebPPictureCopy() and WebPPictureView()
8287012e remove datatype qualifier for vmnv
e1908430 fix a memory leak in gif2webp
0b18b9ee fix two minor memory leaks in webpmux
db5095d5 remove some cruft from swig/libwebp.jar
850e956f README: update swig notes
bddd9b0a swig/python: add minimal documentation
d573a8d5 swig: add python encode support
6b931875 swig/java: reduce wrapper function code duplication
6fe536f4 swig/java: rework uint8_t typemap
a2ea4643 Fix the bug in ApplyPalette.
7bb28d2a webp/lossless: fix big endian BGRA output
f036d4bf Speed up ApplyPalette for ARGB pixels.
8112c8cf remove some warnings:
cc128e0b Further reduce memory to decode lossy+alpha images
07db70d2 fix for big-endian
eda8a7de gif2webp: Fix signed/unsigned comparison mismatch
31f346fe Makefile.vc: fix libwebpdemux dll variable typo
6c76d28e swig: add python (decode) support
b4f5bb6c swig: cosmetics
498d4dd6 WebP-Lossless encoding improvements.
26e72442 swig: ifdef some Java specific code
8ecec686 configure: add warning related flags
e676b043 configure: add GLUT detection; build vwebp
b0ffc437 Alpha decoding: significantly reduce memory usage
20aa7a8d configure: add --enable-everything
b8307cc0 configure.ac: add some helper macros
980e7ae9 Remove the gcc compilation comments
7f25ff99 gif2webp: Fix ICC and XMP support
d8e53211 Add missing name to AUTHORS
11edf5e2 Demux: Fix a potential memleak
c7b92184 don't forward declare enums
7a650c6a prevent signed int overflow in left shift ops
31bea324 add precision about dynamic output reallocation with IDecoder
c22877f7 Add incremental support for extended format files
5051245f Makefile.vc: have 'all' target build everything
8191deca Makefile.vc: flags cleanup
b9d74735 Makefile.vc: drop /FD flag
5568dbcf update gitignore
f4c7b654 WebPEncode: An additional check. Start VP8EncLoop/VP8EncTokenLoop only if VP8EncStartAlpha succeeded.
1fb04bec pngdec: Avoid a double-free.
dcbb1ca5 add WebPBlendAlpha() function to blend colors against background
bc9f5fbe configure.ac: add AM_PROG_AR for automake >= 1.12
bf867bf2 Tuned cross_color parameter (step) for lower qual
90e2ec5a Merge "probe input file and quick-check for WebP format."
7180d7ff Merge "update copyright text"
830f72b7 probe input file and quick-check for WebP format.
2ccf58d6 configure: improve gl/glut library test
d640614d update copyright text
c2113ad4 Merge "configure: remove use of AS_VAR_APPEND"
9326a56f configure: remove use of AS_VAR_APPEND
ea63d619 fix a type warning on VS9 x86
bec11092 fix EXIF parsing in PNG
b6e65f3d Merge "fix warnings for vs9 x64"
438946dc fix warnings for vs9 x64
f4710e3b collect macroblock reconstruction data in VP8MBData struct
23d28e21 add doc precision for WebPPictureCopy() and WebPPictureView()
518f2cd7 cosmetics: gif2webp: fix indent
af358e68 Merge "remove datatype qualifier for vmnv"
3fe91635 remove datatype qualifier for vmnv
764fdffa fix a memory leak in gif2webp
3e59a74d fix two minor memory leaks in webpmux
47b9862f Merge "README: update swig notes"
325d15ff remove some cruft from swig/libwebp.jar
4a7627c2 README: update swig notes
5da81e33 Merge "swig/python: add minimal documentation"
f39e08f2 Merge "swig: add python encode support"
6ca4a3e3 Merge "swig/java: reduce wrapper function code duplication"
8f8702b0 Merge "swig/java: rework uint8_t typemap"
91413be2 reduce memory for VP8MB and remove bitfields use
7413394e Fix the memory leak in ApplyFilters.
2053c2cf simplify the alpha-filter testing loop
825b64db swig/python: add minimal documentation
14677e11 swig: add python encode support
a5c297c8 swig/java: reduce wrapper function code duplication
ad4a367d swig/java: rework uint8_t typemap
0d25876b use uint8_t for inv_palette[]
afa3450c Fix the bug in ApplyPalette.
2d6ac422 Merge "webp/lossless: fix big endian BGRA output"
2ca83968 webp/lossless: fix big endian BGRA output
742110cc Speed up ApplyPalette for ARGB pixels.
2451e47d misc code cleanup
83db4043 Merge "swig: add python (decode) support"
eeeea8b5 Merge "swig: cosmetics"
d5f9b8f3 Merge "libwebp: fix vp8 encoder mem alloc offsetting"
d8edd835 libwebp: fix vp8 encoder mem alloc offsetting
8983b83e remove use of bit-fields in VP8FInfo
87a4fca2 remove some warnings:
ba8f74e2 Merge "fix for big-endian"
a65067fa Merge "Further reduce memory to decode lossy+alpha images"
64c84486 Further reduce memory to decode lossy+alpha images
332130b9 Mux: make a few methods static
44370617 fix for big-endian
5199eab5 Merge "add uncompressed TIFF output support"
a3aede97 add uncompressed TIFF output support
f975b67f Merge "gif2webp: Fix signed/unsigned comparison mismatch"
5fbc734b Merge "GetFeatures: Detect invalid VP8X/VP8/VP8L data"
d5060c87 Merge "mux.h: A comment fix + some consistency fixes"
352d0dee GetFeatures: Detect invalid VP8X/VP8/VP8L data
3ef79fef Cosmetic: "width * height"
043e1ae4 gif2webp: Fix signed/unsigned comparison mismatch
5818cff7 mux.h: A comment fix + some consistency fixes
1153f888 Merge "swig: ifdef some Java specific code"
3eeedae1 Makefile.vc: fix libwebpdemux dll variable typo
f980faf4 swig: add python (decode) support
7f5f42bb swig: cosmetics
8eae188a WebP-Lossless encoding improvements.
c7247c4c swig: ifdef some Java specific code
4cb234d5 Merge "Mux: make ValidateForSingleImage() method static"
ed6f5308 Merge "Add GetCanvasSize() method to mux"
1d530c9a Mux: make ValidateForSingleImage() method static
bba4c2b2 configure: add warning related flags
fffefd18 Add GetCanvasSize() method to mux
732da8d0 Merge "configure: add GLUT detection; build vwebp"
0e513f7a configure: add GLUT detection; build vwebp
55d1c150 Merge "Alpha decoding: significantly reduce memory usage"
13d99fb5 Merge "configure: add --enable-everything"
2bf698fe Merge "configure.ac: add some helper macros"
edccd194 Alpha decoding: significantly reduce memory usage
3cafcc9a configure: add --enable-everything
4ef14477 configure.ac: add some helper macros
a4e1cdbb Remove the gcc compilation comments
6393fe4b Cosmetic fixes
9c4ce971 Simplify forward-WHT + SSE2 version
878b9da5 fix missed optim
00046171 VP8GetInfo(): Check for zero width or height.
9bf31293 align VP8Encoder::nz_ allocation
5da165cf fix CheckMode() signature
0ece07dc Merge "explicitly pad bitfields to 32-bits"
9dbc9d19 explicitly pad bitfields to 32-bits
5369a80f Merge "prevent signed int overflow in left shift ops"
70e39712 Merge "cosmetics: remove unnecessary ';'s"
d3136ce2 Merge "don't forward declare enums"
b26e5ad5 gif2webp: Fix ICC and XMP support
46089b20 Add missing name to AUTHORS
94328d64 Demux: Fix a potential memleak
96e948d7 don't forward declare enums
f4f90880 prevent signed int overflow in left shift ops
0261545e cosmetics: remove unnecessary ';'s
7ebdf110 Merge "Fix few missing comparisons to NULL"
1579989e Fix few missing comparisons to NULL
ea1b21cf Cleaned up VP8GetHeaders() so that it parses only frame header
b66caee4 dwebp: add support for BMP output
ff885bfe add precision about dynamic output reallocation with IDecoder
79241d5a Merge "Makefile.vc: have 'all' target build everything"
ac1c729b Merge "Makefile.vc: flags cleanup"
118a055c Merge "Makefile.vc: drop /FD flag"
ecad0109 Merge "update gitignore"
a681b4f4 Rename PRE_VP8 state to WEBP_HEADER
ead4d478 Add incremental support for extended format files
69d0f926 Makefile.vc: have 'all' target build everything
52967498 Makefile.vc: flags cleanup
c61baf0c Makefile.vc: drop /FD flag
3a15125d update gitignore
5167ca47 Merge "WebPEncode: An additional check. Start VP8EncLoop/VP8EncTokenLoop only if VP8EncStartAlpha succeeded."
67708d67 WebPEncode: An additional check. Start VP8EncLoop/VP8EncTokenLoop only if VP8EncStartAlpha succeeded.
b68912af pngdec: Avoid a double-free.
82abbe12 Merge "configure.ac: add AM_PROG_AR for automake >= 1.12"
e7d9548c add WebPBlendAlpha() function to blend colors against background
ed4dc717 configure.ac: add AM_PROG_AR for automake >= 1.12
df4a406d Merge branch '0.3.0'
1e0d4b8c Update ChangeLog (tag: v0.3.0-rc7, tag: v0.3.0)
d52b405d Cosmetic fixes
6cb4a618 misc style fix
68111ab0 add missing YUVA->ARGB automatic conversion in WebPEncode()
e9a7990b Cosmetic fixes
403bfe82 Container spec: Clarify frame disposal
2aaa423b Merge "add missing YUVA->ARGB automatic conversion in WebPEncode()"
07d87bda add missing YUVA->ARGB automatic conversion in WebPEncode()
142c4629 misc style fix
3e7a13a0 Merge "Container spec: clarify the background color field" into 0.3.0
14af7745 container doc: add a note about the 'ANMF' payload
cc635efa Container spec: clarify the background color field
e3e33949 container doc: move RIFF description to own section
4299f398 libwebp/mux: fix double free
33f9a692 Merge "demux: keep a frame tail pointer; used in AddFrame" into 0.3.0
a2a7b959 use WebPDataCopy() instead of re-coding it.
6f18f12f demux: keep a frame tail pointer; used in AddFrame
e5af49e9 add doc precision about WebPParseHeaders() return codes
db46daab Merge "Makefile.vc: fix dynamic builds" into 0.3.0
53c77afc Merge "gif2webp: Bgcolor fix for a special case" into 0.3.0
a5ebd143 gif2webp: Bgcolor fix for a special case
6378f238 Merge "vwebp/animation: fix background dispose" into 0.3.0
3c8eb9a8 fix bad saturation order in QuantizeBlock
04c7a2ec vwebp/animation: fix background dispose
81a50695 Makefile.vc: fix dynamic builds
5f25c396 update ChangeLog (tag: v0.3.0-rc6)
14d42af2 examples: don't use C99 %zu
5ccf1fe5 update ChangeLog
2560c243 update NEWS
f43bafc3 Merge changes Iecccb09c,If5ee9fd2,I3e181ce4 into 0.3.0
a788644f dwebp: warn when decoding animated webp's
302efcdb Decode: return more meaningful error for animation
ad452735 WebPBitstreamFeatures: add has_animation field
783dfa49 disable FRGM decoding for good in libwebpmux
4b956be0 Update ChangeLog
ad8b86d7 update NEWS
3e084f63 Merge "demux cosmetics: comments/rename internal function" into 0.3.0
d3f8c621 Merge "move WebPFeatureFlags declaration" into 0.3.0
7386fe50 Merge "libwebp{demux,mux}: install mux_types.h" into 0.3.0
d6cd4e90 Merge "bump decode abi" into 0.3.0
17f8da5c bump decode abi
97684ae2 Merge "add doc precision about WebPDemuxPartial()" into 0.3.0
f933fd2a move WebPFeatureFlags declaration
289bc47b libwebp{demux,mux}: install mux_types.h
224e8d46 add doc precision about WebPDemuxPartial()
4c18e80c demux cosmetics: comments/rename internal function
7cfd1bf1 update AUTHORS
401f7b85 Merge "speed-up lossless (~3%) with ad-hoc histogram cost evaluation" into 0.3.0
1fc8ffca Merge "makefile.unix: dist related changes" into 0.3.0
8a89c6ed Merge changes I466c377f,Ib761ebd3,I694857fc into 0.3.0
f4ffb2d5 speed-up lossless (~3%) with ad-hoc histogram cost evaluation
723847d5 gif2webp: only write error messages to stderr
701b9e2a makefile.unix: dist related changes
bb85b437 Merge "update NEWS" into 0.3.0
59423a24 gif2webp: fix crash on open failure with libgif5
9acb17de gif2webp: silence a unused param warning
7d9fdc23 Merge "README updates" into 0.3.0
5621934e Merge "build: fix install race on shared headers" into 0.3.0
70809d89 Merge "bump version to 0.3.0" into 0.3.0
d851cd1d demux: make the parse a bit more strict
28bb4107 update NEWS
cef93882 bump version to 0.3.0
9048494d build: fix install race on shared headers
1e67e8ef README updates
42b611a4 Merge "configure: drop experimental from mux/demux" into 0.3.0
096a8e32 Merge "vwebp: add color profile support" into 0.3.0
ddfee5dc vwebp: add color profile support
0d6927d3 Merge "Mark fragment options as experimental in webpmux" into 0.3.0
5dbd4030 Mark fragment options as experimental in webpmux
a0a6648c configure: drop experimental from mux/demux
ee65bad8 Merge "add support for BITS > 32" into 0.3.0
744930db add support for BITS > 32
7dd288f0 cwebp: fix build
19a8dd01 Merge "Makefile.vc: add vwebp.exe target" into 0.3.0
50eeddad Merge "examples: normalize icc related program arguments" into 0.3.0
757f637f Merge "Makefile.vc: add libwebpdecoder target" into 0.3.0
b65c4b7c Makefile.vc: add libwebpdecoder target
f8db7b4a Merge "vwebp: replace doubles w/floats where appropriate" into 0.3.0
d99aa56f Makefile.vc: add vwebp.exe target
013023e7 vwebp: replace doubles w/floats where appropriate
9b3db894 README.mux: add version reference
7b6a26cf Merge "cwebp: output metadata statistics" into 0.3.0
d8dc72a0 examples: normalize icc related program arguments
7bfc9056 Merge "make alpha unfilter work in-place" into 0.3.0
0037b2d2 Merge "add LUT-free reference code for YUV->RGB conversion." into 0.3.0
166bf744 Merge "demux: disable fragment parsing" into 0.3.0
126974b4 add LUT-free reference code for YUV->RGB conversion.
0aef3ebd make alpha unfilter work in-place
14ef5005 Merge "Remove 'status: experimental' from container spec" into 0.3.0
d40c98e1 Merge "webpmux binary: tiny style fix" into 0.3.0
0bc42689 cwebp: output metadata statistics
bc039803 Merge "autoconf: normalize experimental define" into 0.3.0
d1e21b13 Remove 'status: experimental' from container spec
7681bb96 webpmux binary: tiny style fix
a3dd3d0f avoid installing example_util.h
252320e2 demux: disable fragment parsing
537bde05 autoconf: normalize experimental define
5e338e0b Merge changes I33e8a613,I8e8a7b44 into 0.3.0
d9d0ea1b Merge changes If21e3ec7,I991fc30b into 0.3.0
627f5ca6 automake: add reference to libwebp for mux/demux
eef73d07 don't consolidate proba stats too often
05ec4cc2 libwebp{,decoder}.pc: add pthread flags
1bfcf5bf add libwebpmux.pc
26ca843d add libwebpdemux.pc
69e25906 Merge "Tune Lossless compression for lower qualities."
0478b5d2 Tune Lossless compression for lower qualities.
39f7586f add a mention of parallel alpha encoding in the NEWS
5a21d967 Merge "1.5x-2x faster encoding for method 3 and up"
9bfbdd14 1.5x-2x faster encoding for method 3 and up
27dc741b Correct frame options order in README.mux
be2fd173 Mux: fix a scenario with bad ANMF/FRGM size
19eb012c Merge "Demux: Add option to get frame count using GetI()"
7368b8cb Merge "WebPGetFeatures() out of if condition for clarity."
f604c9a4 Merge "fix windows build"
153f94e8 fix windows build
847b4924 Merge "vwebp: use magenta for 'i'nfo display"
25ea46bd Merge "vwebp: add keyboard shortcuts to help output"
bea7ccaf vwebp: use magenta for 'i'nfo display
8fab161a webpmux: correct -frame param order in help output
03cc23d6 vwebp: add keyboard shortcuts to help output
068eba8d Demux: Add option to get frame count using GetI()
988b8f56 WebPGetFeatures() out of if condition for clarity.
6933d910 Merge "gif2webp: Be lenient about background color index."
4d0f7c55 Merge "WebPGetFeatures() behavior change:"
fdeeb01d gif2webp: Be lenient about background color index.
ad250320 Merge "multi-threaded alpha encoding for lossy"
4e32d3e1 Merge "fix compilation of token.c"
f817930a multi-threaded alpha encoding for lossy
88050351 fix compilation of token.c
fc816219 code using the actual values for num_parts_, not the ones from config
72655350 Merge "move the config check from .c to .h"
dd9e76f7 move the config check from .c to .h
956b217a WebPGetFeatures() behavior change:
df02e4ce WebPDemuxGetI behavior change:
633c004d Merge "rebalance method tools (-m) for methods [0..4]"
58ca6f65 rebalance method tools (-m) for methods [0..4]
7648c3cc Merge "describe rd-opt levels introduce VP8RDLevel enum"
67fb1003 Merge "autoconf: enable silent-rules by default"
a5042a32 GetVersion() methods for mux and demux
5189957e describe rd-opt levels introduce VP8RDLevel enum
4e094ace autoconf: enable silent-rules by default
b7eaa85d inline VP8LFastLog2() and VP8LFastSLog2 for small values
5cf7792e split quant_levels.c into decoder and encoder version
e5d3ffe2 Merge "Update code example in README.mux"
ac5a9156 Update code example in README.mux
38a91e99 Add example code snippet for demux API
5f557f3c README.mux: add info about Demux API and vwebp
c0ba0903 backward_references: avoid signed integer overflow
943386db disable SSE2 for now
9479fb7d lossless encoding speedup
ec2030a8 merge two lines together
b67956c0 Merge "Remove ReadOneBit() and ReadSymbolUnsafe()"
1667bded Remove ReadOneBit() and ReadSymbolUnsafe()
3151669b wicdec + dwebp cosmetics: normalize formatting
92668da6 change default filtering parameters:   * type is now 'strong'   * strength is now '60'
b7490f85 introduce WEBP_REFERENCE_IMPLEMENTATION compile option
33838857 faster decoding (3%-6%)
5c3e381b Merge "add a -jpeg_like option"
c2311046 remove unused declaration of VP8Zigzag
36152957 Merge "wicdec: add alpha support for paletted formats"
c9f16490 wicdec: add alpha support for paletted formats
1262f81e Merge "wicdec: silence some warnings"
e7ea61eb wicdec: silence some warnings
23c0f354 fix missing intptr_t->int cast for MSVC
e895059a add a -jpeg_like option
1f803f64 Merge "Tune alpha quality mapping to more reasonable values."
1267d498 Tune alpha quality mapping to more reasonable values.
043076e2 Merge "speed-up lossless in BackwardTrace"
f3a44dcd remove one malloc from TraceBackwards()
0fc1a3a0 speed-up lossless in BackwardTrace
7c732e59 cwebp: centralize WebPCleanupTransparentArea()
7381254e Merge "wicdec: add ICC profile extraction"
e83ff7de wicdec: add ICC profile extraction
146c6e3b Merge "cosmetics: pngdec: normalize default label location"
a8f549d7 Merge "manpages: italicize option parameters"
e118db83 Merge "encode.h: note the need to free() WebPMemoryWriter"
1dfee6db cosmetics: pngdec: normalize default label location
14c38200 manpages: italicize option parameters
7defbfad encode.h: note the need to free() WebPMemoryWriter
88d382a0 cwebp: cleanup after memory_writer
12d6cecf fix extra space in dwebp.1 man
b01681a9 Fix for demuxer frame iteration:
56c12aa6 Demuxer creation fix:
66c810bc add a -yuv option to dwebp (very similar to -pgm)
841a3ba5 Merge "Remove -Wshadow warnings."
8fd02527 Merge "upsampling_neon.c: fix build"
6efed268 Remove -Wshadow warnings.
60904aa6 Merge "allow WebPINewRGB/YUVA to be passed a NULL output buffer."
b7adf376 allow WebPINewRGB/YUVA to be passed a NULL output buffer.
27f8f742 upsampling_neon.c: fix build
06b9cdf1 gitignore: add IOS related directories
f112221e Merge "Fix more comments for iobuild.sh"
fe4d25dd Fix more comments for iobuild.sh
1de3e252 Merge "NEON optimised yuv to rgb conversion"
090b708a NEON optimised yuv to rgb conversion
daa06476 Merge "Add ios build script for building iOS library."
79fe39e2 Add ios build script for building iOS library.
126c035f remove some more -Wshadow warnings
522e9d61 Merge "cwebp: enable '-metadata'"
76ec5fa1 cwebp: enable '-metadata'
aeb91a9d Merge "cosmetics: break a few long lines"
be7c96b0 cosmetics: break a few long lines
cff8ddb6 Merge "add libwebpdecoder.pc"
93148ab8 Merge "libwebp.pc.in: detab"
6477f955 Merge "Makefile.vc: normalize path separator"
bed1ed7c add libwebpdecoder.pc
46168b2d libwebp.pc.in: detab
a941a346 Fixed few nits in the build files.
dd7a49b2 Makefile.vc: normalize path separator
9161be86 Merge "cwebp: extract WIC decoding to its own module"
08e7c58e Merge "Provide an option to build decoder library."
0aeba528 Provide an option to build decoder library.
757ebcb1 catch malloc(0)/calloc(0) with an assert
152ec3d2 Merge "handle malloc(0) and calloc(0) uniformly on all platforms"
a452a555 cwebp: extract WIC decoding to its own module
2b252a53 Merge "Provide option to swap bytes for 16 bit colormodes"
94a48b4b Provide option to swap bytes for 16 bit colormodes
42f8f934 handle malloc(0) and calloc(0) uniformly on all platforms
8b2152c5 Merge "add an extra assert to check memory bounds"
0d19fbff remove some -Wshadow warnings
cd22f655 add an extra assert to check memory bounds
8189feda Merge "Add details and reference about the YUV->RGB conversion"
1d2702b1 Merge "Formatting fixes in lossless bitstream spec"
8425aaee Formatting fixes in lossless bitstream spec
a556cb1a Add details and reference about the YUV->RGB conversion
d8f21e0b add link to SSIM description on Wikipedia
18e9167e Merge "WebP-lossless spec clarifications:"
98e25b9b Merge "cwebp: add -metadata option"
f01c2a53 WebP-lossless spec clarifications:
f4a97970 Merge "Disto4x4 and Disto16x16 in NEON"
47b7b0ba Disto4x4 and Disto16x16 in NEON
7eaee9f1 cwebp: add -metadata option
36c52c2c tiffdec: use toff_t for exif ifd offset
7c8111e4 Merge "cwebp/tiffdec: add TIFF metadata extraction"
e6409adc Remove redundant include from dsp/lossless code.
1ab5b3a7 Merge "configure: fix --with-gifincludedir"
03c749eb configure: fix --with-gifincludedir
8b650635 multiple libgif versions support for gif2webp
476e293f gif2webp: Use DGifOpenFileName()
b50f277b tiffdec: correct format string
2b9048e3 Merge "tiffdec: check error returns for width/height"
a1b5a9a3 Merge "cwebp/tiff: use the first image directory"
079423f5 tiffdec: check error returns for width/height
d62824af Merge "cwebp/jpegdec: add JPEG metadata extraction"
03afaca4 Merge "cwebp: add PNG metadata extraction"
2c724968 cwebp/jpegdec: add JPEG metadata extraction
dba64d91 cwebp: add PNG metadata extraction
1f075f89 Lossless spec corrections/rewording/clarifications
2914ecfd cwebp/tiffdec: add TIFF metadata extraction
d82a3e33 More corrections/clarifications in lossless spec:
bd002557 cwebp/tiff: use the first image directory
df7aa076 Merge "Cleanup around jpegdec"
0f57dcc3 decoding speed-up (~1%)
bcec339b Lossless bitstream clarification:
6bf20874 add examples/metadata.c
207f89c0 Merge "configure: add libwebpdemux status to summary"
1bd287a6 Cleanup around jpegdec
91455679 Merge "cosmetics: use '== 0' in size checks"
d6b88b76 cosmetics: use '== 0' in size checks
d3dace2f cosmetics: jpegdec
2f69af73 configure: add libwebpdemux status to summary
1c1c5646 cwebp: extract tiff decoding to its own module
6a871d66 cwebp: extract jpeg decoding to its own module
2ee228f9 cwebp: extract png decoding to its own module
4679db00 Merge "cwebp: add metadata framework"
63aba3ae cwebp: add metadata framework
931bd516 lossless bitstream: block size bits correction
e4fc4c1c lossless bitstream: block size bits correction
d65ec678 fix build, move token.c to src/enc/
657f5c91 move token buffer to its own file (token.c)
c34a3758 introduce GetLargeValue() to slim-fast GetCoeffs().
d5838cd5 faster non-transposing SSE2 4x4 FTransform
f76191f9 speed up GetResidualCost()
ba2aa0fd Add support for BITS=24 case
2e7f6e8e makefile.unix: Dependency on libraries
dca84219 Merge "Separate out mux and demux code and libraries:"
23782f95 Separate out mux and demux code and libraries:
bd56a01f configure: add summary output
90e5e319 dwebp manual: point to webpmux, gif2webp.
540790ca gif2webp.c: add a note about prerequisites
d1edf697 cwebp man page: meaning of '-q' for lossy/lossless
79efa1d0 Add man page for gif2webp utility
2243e40c Merge "gif2webp build support with autoconf tools"
c40efca1 gif2webp build support with autoconf tools
6523e2d4 WebP Container:
4da788da Merge "simplify the fwd transform"
42c3b550 simplify the fwd transform
41a6ced9 user GLfloat instead of float
b5426119 fix indentation
68f282f7 * handle offset in anim viewer 'vwebp' * fix gif2webp to handle disposal method and odd offset correctly
118cb312 Merge "add SSE2 version of Sum of Square error for 16x16, 16x8 and 8x8 case"
8a7c3cc8 Merge "Change the order of -frame argument to be more natural"
99e0a707 Merge "Simplify the texture evaluation Disto4x4()"
0f923c3f make the bundling work in a tmp buffer
e5c3b3f5 Simplify the texture evaluation Disto4x4()
48600084 Change the order of -frame argument to be more natural
35bfd4c0 add SSE2 version of Sum of Square error for 16x16, 16x8 and 8x8 case
a7305c2e Clarification for unknown chunks
4c4398e2 Refine WebP Container Spec wrt unknown chunks.
2ca642e0 Rectify WebPMuxGetFeatures:
7caab1d8 Some cosmetic/comment fixes.
60b2651a Merge "Write a GIF to WebP converter based on libgif."
c7127a4d Merge "Add NEON version of FTransformWHT"
11b27212 Write a GIF to WebP converter based on libgif.
e9a15a37 ExUtilWriteFile() to write memory segment to file
74356eb5 Add a simple cleanup step in mux assembly:
51bb1e5d mux.h: correct WebPDemuxSelectFragment() prototype
22a0fd9d Add NEON version of FTransformWHT
fa30c863 Update mux code to match the spec wrt animation
d9c5fbef by-pass Analysis pass in case segments=1
d2ad4450 Merge changes Ibeccffc3,Id1585b16
5c8be251 Merge "Chunk fourCCs for XMP/EXIF"
a00a3daf Use 'frgm' instead of 'tile' in webpmux parameters
81b8a741 Design change in ANMF and FRGM chunks:
f903cbab Chunk fourCCs for XMP/EXIF
812933d6 Tune performance of HistogramCombine
52ad1979 Animation specification in container spec
001b9302 Image fragment specification in container spec
391f9db9 Ordering of description of bits in container spec
d5735776 Metadata specification in container spec
1c4609b1 Merge commit 'v0.2.1'
0ca584cb Merge "Color profile specification in container spec"
e8b41ad1 add NEON asm version for WHT inverse transform
af6f0db2 Color profile specification in container spec
a61a824b Merge "Add NULL check in chunk APIs"
0e8b7eed fix WebPPictureView() unassigned strides
75e5f17e ARM/NEON: 30% encoding speed-up
02b43568 Add NULL check in chunk APIs
a0770727 mux struct naming
6c66dde8 Merge "Tune Lossless encoder"
ab5ea217 Tune Lossless encoder
74fefc8c Update ChangeLog (tag: v0.2.1, origin/0.2.0)
92f8059c Rename some chunks:
3bb4bbeb Merge "Mux API change:"
d0c79f05 Mux API change:
abc06044 Merge "update NEWS" into 0.2.0
57cf313b update NEWS
25f585c4 bump version to 0.2.1
fed7c048 libwebp: validate chunk size in ParseOptionalChunks
552cd9bc cwebp (windows): fix alpha image import on XP
b14fea99 autoconf/libwebp: enable dll builds for mingw
4a8fb272 [cd]webp: always output windows errors
d6621580 fix double to float conversion warning
72b96a69 cwebp: fix jpg encodes on XP
734f762a VP8LAllocateHistogramSet: fix overflow in size calculation
f9cb58fb GetHistoBits: fix integer overflow
b30add20 EncodeImageInternal: fix uninitialized free
3de58d77 fix the -g/O3 discrepancy for 32bit compile
77aa7d50 fix the BITS=8 case
e5970bda Make *InitSSE2() functions be empty on non-SSE2 platform
ef5cc47e make *InitSSE2() functions be empty on non-SSE2 platform
c4ea259d make VP8DspInitNEON() public
8344eadf Merge "libwebp: validate chunk size in ParseOptionalChunks"
4828bb93 Merge "cwebp (windows): fix alpha image import on XP"
30763333 libwebp: validate chunk size in ParseOptionalChunks
70481898 AccumulateLSIM: fix double -> float warnings
eda8ee4b cwebp (windows): fix alpha image import on XP
c6e98658 Merge "add EXPERIMENTAL code for YUV-JPEG colorspace"
f0360b4f add EXPERIMENTAL code for YUV-JPEG colorspace
f86e6abe add LSIM metric to WebPPictureDistortion()
c3aa215a Speed up HistogramCombine for lower qualities.
1765cb1c Merge "autoconf/libwebp: enable dll builds for mingw"
a13562e8 autoconf/libwebp: enable dll builds for mingw
9f469b57 typo: no_fancy -> no_fancy_upsampling
1a27f2f8 Merge "fix double to float conversion warning"
cf1e90de Merge "cwebp: fix jpg encodes on XP"
f2b5d19b [cd]webp: always output windows errors
e855208c fix double to float conversion warning
ecd66f77 cwebp: fix jpg encodes on XP
7b3eb372 Tune lossless compression to get better gains.
ce8bff45 Merge "VP8LAllocateHistogramSet: fix overflow in size calculation"
ab5b67a1 Merge "EncodeImageInternal: fix uninitialized free"
7fee5d12 Merge "GetHistoBits: fix integer overflow"
a6ae04d4 VP8LAllocateHistogramSet: fix overflow in size calculation
80237c43 GetHistoBits: fix integer overflow
8a997235 EncodeImageInternal: fix uninitialized free
0b9e6829 minor cosmetics
a792b913 fix the -g/O3 discrepancy for 32bit compile
73ba4357 Merge "detect and merge similar segments"
fee66275 detect and merge similar segments
0c44f415 src/webp/*.h: don't forward declare enums in C++
d7a5ac86 vwebp: use demux interface
931e0ea1 Merge "replace 'typedef struct {} X;" by "typedef struct X X; struct X {};""
8f216f7e remove cases of equal comparison for qsort()
28d25c82 replace 'typedef struct {} X;" by "typedef struct X X; struct X {};"
2afee60a speed up for ARM using 8bit for boolean decoder
5725caba new segmentation algorithm
2cf1f815 Merge "fix the BITS=8 case"
12f78aec fix the BITS=8 case
6920c71f fix MSVC warnings regarding implicit uint64 to uint32 conversions
f6c096aa webpmux binary: Rename 'xmp' option to 'meta'
ddfe871a webpmux help correction
b7c55442 Merge "Make *InitSSE2() functions be empty on non-SSE2 platform"
1c04a0d4 Common APIs for chunks metadata and color profile.
2a3117a1 Merge "Create WebPMuxFrameInfo struct for Mux APIs"
5c3a7231 Make *InitSSE2() functions be empty on non-SSE2 platform
7c6e60f4 make *InitSSE2() functions be empty on non-SSE2 platform
c7eb4576 make VP8DspInitNEON() public
ab3234ae Create WebPMuxFrameInfo struct for Mux APIs
e3990fd8 Alignment fixes
e55fbd6d Merge branch '0.2.0'
4238bc0a Update ChangeLog (tag: v0.2.0)
c655380c dec/io.c: cosmetics
fe1958f1 RGBA4444: harmonize lossless/lossy alpha values
681cb30a fix RGBA4444 output w/fancy upsampling
f06c1d8f Merge "Alignment fix" into 0.2.0
f56e98fd Alignment fix
6fe843ba avoid rgb-premultiply if there's only trivial alpha values
528a11af fix the ARGB4444 premultiply arithmetic
a0a48855 Lossless decoder fix for a special transform order
62dd9bb2 Update encoding heuristic w.r.t palette colors.
6f4272b0 remove unused ApplyInverseTransform()
93bf0faa Update ChangeLog (tag: v0.2.0-rc1)
5934fc59 update AUTHORS
014a711d update NEWS
43b0d610 add support for ARGB -> YUVA conversion for lossless decoder
33705ca0 bump version to 0.2.0
c40d7ef1 fix alpha-plane check + add extra checks
a06f8023 MODE_YUVA: set alpha to opaque if the image has none
52a87dd7 Merge "silence one more warning" into 0.2.0
3b023093 silence one more warning
f94b04f0 move some RGB->YUV functions to yuv.h
4b71ba03 README: sync [cd]webp help output
c9ae57f5 man/dwebp.1: add links to output file format details
292ec5cc quiet a few 'uninitialized' warnings
4af3f6c4 fix indentation
9b261bf5 remove the last NOT_HAVE_LOG2 instances
323dc4d9 remove use of log2(). Use VP8LFastLog2() instead.
8c515d54 Merge "harness some malloc/calloc to use WebPSafeMalloc and WebPSafeCalloc" into 0.2.0
d4b4bb02 Merge changes I46090628,I1a41b2ce into 0.2.0
bff34ac1 harness some malloc/calloc to use WebPSafeMalloc and WebPSafeCalloc
a3c063c7 Merge "extra size check for security" into 0.2.0
5e796300 Merge "WebPEncode: clear stats at the start of encode" into 0.2.0
f1edf62f Merge "rationalize use of color-cache" into 0.2.0
c1933317 extra size check for security
906be657 rationalize use of color-cache
dd1c3873 Add image-hint for low-color images.
4eb7aa64 Merge "WebPCheckMalloc() and WebPCheckCalloc():" into 0.2.0
80cc7303 WebPCheckMalloc() and WebPCheckCalloc():
183cba83 check VP8LBitWriterInit return
cbfa9eec lossless: fix crash on user abort
256afefa cwebp: exit immediately on version mismatch
475d87d7 WebPEncode: clear stats at the start of encode
a7cc7291 fix type and conversion warnings
7d853d79 add stats for lossless
d39177b7 make QuantizeLevels() store the sum of squared error
5955cf5e replace x*155/100 by x*101581>>16
7d732f90 make QuantizeLevels() store the sum of squared error
e45a446a replace x*155/100 by x*101581>>16
159b75d3 cwebp output size consistency:
cbee59eb Merge commit 'v0.1.99'
1889e9b6 dwebp: report -alpha option
3bc3f7c0 Merge "dwebp: add PAM output support" into 0.2.0
d919ed06 dwebp: add PAM output support
85e215d3 README/manpages/configure: update website link
c3a207b9 Update ChangeLog (tag: v0.1.99)
d1fd7826 Merge "add extra precision about default values and behaviour" into 0.2.0
efc826e0 add extra precision about default values and behaviour
9f29635d header/doc clean up
ff9fd1ba Makefile.vc: fix webpmux.exe *-dynamic builds
8aacc7b0 remove INAM, ICOP, ... chunks from the test webp file.
2fc13015 harmonize authors as "Name (mail@address)"
4a9f37b7 Merge "update NEWS" into 0.2.0
7415ae13 makefile.unix: provide examples/webpmux target
ce82cedc update NEWS
641e28e8 Merge "man/cwebp.1: wording, change the date" into 0.2.0
c37c23e5 README: cosmetics
3976dcd5 man/cwebp.1: wording, change the date
3e5bbe1c Merge "rename 'use_argb_input' to 'use_argb'" into 0.2.0
ce90847a Merge "add some padding bytes areas for later use" into 0.2.0
2390dabc Merge "fixing the findings by Frederic Kayser to the bitstream spec" into 0.2.0
02751591 add a very crude progress report for lossless
a4b9b1c6 Remove some unused enum values.
dd108176 rename 'use_argb_input' to 'use_argb'
90516ae8 add some padding bytes areas for later use
d03b2503 fixing the findings by Frederic Kayser to the bitstream spec
ce156afc add missing ABI compatibility checks
9d45416a Merge "Doc: container spec text tweaks" into 0.2.0
4e2e0a8c Doc: container spec text tweaks
f7f16a29 add ABI compatibility check
2a775570 Merge "swig: add WebPEncodeLossless* wrappers" into 0.2.0
a3ec6225 mux.h: remove '* const' from function parameters
31426eba encode.h: remove '* const' from function parameters
9838e5d5 decode.h: remove '* const' from function parameters
4972302d swig: add WebPEncodeLossless* wrappers
9ff00cae bump encoder/decoder versions
c2416c9b add lossless quick encoding functions to the public API
4c1f5d64 Merge "NEWS: mention decode_vp8.h is no longer installed" into 0.2.0
6cb2277d NEWS: mention decode_vp8.h is no longer installed
d5e5ad63 move decode_vp8.h from webp/ to dec/
8d3b04a2 Merge "header clean-up" into 0.2.0
02201c35 Merge "remove one malloc() by making color_cache non dynamic" into 0.2.0
d708ec14 Merge "move MIN/MAX_HISTO_BITS to format_constants.h" into 0.2.0
ab2da3e9 Merge "add a malloc() check" into 0.2.0
2d571bd8 add a malloc() check
7f0c178e remove one malloc() by making color_cache non dynamic
6569cd7c Merge "VP8LFillBitWindow: use 64-bit path for msvc x64 builds" into 0.2.0
23d34f31 header clean-up
2a3ab6f9 move MIN/MAX_HISTO_BITS to format_constants.h
985d3da6 Merge "shuffle variables in HashChainFindCopy" into 0.2.0
cdf885c6 shuffle variables in HashChainFindCopy
c3b014db Android.mk: add missing lossless files
8c1cc6b5 makefile.unix dist: explicitly name installed includes
7f4647ee Merge "clarify the colorspace naming and byte ordering of decoded samples" into 0.2.0
cbf69724 clarify the colorspace naming and byte ordering of decoded samples
857650c8 Mux: Add WebPDataInit() and remove WebPImageInfo
ff771e77 don't install webp/decode_vp8.h
596dff78 VP8LFillBitWindow: use 64-bit path for msvc x64 builds
3ca7ce98 Merge "doc: remove non-finalized chunk references" into 0.2.0
1efaa5a3 Merge "bump versions" into 0.2.0
51fa13e1 Merge "README: update cwebp help output" into 0.2.0
12f9aede README: update cwebp help output
f0b5defb bump versions
4c42a61b update AUTHORS
6431a1ce doc: remove non-finalized chunk references
8130c4cc Merge "build: remove libwebpmux from default targets/config"
23b44438 Merge "configure: broaden test for libpng-config"
85bff2cd Merge "doc: correct lossless prefix coding table & code"
05108f6e Merge "More spec/code matching in mux:"
6808e69d More spec/code matching in mux:
bd2b46f5 Merge "doc/webp-container-spec: light cosmetics"
20ead329 doc/webp-container-spec: light cosmetics
1d40a8bc configure: add pthread detection
b5e9067a fix some int <-> size_t mix for buffer sizes
e41a7596 build: remove libwebpmux from default targets/config
0fc2baae configure: broaden test for libpng-config
45b8272c Merge "restore authorship to lossless bitstream doc"
06ba0590 restore authorship to lossless bitstream doc
44a09a3c add missing description of the alpha filtering methods
63db87dd Merge "vwebp: add checkboard background for alpha display"
a73b8978 vwebp: add checkboard background for alpha display
939158ce Merge "vwebp: fix info display"
b35c07d9 vwebp: fix info display
48b39eb1 fix underflow for very short bitstreams
7e622984 cosmetics: param alignment, manpage wording
1bd7dd50 Merge changes I7b0afb0d,I7ecc9708
ac69e63e Merge "Updated cwebp man's help for Alpha & Lossless."
c0e8859d Get rid of image_info_ from WebPChunk struct.
135ca69e WebP Container Spec:
eb6f9b8a Updated cwebp man's help for Alpha & Lossless.
0fa844fb cosmetic fixes on assert and 'const' where applicable
7f22bd25 check limit of width * height is 32 bits
16c46e83 autoconf/make: cosmetics: break long lines
ab22a07a configure: add helper macro to define --with-*
c17699b3 configure: add libtiff test
0e09732c Merge "cwebp: fix crash with yuv input + lossless"
88a510ff Merge "fix big-endian VP8LWriteBits"
da99e3bf Merge "Makefile.vc: split mux into separate lib"
7bda392b cwebp: fix crash with yuv input + lossless
f56a369a fix big-endian VP8LWriteBits
54169d6c Merge "cwebp: name InputFileFormat members consistently"
e2feefa9 Makefile.vc: split mux into separate lib
27caa5aa Merge "cwebp: add basic TIFF support"
d8921dd4 cwebp: name InputFileFormat members consistently
6f76d246 cwebp: add basic TIFF support
4691407b Merge changes If39ab7f5,I3658b5ae
cca7c7b8 Fixed nit: 10 -> 10.f
5d09a244 WebPMuxCreate() error handling:
777341c3 Fix a memleak in WebPMuxCreate()
61c9d161 doc: correct lossless prefix coding table & code
4c397579 Merge "mark VP8{,L}{GetInfo,CheckSignature} as WEBP_EXTERN"
e4e36cc6 Merge "Mux: Allow only some frames/tiles to have alpha."
ad2aad3c Merge "WebP Decoding error handling:"
97649c8f Mux: Allow only some frames/tiles to have alpha.
f864be3b Lower the quality settings for Alpha encoding.
3ba81bbe WebP Decoding error handling:
fcc69923 add automatic YUVA/ARGB conversion during WebPEncode()
802e012a fix compilation in non-FANCY_UPSAMPLING mode
e012dfd9 make width/height coding match the spec
228d96a5 mark VP8{,L}{GetInfo,CheckSignature} as WEBP_EXTERN
637a314f remove the now unused *KeepA variants
d11f6fcc webpmux returns error strings rather than numbers
fcec0593 makefile.unix: cwebp: fix OSX link
6b811f1b Merge "doc: remove lossless pdf"
c9634821 doc: remove lossless pdf
b9ae4f0d cosmetics after mux changes b74ed6e, b494ad5
b494ad50 Mux: only allow adding frame/tiles at the end.
2c341b0e Merge "Added image characteristic hint for the codec."
d373076a Added image characteristic hint for the codec.
2ed2adb5 Merge "msvc: add intrinsic based BitsLog2Floor"
e595e7c5 Merge "add demux.c to the makefiles"
da47b5bd Merge "demux: add {Next,Prev}Chunk"
e5f46742 add demux.c to the makefiles
4708393c demux: add {Next,Prev}Chunk
e8a0a821 demux: quiet msvc warnings
7f8472a6 Update the WebP Container Spec.
31b68fe6 cleanup WebPPicture struct and API
9144a186 add overflow check before calling malloc()
81720c91 consistency cosmetics
2ebe8394 Merge "Add kramdown version information to README"
******** enc/vp8l.c: fix build
b7ac19fe Add kramdown version information to README
efdcb667 Merge "Edit for consistency, usage and grammar."
******** Enable alpha in vvwebp
8de9a084 Merge "Mux API change:"
b74ed6e7 Mux API change:
233a589e take picture->argb_stride into account for lossless coding
04e33f17 Edit for consistency, usage and grammar.
a575b4bc Merge "cosmetics: add missing const"
8d99b0f4 Merge "cosmetics: remove unimplemented function proto"
69d02217 cosmetics: add missing const
5b08318b cosmetics: remove unimplemented function proto
b7fb0ed5 Log warning for unsupported options for lossless.
e1f769fe msvc: add intrinsic based BitsLog2Floor
8a69c7d8 Bug-fix: Clamp backward dist to 1.
b5b6ac97 Merge "Bring the special writer 'WebPMemoryWriter' to public API"
a6a1909f Merge "Fix floating point exception with cwebp -progress"
f2cee067 Fix floating point exception with cwebp -progress
91b7a8c7 Bring the special writer 'WebPMemoryWriter' to public API
310e2972 support resize and crop for RGBA input
a89835d3 Merge changes Ice662960,Ie8d7aa90,I2d996d5e,I01c04772
ce614c0c Merge "dec/vp8: avoid setting decoder status twice"
900285da dec/vp8: avoid setting decoder status twice
8227adc8 Merge changes I6f02b0d0,I5cbc9c0a,I9dd9d4ed,Id684d2a1
dcda59c1 Merge "demux: rename SetTile to SelectTile"
622ef12e demux: rename SetTile to SelectTile
81ebd375 Merge "demux: add {Next,Prev}Frame"
02dd37a2 demux: add {Next,Prev}Frame
4b79fa59 Merge "Limit the maximum size of huffman Image to 16MB."
9aa34b34 Manually number "chapters," as chapter numbers are used in the narrative.
2a4c6c29 Re-wrap at <= 72 columns
a45adc19 Apply inline emphasis and monospacing, per gdoc / PDF
91011206 Incorporate gdoc changes through 2012-06-08
7a182487 Removed CodeRay syntax declarations ...
b3ec18c5 Provide for code-block syntax highlighting.
709d7702 Replace high ASCII artifacts (curly quotes, etc.).
930e8abb Lossless WebP doc largely ported to markdown text.
18cae37b msvc: silence some build warnings
b3923084 Limit the maximum size of huffman Image to 16MB.
f180df2a Merge "libwebp/demux: add Frame/Chunk iteration"
2bbe1c9a Merge "Enable lossless encoder code"
d0601b01 Merge changes I1d97a633,I81c59093
78f3e345 Enable lossless encoder code
d974a9cc Merge "libwebp/demux: add simple format parsing"
26bf2232 Merge "libwebp: add WebPDemux stub functions"
2f666688 Merge "modify WebPParseHeaders to allow reuse by GetFeatures"
b402b1fb libwebp/demux: add Frame/Chunk iteration
ad9ada3b libwebp/demux: add WebPDemuxGetI
2f2d4d58 libwebp/demux: add extended format parsing
962dcef6 libwebp/demux: add simple format parsing
f8f94081 libwebp: add WebPDemux stub functions
fb47bb5c Merge "NumNamedElements() should take an enum param."
7c689805 Fix asserts in Palette and BackwardReference code.
fbdcb7ea NumNamedElements() should take an enum param.
fb4943bd modify WebPParseHeaders to allow reuse by GetFeatures
3697b5ce write an ad-hoc EncodeImageInternal variant
eaee9e79 Bug-Fix: Decode small (less than 32 bytes) images.
0bceae48 Merge "cwebp: fix alpha reporting in stats output"
0424b1ef Rebase default encoding settings.
c71ff9e3 cwebp: fix alpha reporting in stats output
e2ffe446 Merge "Stop indefinite recursion for Huffman Image."
70eb2bd6 Stop indefinite recursion for Huffman Image.
f3bab8eb Update vwebp
6d5c797c Remove support for partial files in Mux.
f1df5587 WebPMuxAssemble() returns WebPData*.
814a0639 Rename 'Add' APIs to 'Set'.
bbb0218f Update Mux psuedo-code examples.
4fc4a47f Use WebPData in MUX set APIs
c67bc979 Merge "add WebPPictureImportRGBX() and WebPPictureImportBGRX()"
27519bc2 add WebPPictureImportRGBX() and WebPPictureImportBGRX()
f80cd27e factorize code in Import()
9b715026 histogram: add log2 wrapper
8c34378f Merge "fix some implicit type conversion warnings"
42f6df9d fix some implicit type conversion warnings
250c16e3 Merge "doc: update lossless pdf"
9d9daba4 Merge "add a PDF of the lossless spec"
8fbb9188 prefer webp/types.h over stdint.h
0ca170c2 doc: update lossless pdf
0862ac6e add a PDF of the lossless spec
437999fb introduce a generic WebPPictureHasTransparency() function
d2b6c6c0 cosmetic fixes after Idaba281a
b4e6645c Merge "add colorspace for premultiplied alpha"
48f82757 add colorspace for premultiplied alpha
069f903a Change in lossless bit-stream.
5f7bb3f5 Merge "WebPReportProgress: use non-encoder specific params"
f18281ff WebPReportProgress: use non-encoder specific params
9ef32283 Add support for raw lossless bitstream in decoder.
7cbee29a Fix bug: InitIo reseting fancy_upsampling flag.
880fd98c vwebp: fix exit w/freeglut
1875d926 trap two unchecked error conditions
87b4a908 no need to have mux.h as noinst clause in enc/
88f41ec6 doc: fix bit alignment in VP8X chunk
52f5a4ef Merge "fix bug with lossy-alpha output stride"
3bde22d7 fix bug with lossy-alpha output stride
42d61b6d update the spec for the lossy-alpha compression methods.
e75dc805 Move some more defines to format_constants.h
c13f6632 Move consts to internal header format_constants.h
7f2dfc92 use a bit-set transforms_seen_ instead of looping
18da1f53 modulate alpha-compression effort according to config.method
f5f2fff6 Merge "Alpha flag fix for lossless."
c975c44e Alpha flag fix for lossless.
4f067fb2 Merge "Android: only build dec_neon with NEON support"
255c66b4 Android: only build dec_neon with NEON support
8f9117a9 cosmetics: signature fixes
39bf5d64 use header-less lossless bitstream for alpha channel
75d7f3b2 Merge "make input data be 'const' for VP8LInverseTransform()"
9a721c6d make input data be 'const' for VP8LInverseTransform()
9fc64edc Disallow re-use of same transformation.
98ec717f  use a function pointer for ProcessRows()
f7ae5e37 cosmetics: join line
140b89a3 factor out buffer alloc in AllocateARGBBuffers()
a107dfa8 Rectify WebPParseOptionalChunks().
237eab67 Add two more color-spaces for lossless decoding.
27f417ab fix orthographic typo
489ec335 add VP8LEncodeStream() to compress lossless image stream
fa8bc3db make WebPEncodingSetError() take a const picture
638528cd bitstream update for lossy alpha compression
d73e63a7 add DequantizeLevels() placeholder
ec122e09 remove arch-dependent rand()
d40e7653 fix alignment
1dd6a8b6 Merge "remove tcoder, switch alpha-plane compression to lossless"
3e863dda remove tcoder, switch alpha-plane compression to lossless
8d77dc29 Add support for lossless in mux:
831bd131 Make tile size a function of encoding method.
778c5228 Merge "remove some variable shadowing"
817c9dce Few more HuffmanTreeToken conversions.
37a77a6b remove some variable shadowing
89c07c96 Merge "normalize example header includes"
4aff411f Merge "add example_util.[hc]"
00b29e28 normalize example header includes
061263a7 add example_util.[hc]
c6882c49 merge all tree processing into a single VP8LProcessTree()
9c7a3cf5 fix VP8LHistogramNumCodes to handle the case palette_code_bits == 0
b5551d2e Merge "Added HuffmanTreeCode Struct for tree codes."
8b85d01c Added HuffmanTreeCode Struct for tree codes.
093f76d8 Merge "Allocate single memory in GetHuffBitLengthsAndCodes."
41d80494 Allocate single memory in GetHuffBitLengthsAndCodes.
1b04f6d2 Correct size in VP8L header.
2924a5ae Makefile.vc: split object lists based on directory
c8f24165 Merge "add assert(tokens)"
43239947 add assert(tokens)
9f547450 Catch an error in DecodeImageData().
ac8e5e42 minor typo and style fix
9f566d1d clean-up around Huffman-encode
c579a710 Introduce CHUNK_SIZE_BYTES in muxi.h.
14757f8a Make sure huffman trees always have valid symbols
41050618 makefile.unix: add support for building vwebp
48b37721 Merge "fixed signed/unsigned comparison warning"
57f696da Merge "EncodeImageInternal: fix potential leak"
d972cdf2 EncodeImageInternal: fix potential leak
5cd12c3d fixed signed/unsigned comparison warning
cdca30d0 Merge "cosmetics: shorten long line"
e025fb55 cosmetics: shorten long line
22671ed6 Merge "enc/vp8l: fix double free on error"
e1b9b052 Merge "cosmetics: VP8LCreateHuffmanTree: fix indent"
a8e725f8 enc/vp8l: fix double free on error
27541fbd cosmetics: VP8LCreateHuffmanTree: fix indent
1d38b258 cwebp/windows: use MAKE_REFGUID where appropriate
817ef6e9 Merge "cwebp: fix WIC/Microsoft SDK compatibility issue"
902d3e3b cwebp: fix WIC/Microsoft SDK compatibility issue
89d803c4 Merge "Fix a crash due to wrong pointer-integer arithmetic."
cb1bd741 Merge "Fix a crash in lossless decoder."
de2fe202 Merge "Some cleanup in VP8LCreateHuffmanTree() (and related functions CompareHuffmanTrees() and SetBitDepths()): - Move 'tree_size' initialization and malloc for 'tree + tree_pool'   outside the loop. - Some renames/tweaks for readability."
ce69177a Fix a crash due to wrong pointer-integer arithmetic.
e40a3684 Fix a crash in lossless decoder.
3927ff3a remove unneeded error condition for WebPMuxNumNamedElements()
2c140e11 Some cleanup in VP8LCreateHuffmanTree() (and related functions CompareHuffmanTrees() and SetBitDepths()): - Move 'tree_size' initialization and malloc for 'tree + tree_pool'   outside the loop. - Some renames/tweaks for readability.
861a5b7b add support for animation
eb5c16cc Merge "Set correct encode size in encoder's stats."
4abe04a2 fix the return value and handle missing input file case.
2fafb855 Set correct encode size in encoder's stats.
e7167a2b Provide one entry point for backward references.
c4ccab64 Print relevant lossless encoding stats in cwebp.
e3302cfd GetHuffBitLengthsAndCodes: reduce level of indirection
b5f2a9ed enc/vp8l: fix uninitialized variable warning
7885f8b2 makefile.unix: add lossless encoder files
1261a4c8 Merge "cosmetics"
3926b5be Merge "dsp/cpu.c: Android: fix crash on non-neon arm builds"
834f937f dsp/cpu.c: Android: fix crash on non-neon arm builds
126e1606 cosmetics
e38602d2 Merge branch 'lossless_encoder'
e8d3d6a0 split StoreHuffmanCode() into smaller functions
d0d88990 more consolidation: introduce VP8LHistogramSet
1a210ef1 big code clean-up and refactoring and optimization
41b5c8ff Some cosmetics in histogram.c
ada6ff77 Approximate FastLog between value range [256, 8192]
ec123ca3 Forgot to update out_bit_costs to symbol_bit_costs at one instance.
cf33ccd1 Evaluate output cluster's bit_costs once in HistogramRefine.
781c01f4 Simple Huffman code changes.
a2849bc5 Lossless decoder: remove an unneeded param in ReadHuffmanCodeLengths().
b39e7487 Reducing emerging palette size from 11 to 9 bits.
bfc73db4 Move GetHistImageSymbols to histogram.c
889a5786 Improve predict vs no-predict heuristic.
01f50663 code-moving and clean-up
31035f3b reduce memory usage by allocating only one histo
fbb501b8 Restrict histo_bits to ensure histo_image size is under 32MB
8415ddf3 further simplification for the meta-Huffman coding
e4917299 A quick pass of cleanup in backward reference code
83332b3c Make transform bits a function of encode method (-m).
72920caa introduce -lossless option, protected by USE_LOSSLESS_ENCODER
c6ac4dfb Run TraceBackwards for higher qualities.
412222c8 Make histo_bits and transform_bits function of quality.
149b5098 Update lossless encoder strategy:
0e6fa065 cache_bits passed to EncodeImageInternal()
e38b40a9 Factorize code for clearing HtreeGroup.
6f4a16ea Removing the indirection of meta-huffman tables.
3d33ecd1 Some renaming/comments related to palette in lossless encoder.
4d02d586 Lossless encoder: correction in Palette storage
4a636235 fix a memleak in EncodeImageInternal()
0993a611 Full and final fix for prediction transform
afd2102f Fix cross-color transform in lossless encoder
b96d8740 Need to write a '0' bit at the end of transforms.
54dad7e5 Color cache size should be counted as 0 when cache bits = 0
4f0c5caf Fix prediction transform in lossless encoder.
36dabdad Fix memory leak in method EncodeImageInternal for histogram_image.
352a4f49 Get rid of PackLiteralBitLengths()
d673b6b9 Change the predictor function to pass left pixel
b2f99465 Fix CopyTileWithPrediction()
84547f54 Add EncodeImageInternal() method.
6b38378a Guard the lossless encoder (in flux) under a flag
09f7532c Fix few nits (const qualifiers)
648be393 Added implementation for various lossless functions
32714ce3 Add VP8L prefix to backward ref & histogram methods.
fcba7be2 Fixed header file tag (WEBP_UTILS_HUFFMAN_ENCODE_H_)
bc703746 Add backward_ref, histogram & huffman encode modules from lossless.
fdccaadd Fixing nits
227110c4 libwebp interface changes for lossless encoding.
50679acf minor style fixes
b38dfccf remove unneeded reference to NUM_LITERAL_CODES
8979675b harmonize header description
c04eb7be tcoder.c: define NOT_HAVE_LOG2 for MSVC builds
9a214fa1 Merge "VP8[L]GetInfo: check input pointers"
5c5be8ba VP8[L]GetInfo: check input pointers
0c188fec Merge changes I431acdfe,I713659b7
b3515c62 mux: drop 'chunk' from ChunkInfo member names
aea7923c muxi.h: remove some unused defines
01422492 update NEWS file for next release
29e3f7ec Merge "dec: remove deprecated WebPINew()"
4718e449 Merge "muxedit: a few more size_t changes"
82654f96 Merge "muxedit: remove a few redundant NULL checks"
02f27fbd dec: remove deprecated WebPINew()
ccddb3fc muxedit: remove a few redundant NULL checks
a6cdf710 muxedit: a few more size_t changes
a3846892 Merge "mux: remove unused LIST_ID"
11ae46ae alpha.c: quiet some size_t -> int conversion warnings
dee46692 mux: remove unused LIST_ID
03f1f493 mux: add version checked entry points
6a0abdaa Merge "doc: tile/alpha corrections"
c8139fbe Merge "few cosmetics"
68338737 Merge "lossless: remove some size_t -> int conversions"
5249e94a doc: tile/alpha corrections
d96e722b huffman: quiet int64 -> int conversion warning
532020f2 lossless: remove some size_t -> int conversions
23be6edf few cosmetics
1349edad Merge "configure: AC_ARG_* use AS_HELP_STRING"
bfbcc60a configure: AC_ARG_* use AS_HELP_STRING
1427ca8e Merge "Makefile.am: header file maintenance"
087332e3 Merge "remove unused parameter 'round' from CalcProba()"
9630e168 remove unused parameter 'round' from CalcProba()
92092eaa Merge "bit_reader.h: correct include"
a87fc3f6 Merge "mux: ensure # images = # tiles"
53af99b1 Merge "mux: use size_t consistently"
39a57dae Makefile.am: header file maintenance
1bd0bd0d bit_reader.h: correct include
326a3c6b mux: ensure # images = # tiles
95667b8d mux: use size_t consistently
231ec1fb Removing the indirection of meta-huffman tables.
15ebcbaa check return pointer from MuxImageGetListFromId
b0d6c4a7 Merge "configure: remove test for zlib.h"
8cccac50 Merge "dsp/lossless: silence some build warnings"
b08819a6 dsp/lossless: silence some build warnings
7ae22521 Android.mk: SSE2 & NEON updates
0a49e3f3 Merge "makefile.unix add missing header files"
2e75a9a1 Merge "decode.h: use size_t consistently"
fa13035e configure: remove test for zlib.h
d3adc81d makefile.unix add missing header files
262fe01b Merge "makefile.unix & Android.mk: cosmetics"
4cce137e Merge "enc_sse2 add missing stdlib.h include"
80256b85 enc_sse2 add missing stdlib.h include
9b3d1f3a decode.h: use size_t consistently
64083d3c Merge "Makefile.am: cosmetics"
dceb8b4d Merge changes If1331d3c,I86fe3847
0e33d7bf Merge "webp/decode.h: fix prototypes"
fac0f12e rename BitReader to VP8LBitReader
fbd82b5a types.h: centralize use of stddef.h
2154835f Makefile.am: cosmetics
1c92bd37 vp8io: use size_t for buffer size
90ead710 fix some more uint32_t -> size_t typing
cbe705c7 webp/decode.h: fix prototypes
3f8ec1c2 makefile.unix & Android.mk: cosmetics
217ec7f4 Remove tabs in configure.ac
b3d35fc1 Merge "Android.mk & Makefile.vc: add new files"
0df04b9e Android.mk & Makefile.vc: add new files
e4f20c5b Merge "automake: replace 'silent-rules' w/AM_SILENT_RULES"
8d254a09 cosmetics
6860c2ea fix some uint32_t -> size_t typing
4af1858a Fix a crash due to max symbol in a tree >= alphabet size
6f01b830 split the VP8 and VP8L decoding properly
f2623dbe enable lossless decoder
b96efd7d add dec/vp8i.h changes from experimental
19f6398e add dec/vp8l{i.h,.c} from experimental
c4ae53c8 add utils/bit_reader.[hc] changes from experimental
514d0089 add dsp/lossless.[hc] from experimental
9c67291d add utils/huffman.[hc] from experimental
337914a0 add utils/color_cache.[hc] from experimental
b3bf8fe7 the read-overflow code-path wasn't reporting as an error
1db888ba take colorspace into account when cropping
61c2d51f move the rescaling code into its own file and make enc/ and dec/ use it.
efc2016a Make rescaler methods generic
3eacee81 Move rescaler methods out of io.c.
a69b893d automake: replace 'silent-rules' w/AM_SILENT_RULES
6f7bf645 issue 111: fix little-endian problem in bit-reader
ed278e22 Removed unnecessary lookup
cd8c3ba7 fix some warnings: down-cast and possibly-uninitialized variable
0a7102ba ~1% improvement of alpha compression
3bc1b141 Merge "Reformat container doc"
dc17abdc mux: cosmetics
cb5810df Merge "WebPMuxGetImage: allow image param to be NULL"
506a4af2 mux: cosmetics
135e8b19 WebPMuxGetImage: allow image param to be NULL
de556b68 Merge "README.mux: reword some descriptions"
0ee2aeb9 Makefile.vc: use batch mode rules
d9acddc0 msvc: move {i,p}db creation to object directory
237c9aa7 Merge "expose WebPFree function for DLL builds"
b3e4054f silence msvc debug build warning
45feb55d expose WebPFree function for DLL builds
11316d84 README.mux: reword some descriptions
4be52f4a factorize WebPMuxValidate
14f6b9f6 mux: light cleanup
5e96a5db add more param checks to WebPPictureDistortion()
8abaf820 Merge "silence some type size related warnings"
1601a39b silence some type size related warnings
f3abe520 Merge "idec: simplify buffer size calculation"
a9c5cd4c idec: simplify buffer size calculation
7b06bd7f Merge "configure/automake: add silent-rules option"
e9a7d145 Reformat container doc
d4e5c7f3 configure/automake: add silent-rules option
5081db78 configure/automake: no -version-info for convenience libs
85b6ff68 Merge "idec: fix WebPIUpdate failure"
7bb6a9cc idec: fix internal state corruption
89cd1bb8 idec: fix WebPIUpdate failure
01b63806 4-5% faster decoding, optimized byte loads in arithmetic decoder.
631117ea Merge "cosmetics & warnings"
a0b2736d cosmetics & warnings
f73947f4 use 32bit for storing dequant coeffs, instead of 16b.
b9600308 Merge "store prediction mode array as uint8_t[16], not int[16]."
7b67881a store prediction mode array as uint8_t[16], not int[16].
cab8d4dc Merge "NEON TransformOne"
ba503fda NEON TransformOne
9f740e3b Merge "gcc warning fix: remove the 'const' qualifier."
f76d3587 gcc warning fix: remove the 'const' qualifier.
e78478d6 Merge "webpmux: make more use of WebPData"
f85bba3d Merge "manpages: add BUGS section"
48a43bbf Merge "makefile.unix: variable cosmetics"
c274dc96 makefile.unix: variable cosmetics
1f7b8595 re-organize the error-handling in the main loop a bit
1336fa71 Only recompute level_cost_[] when needed
771ee449 manpages: add BUGS section
0f7820e6 webpmux: make more use of WebPData
974aaff3 examples: logging updates
6c14aadd Merge "better token buffer code"
f4054250 better token buffer code
18d959fa Merge "mux: add WebPData type"
eec4b877 mux: add WebPData type
0de3096b use 16bit counters for recording proba counts
7f23678d fix for LevelCost + little speed-up
7107d544 further speed-up/cleanup of RecordCoeffs() and GetResidualCost()
fd221040 Introduce Token buffer (unused for now)
5fa148f4 Merge "speed-up GetResidualCost()"
28a9d9b4 speed-up GetResidualCost()
11e7dadd Merge "misc cosmetics"
378086bd misc cosmetics
d61479f9 add -print_psnr and -print_ssim options to cwebp.
2e3e8b2e add a WebPCleanupTransparentArea() method
552c1217 Merge "mux: plug some memory leaks on error"
a2a81f7d Merge "fix Mach-O shared library build"
b3482c43 Merge "fix gcc-4.0 apple 32-bit build"
e4e3ec19 fix gcc-4.0 apple 32-bit build
b0d2fecf mux: plug some memory leaks on error
f0d2c7a7 pass of cosmetics
b309a6f9 fix Mach-O shared library build
241ddd38 doc: delete mux container pdf
8b1ba272 doc: update VP8 decode guide link
7e4371c5 WebPMuxCreate: fix unchecked malloc
eb425586 Merge "have makefile.unix clean up src/webp/*~ too"
a85c3631 Merge "correct EncodeAlpha documentation"
a33842fd Merge "Update webp container spec with alpha filter options."
8d6490da Incremental support for some of the mux APIs.
b8375abd have makefile.unix clean up src/webp/*~ too
b5855fc7 correct EncodeAlpha documentation
dba37fea Update webp container spec with alpha filter options.
2e74ec8b fix compile under MINGW
716d1d7f fix suboptimal MAX_LEN cut-off limit
57cab7b8 Harmonize the alpha-filter predictions at boundary
3a989534 Merge "Fix bug for Alpha in RGBA_4444 color-mode."
8ca2076d Introduce a 'fast' alpha mode
221a06bb Fix bug for Alpha in RGBA_4444 color-mode.
ad1e163a cosmetics: normalize copyright headers
c77424d7 cosmetics: light include cleanup
9d0e17c9 fix msvc build breakage after 252028a
7c4c177c Some readability fixes for mux library
d8a47e66 Merge "Add predictive filtering option for Alpha."
252028aa Add predictive filtering option for Alpha.
9b69be1c Merge "Simplify mux library code"
a056170e Simplify mux library code
992187a3 improve log2 test
e852f832 update Android.mk file list
a90cb2be reduce number of copies and mallocs in alpha plane enc/dec
b1662b05 fix some more type conversion warnings w/MSVC
223d8c60 fix some uint64_t -> int conversion warnings with MSC
c1a0437b Merge "simplify checks for enabling SSE2 code"
f06817aa simplify checks for enabling SSE2 code
948d4fe9 silence a msvc build warning
91179549 vwebp: msvc build tweaks
7937b409 simple WebP viewer, based on OpenGL
6aac1df1 add a bunch of missing 'extern "C"'
421eb99d Merge "Remove assigned-but-not-used variable "br""
91e27f45 better fitting names for upsampling functions
a5d7ed5c Remove assigned-but-not-used variable "br"
f62d2c94 remove unused 'has_alpha' from VP8GetInfo() signature
08e86582 trap alpha-decoding error
b361eca1 add cut-off to arith coder probability update.
8666a93a Some bug-fixes for images with alpha.
273a12a0 fix off-by-1 diff in case cropping and simple filtering
2f741d1e webpmux: ReadImage: fix ptr free in error case
721f3f48 fix alpha decode
60942c8c fix the has_alpha_ order
30971c9e Implement progress report (and user abort)
eda520a9 cosmetics after 9523f2a
38bd5bb5 Merge "Better alpha support in webpmux binary"
ccbaebfe Merge "Updated the includes to relative paths."
d71fbdcc fix small typo in error message array
cdf97aa2 Better alpha support in webpmux binary
885f25bc Updated the includes to relative paths.
a0ec9aac Update WebP encoder (cwebp) to support Alpha.
667b769a Fixed the include for types.h within mux.h
9523f2a5 Add Alpha Encode support from WebPEncode.
16612ddd Merge "Add Alpha Decode support from WebPDecode."
d117a940 Add Alpha Decode support from WebPDecode.
67228734 cosmetics after e1947a9
e1947a92 Add Alpha encode/decode code.
afc4c5d6 simplify code by introducing a CopyPlane() helper func
113b3128 Merge "MUX API Updates"
c398f595 MUX API Updates
5acf04ef remove orphan source file
059f03ef Merge "dec: validate colorspace before using as array index"
70a03989 Merge "factorize some code"
9b243b3d factorize some code
372e2b46 Correct a bug in ReadPNG() with GRAY_ALPHA images
469d6eb9 Merge "Makefile.am: remove redundant noinst_HEADERS"
9fe3372f dec: validate colorspace before using as array index
8962030f remove orphan source file
ced3e3f4 Makefile.am: remove redundant noinst_HEADERS
964387ed use WEBP_INLINE for inline function declarations
90880a11 Merge "manpages: break long lines"
b5910895 Merge "manpages: minor formatting updates"
4c451e4a Merge "Rectify the Chunk parsing logic."
04e84cf1 examples: slight cleanup
099717ce manpages: break long lines
1daf39bb manpages: minor formatting updates
abd030b5 fix missing "(void)" in function signature
f6a7d758 remove useless test
f07b2138 Rectify the Chunk parsing logic.
b8634f7d webpmux: fix lib link order
42c2e682 Fix missing coma (on uncompiled code)
d8329d41 Android.mk: add missing source files
13a54df5 Merge "More aggressive copy-edit; add TODO; validate HTML5"
868b96ae More aggressive copy-edit; add TODO; validate HTML5
767afea2 configure: check for a symbol contained in libpng
408b8918 Merge "Linewrap at 72 cols. Casual copy-edit."
3ae318c7 Merge "Restore (most) emphasis; add emphasis to normative RFC 2119 terms (MUST, etc.)"
918eb2d8 Merge "Basic container doc source clean-up; fix lists and pseudocode blocks."
03bec9e0 Linewrap at 72 cols. Casual copy-edit.
2678d819 Restore (most) emphasis; add emphasis to normative RFC 2119 terms (MUST, etc.)
428674da Basic container doc source clean-up; fix lists and pseudocode blocks.
6a77d928 Merge "Makefile.vc: cosmetics"
28c38e8c Merge "Makefile.vc: condense directory creation rules"
55be2cf8 Initial import of container spec document, from pdftotext transform.
a82a788b Makefile.vc: cosmetics
c8f41ce5 Makefile.vc: condense directory creation rules
2b877cd0 Some fixes to Makefile.vc to support the src\mux directory.
3eb969b3 Merge "Add Makefile.vc for Mux library & binary."
e78e971e Add Makefile.vc for Mux library & binary.
6aedde58 Add manual for WebPMux tool.
8a360d0a Merge "Added WebPMux Binary."
a4f32cae Added WebPMux Binary.
f3bf4c76 Added Mux Container Spec & README for MUX-API.
9f761cfa Changed function signature for WebPMuxCreate
5f31b5ec Merge "Add Mux library for manipulating WebP container."
2315785f Add Mux library for manipulating WebP container.
7e198abb update ChangeLog (tag: v0.1.3)
dfc9c1ea Harmonize the dates
28ad70c5 Fix PNG decoding bug
846e93c5 Update AUTHORS & add .mailmap
563e52d6 cosmetics after '76036f5 Refactor decoder library'
76036f54 Refactor decoder library
377ef43c configure.ac: update AC_INIT params
7a8d8762 use a user-visible MACRO for max width/height.
d4e9f559 NEON decode support in WebP
0ee683b5 update libtool version-info
fdbe02c5 windows: match _cond_destroy logic w/return variable name
206b686b README: correct advanced decode api pseudo-code
6a32a0f5 make VP8BitReader a typedef, for better re-use
b112e836 create a libwebputils under src/utils
ee697d9f harmonize the include guards and #endif comments
a1ec07a6 Fixing compiler error in non x86 arch.
dcfa509a Fixed recursive inclusion of bit_writer.h and vp8enci.h.
e06ac088 create a separate libwebpdsp under src/dsp
ebeb412a use unsigned int for bitfields
341cc56a make kNewRange a static array
227a91e5 README: minor wording update
05bd8e6a add man pages to dist
812dfa1a bump up versions in preparations for 0.1.3
a5b78c81 wrap alpha-related options under WEBP_EXPERIMENTAL_FEATURES flag
34dc7907 regen ChangeLog for 0.1.3-rc2
7c436630 Silence some (more) Visual Studio warnings.
60306e8c add top-level gitattributes
2aa6b80e Slience some Visual Studio warnings.
4cbbb290 Merge "bump up version for next freeze"
a3291674 bump up version for next freeze
c7e86aba cosmetics: fix comment line lengths
c9e037ab makefile.unix: add simple dist target
87d58ce9 makefile.unix: rule maintenance
d477de77 mend
fac15ec7 Update NEWS & README for next release V0.1.3
6215595c Merge "add a -partition_limit option to limit the number of bits used by intra4x4"
3814b76c Merge "reorganize chunk-parsing code"
900286e0 add a -partition_limit option to limit the number of bits used by intra4x4
cd12b4b0 add the missing cost for I4/I16 mode selection
dfcc2136 reorganize chunk-parsing code
3cf20306 initialize pointers to function within VP8DspInit()
d21b4795 Merge "windows: add decode threading support"
473ae953 fix hang on thread creation failure
fccca420 windows: add decode threading support
a31f843a Use the exact PNG_INCLUDES/PNG_LIBS when testing for -lpng
ad9b45f1 Merge "Makefile.vc: rule maintenance"
565a2cab Makefile.vc: rule maintenance
2d0da681 makefile.unix: disable Wvla by default
fc7815d6 multi-thread decoding: ~25-30% faster
acd8ba42 io->teardown() was not always called upon error
c85527b1 Merge "Makefile.vc: add DLL configs"
e1e9be35 cosmetics: spelling/grammar in README and lib headers
b4d0ef8f Makefile.vc: add DLL configs
998754a7 remove unused nb_i4_ and nb_i16_ fields.
9f01ce3a rename WebPDecBuffer::memory -> private_memory
fb5d659b fix an overflow bug in LUT calculation
d646d5c7 swig: add WebPDecodeARGB
78aeed40 add missing WebPDecodeARGBInto() and switch ARGB4444 to RGBA4444 as was intended
cd7c5292 explicitly mark library functions as extern
19db59f8 add support for RGB565, ARGB4444 and ARGB colorspace (decoder)
c915fb2a encoder speed-up: hardcode special level values
c558bdad Rename and improve the API to retrieve decoded area
bf599d74 Merge "makefile.unix: disable -Wvla by default"
c9ea03d7 SSE2 version of strong filtering
993af3e2 makefile.unix: disable -Wvla by default
3827e1bc Merge "examples: (windows/WIC) add alpha support"
e291fae0 SSE2 functions for the fancy upsampler.
a06bbe2e add WebPISetIOHooks() to set some custom hooks on the incremental decoder object.
7643a6f2 Merge "makefile.unix: use uname to detect OSX environment"
5142a0be export alpha channel (if present) when dumping to PGM format
14d5731c makefile.unix: use uname to detect OSX environment
08057062 examples: quiet warnings
3cfe0888 examples: (windows/WIC) add alpha support
13ed94b8 add compile warning for variable-length-array
5a18eb1a Merge "add Advanced Decoding Interface"
5c4f27f9 add missing \n
f4c4e416 80 cols fix
d2603105 add Advanced Decoding Interface
bd2f65f6 sse2 version of the complex filter
96ed9ce0 perform two idct transforms at a time when possible
01af7b69 use aligned stored
0e1d1fdf Merge "Makefile.vc: add experimental target"
2a1292a6 Makefile.vc: add experimental target
23bf351e Enable decode SSE2 for Visual Studio
131a4b7b dec/dsp_sse2: fix visual studio compile
00d9d680 swig: file reorganization
7fc7e0d9 Merge "swig/java: basic encode support"
3be57b16 fix MSVC compile for WEBP_EXPERIMENTAL_FEATURES
40a7e347 dec/dsp: disable sse2 for Visual Studio builds
e4d540c8 add SSE2 code for transform
54f2170a swig/java: basic encode support
c5d4584b call function pointers instead of C-version
ea43f045 Merge "configure: mingw32 targets: test for WIC support"
a11009d7 SSE2 version of simple in-loop filtering
42548da9 shave one unneeded filter-cache line
31f9dc6f configure: mingw32 targets: test for WIC support
19559699 Merge "split expression in two."
415dbe46 split expression in two.
e29072a8 configure: test for zlib only w/--enable-experimental
b2b0090b Simplify Visual Studio ifdefs
ca7a2fd6 Add error reporting from encoding failures.
6c9405db Merge "Makefile.vc: require CFG with clean target"
0424ecd9 Makefile.vc: require CFG with clean target
003417c7 Enable SSE2 for Visual Studio builds
af10db4a little speed up for VP8BitUpdate()
e71418f8 more MSVC files to ignore
46d90363 cosmetics
edf59ab3 typo fix
72229f5f Add support for x64 and SSE2 builds under Windows.
92e5c6e1 VP8GetInfo() + WebPResetDecParams()
416b7a6b raise the fixed-point precision for the rescaler
aa87e4e0 fix alignment
eb66670c disable WEBP_EXPERIMENTAL_FEATURES
c5ae7f65 typo fix: USE_ => WEBP_
d041efae swig: add libwebp.jar/libwebp_java_wrap.c
f6fb3877 add swig interface
e9273902 align buffer for double too
842c009b fix -strong option
d0a70387 Merge "cosmetics"
fc0a02e5 fix the dichotomy loop
38369c03 cosmetics
8dfc4c6f factorize and unify GetAlpha() between the C and SSE2 version
6d0e66c2 prepare experimentation with yuv444 / 422
79cc49f5 add a --enable-experimental option to './configure'
d7575238 sse2 version of CollectHistogram()
c1c728d6 add an extra #ifdef WEBP_EXPERIMENTAL_FEATURES to avoid 'unused variable' warning
60c61d2d always call VP*EncDeleteAlpha() unconditionnally, for simplicity
0f8c6384 simply don't call WriteExtensions() if WEBP_EXPERIMENTAL_FEATURES is not defined
47c661d5 rename swap -> swap_rb
10d55bbb move chunk[] declaration out of the for() loop
517cec21 fix indentation
f7d9e261 fix merge problems
8fd42b3a add a stride 'a_stride' for the alpha plane
b8dcbf2f fix alpha-plane copy and crop methods
cdef89de fix some 'unused variable' warning
fb29c262 SSE2 version of the fwd transform and the squared sum metric
2ab4b72f EXPERIMENTAL: add support for alpha channel
cfbf88a6 add SSE2 functions. ~2x faster encoding on average.
e7ff3f9a merge two ITransforms together when applicable and change the TTransform to return the sum directly.
ca554137 fix WebPIDecGetRGB() to accept any RGB(A) mode, not just MODE_RGB
8aa50efd fix some 'man' typos
d3f3bdda update ChangeLog (tag: v0.1.2)
d7e9a69c update contributor list
261abb8e add a 'superclean' section
276ae825 Remove files not mean to be in git, and update .gitignore
24868455 build: prepare libwebp.pc
14ceb6e8 add "-version" description to man pages
b247a3b2 Create the m4 directory, and also place .gitignore in there for libtool.
cdd734c9 Resolve automake warnings
c5fa726e build: add pkgconfig files
b20aaca2 build: just use autoreconf, avoid calling tools manually
4b0b0d66 cwebp: use modern functions
efbc6c41 update Android.mk
7777570b better version of ChangeLog
fa70d2b7 update version number in the DOC
f8db5d5d more C89-fixes
0de013b3 fix typos
650ffa3b add version getters for decoder and encoder
be4867d2 doc for incremental decoding
56732a1b add idec.obj in MSVC makefile
208afb5e add c++ guards
8bf76fe0 add incremental decoding
1f288328 'inline' isn't defined in strict ansi c89
8b77c632 move the quantization function to dsp.c
b2c3575c add a 'last_y' field to WebPDecParams
2654c3da correctly pass along the exact same status returned from ParsePartitions
4704146a add missing precision in the man
6d978a6c add error messages
6463e6ab add some install instructions, and fix intel-mac flags
05fb7bfc Merge ".gitignore: initial version"
c33f0195 .gitignore: initial version
e532b9ab Makefile: allow out of tree builds
4c0da7aa enable sparse dc/ac transforms
07dbb8d5 clarify the return logic
5c69e1bb fix bigger-by-1 array
7c5267e3 fix a (harmless) typo: non_zero_ -> non_zero_ac_
bc752135 fix missing free()
af3e2aaa remove trailing spaces
13e50da6 make the bitreader preload at least 8bits, instead of post-load them (this makes initialization easier and will be helpful for incremental decoding). Modify ParsePartitions() to accommodate for truncated input.
f4888f77 emit 9 - nb_bits trailing zeros instead of 8
3db65255 separate block-parsing into a visible VP8DecodeMB()
a871de02 add missing extern "C"
b3ce8c52 remove a gcc warning about type pun by using a proper union'd type
e1863715 update after addition of webpi.h
3e856e2d Extract some useful functions around decoding buffer WebPDecParams.
d5bc05a4 make the filtering process match libvpx and ffvp8
dd60138d add man pages for cwebp(1) and dwebp(1)
c4fa3644 fix header
5b70b378 * add an option to bypass_filtering in VP8Io.
b97a4003 simplify QuantizeBlock code a bit
84b58ebb add more checks around picture allocation
b65a3e10     remove absolute_delta_ field and syntax code
0744e842 Dont' open output file until we're sure the input file is valid
d5bd54c7 fix typo and buggy line
f7a9549d Add a simple top-level makefile.unix for quick & easy build.
5f36b944 update the doc for the -f option
f61d14aa a WebP encoder converts PNG & JPEG to WebP
81c96621 oops: forgotten call to Initialize() + move the error message to a more useful place
87ffa005 typo: fix a missing 'R', was confusing.
b04b857a * add decoding measurement using stopwatch.h (use -v option) * support PNG output through WIC on Win32
746a4820 * make (*put)() hook return a bool for abort request. * add an enum for VP8Status() to make things clearer
73c973e6 * strengthen riff/chunk size checks * don't consider odd-sized chunks being an error
1dc4611a add support for PNG output (default) regularize include guards
860641df fix a typo: sizeof(kYModeProbaInter0) => sizeof(kUVModeProbaInter0)
3254fc52 fix some petty constness fix the ./configure file too
504d3393 fix eof_ mis-initialization
2bc0778f leftover Makefile.* from previous commit
d2cf04e4 move Makefile.am one level below, to src/dec fix typos here and there dwebp is now an installed program
ade92de8 typo: vp8.h -> decode_vp8.h
d7241241 forgot to declare types.h to be installed
6421a7a4 move the decoder sourcetree to a sub-location src/dec to make room for future libs sources
a9b3eab6 correct layout name is IMC4.
2330522c handle corner case of zero-dimensions
280c3658 make VP8Init() handle short buffers (< 2 bytes) correctly
b1c9e8b4 handle error cases more robustly
0e94935c Merge "table-less version of clip_8b()"
1e0a2d25 table-less version of clip_8b()
e12109ee dwebp: change -yuv option to -raw change the layout to IMC2
d72180a4 speed-up fancy upscaler
9145f3bc reset eof_ at construction time
a7ee0559 simplify the logic of GetCoeffs()
f67b5939 lot of cosmetics
ea27d7c6 fix endian problem on PowerPC
beb0a1ba fix signature of VP8StoreBlock
b128c5e2 Merge "fancy chroma upscaling"
6a37a2aa fancy chroma upscaling
ff565edc fix two numeric typos
5a936a0a use uintptr_t for casting pointers to ints
e14a0301 for cross_compiling=yes to prevent executing any binary
83b545ee add vc9+ makefile
296f6914 fix output loop for small height
cbfbb5c3 convert to plain-C
f09f96ee Fix declaration after statement warning
5981ee55 Fix UV plane ac/dc quantizer transposition
c8d15efa convert to ANSI-C
c3f41cb4 Initial commit
