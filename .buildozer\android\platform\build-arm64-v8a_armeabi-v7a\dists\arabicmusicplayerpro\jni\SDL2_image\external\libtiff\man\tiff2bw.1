.\"
.\" Copyright (c) 1988-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFF2BW 1 "November 2, 2005" "libtiff"
.SH NAME
tiff2bw \- convert a color
.SM TIFF
image to greyscale
.SH SYNOPSIS
.B tiff2bw
[
.I options
]
.I input.tif
.I output.tif
.SH DESCRIPTION
.I Tiff2bw
converts an
.SM RGB
or Palette color
.SM TIFF
image to a greyscale image by
combining percentages of the red, green, and blue channels.
By default, output samples are created by taking
28% of the red channel, 59% of the green channel, and 11% of
the blue channel.
To alter these percentages, the
.BR \-R ,
.BR \-G ,
and
.BR \-B
options may be used.
.SH OPTIONS
.TP
.B \-c
Specify a compression scheme to use when writing image data:
.B "\-c none"
for no compression,
.B "\-c packbits"
for the PackBits compression algorithm,
.B "\-c zip
for the Deflate compression algorithm,
.B "\-c g3
for the CCITT Group 3 compression algorithm,
.B "\-c g4
for the CCITT Group 4 compression algorithm,
and
.B "\-c lzw"
for Lempel-Ziv & Welch (the default).
.TP
.B \-r
Write data with a specified number of rows per strip;
by default the number of rows/strip is selected so that each strip
is approximately 8 kilobytes.
.TP
.B \-R
Specify the percentage of the red channel to use (default 28).
.TP
.B \-G
Specify the percentage of the green channel to use (default 59).
.TP
.B \-B
Specify the percentage of the blue channel to use (default 11).
.SH "SEE ALSO"
.BR pal2rgb (1),
.BR tiffinfo (1),
.BR tiffcp (1),
.BR tiffmedian (1),
.BR libtiff (3)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
