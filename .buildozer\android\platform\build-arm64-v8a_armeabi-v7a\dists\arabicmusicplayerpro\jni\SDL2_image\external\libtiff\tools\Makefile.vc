#
# Copyright (C) 2004, <PERSON><PERSON> <<EMAIL>>
#
# Permission to use, copy, modify, distribute, and sell this software and 
# its documentation for any purpose is hereby granted without fee, provided
# that (i) the above copyright notices and this permission notice appear in
# all copies of the software and related documentation, and (ii) the names of
# <PERSON>ffler and Silicon Graphics may not be used in any advertising or
# publicity relating to the software without the specific, prior written
# permission of <PERSON> and Silicon Graphics.
# 
# THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
# EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
# WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
# 
# IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
# ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
# OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, <PERSON>ATA OR PROFITS,
# WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
# LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
# OF THIS SOFTWARE.
#
# Makefile for MS Visual C and Watcom C compilers.
#
# To build:
# C:\libtiff\tools> nmake /f makefile.vc 

!INCLUDE ..\nmake.opt

TARGETS	=	tiffinfo.exe tiffdump.exe fax2tiff.exe \
		fax2ps.exe pal2rgb.exe ppm2tiff.exe \
		rgb2ycbcr.exe thumbnail.exe raw2tiff.exe \
		tiff2bw.exe tiff2rgba.exe tiff2pdf.exe tiff2ps.exe \
		tiffcmp.exe tiffcp.exe tiffcrop.exe tiffdither.exe \
		tiffmedian.exe tiffset.exe tiffsplit.exe 

INCL		= 	-I..\libtiff -I..\port -DNEED_LIBPORT
LIBS		=	$(LIBS) ..\port\libport.lib ..\libtiff\libtiff.lib

default:	$(TARGETS)

.c.exe:
	$(CC) $(CFLAGS) $*.c $(EXTRA_OBJ) $(LIBS)

tiffgt.exe:
	$(CC) $(CFLAGS) tiffgt.c $(EXTRA_OBJ) $(LIBS)

clean:
	-del *.exe
	-del *.exe.manifest
	-del *.obj
