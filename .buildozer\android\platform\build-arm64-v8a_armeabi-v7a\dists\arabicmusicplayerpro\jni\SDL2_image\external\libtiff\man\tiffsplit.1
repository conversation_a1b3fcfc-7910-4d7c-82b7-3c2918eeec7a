.\"
.\" Copyright (c) 1992-1997 <PERSON>
.\" Copyright (c) 1992-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFSPLIT 1 "September 20, 2005" "libtiff"
.SH NAME
tiffsplit \- split a multi-image
.SM TIFF
into single-image
.SM TIFF
files
.SH SYNOPSIS
.B tiffsplit
.I src.tif
[
.I prefix
]
.SH DESCRIPTION
.I tiffsplit
takes a multi-directory (page)
.SM TIFF
file and creates one or more single-directory (page)
.SM TIFF
files from it.
The output files are given names created by concatenating
a prefix, a lexically ordered
suffix in the range [\fIaaa\fP-\fIzzz\fP], the suffix
.I .tif 
(e.g. 
.IR xaaa.tif ,
.IR xaab.tif ,
.IR ... ,
.IR xzzz.tif ).
If a prefix is not specified on the command line,
the default prefix of
.I x
is used.
.SH OPTIONS
None.
.SH BUGS
Only a select set of ``known tags'' is copied when splitting.
.SH "SEE ALSO"
.BR tiffcp (1),
.BR tiffinfo (1),
.BR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
