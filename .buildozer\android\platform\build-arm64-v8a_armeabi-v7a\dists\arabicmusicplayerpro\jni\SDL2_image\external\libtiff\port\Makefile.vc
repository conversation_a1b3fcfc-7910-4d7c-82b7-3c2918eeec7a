#
# Copyright (C) 2004, <PERSON><PERSON> <<EMAIL>>
#
# Permission to use, copy, modify, distribute, and sell this software and 
# its documentation for any purpose is hereby granted without fee, provided
# that (i) the above copyright notices and this permission notice appear in
# all copies of the software and related documentation, and (ii) the names of
# <PERSON>ffler and Silicon Graphics may not be used in any advertising or
# publicity relating to the software without the specific, prior written
# permission of <PERSON> and Silicon Graphics.
# 
# THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
# EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
# WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
# 
# IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
# ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
# OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, <PERSON><PERSON>A OR PROFITS,
# WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
# LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
# OF THIS SOFTWARE.
#
# Makefile for MS Visual C and Watcom C compilers.
#
# To build:
# C:\libtiff\port> nmake /f makefile.vc

!INCLUDE ..\nmake.opt

HAVE_STRTOL = 1
HAVE_STRTOUL = 1

# strtoul()/strtoull() are provided together
!IF $(HAVE_STRTOLL)
HAVE_STRTOULL = 1
!ELSE
HAVE_STRTOULL = 0
!endif

!IF $(HAVE_STRTOL)
STRTOL_OBJ =
!ELSE
STRTOL_OBJ = strtol.obj
!ENDIF

!IF $(HAVE_STRTOUL)
STRTOUL_OBJ =
!ELSE
STRTOUL_OBJ = strtoul.obj
!ENDIF

!IF $(HAVE_STRTOLL)
STRTOLL_OBJ =
!ELSE
STRTOLL_OBJ = strtoll.obj
!ENDIF

!IF $(HAVE_STRTOULL)
STRTOULL_OBJ =
!ELSE
STRTOULL_OBJ = strtoull.obj
!ENDIF

INCL = -I..\libtiff

OBJ	= \
	snprintf.obj \
	strcasecmp.obj \
	$(STRTOL_OBJ) \
	$(STRTOUL_OBJ) \
	$(STRTOLL_OBJ) \
	$(STRTOULL_OBJ) \
	getopt.obj

all:	libport.lib

libport.lib:	$(OBJ)
	$(AR) /out:libport.lib $(OBJ)

clean:
	-del *.obj
	-del *.lib

