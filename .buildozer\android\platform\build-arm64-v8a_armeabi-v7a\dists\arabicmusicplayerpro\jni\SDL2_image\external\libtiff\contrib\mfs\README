Date:    Mon, 23 Jun 1997 13:30:48 +0200
To:      <<EMAIL>>

From:    "<PERSON>" <<EMAIL>>
Subject: libtiff - Thanks

Return-Path: <EMAIL> 
Delivery-Date: Mon, 23 Jun 1997 06:53:39 -0700

Hi <PERSON>,

I noticed in the README from libtiff that you would like to know about
what people have done with libtiff, so I thought I would drop you a
line.

We have used libtiff to create and convert TIFF images of financial
documents which are sent from and to major document processing systems
in Sweden and Denmark.

I would like to express my deep gratitude to yourself and Sillicon
Graphics for making this excellent library available for public use.
There is obviously a lot of work that has gone in to libtiff and the
quality of the code and documentation is an example to others.

One thing that libtiff did not do was work on a memory area rather than
files. In my applications I had already read a TIFF or other format
file in to memory and did not want to waste I/O writing it out again
for libtiff's benefit. I therefore constructed a set of functions to
pass up to TIFFClientOpen to simulate a file in memory. I have attached
my mfs (memory file system) source code for you to use or junk, as you
see fit. :-)

Once again, thanks very much for making my life simpler.

Best Regards,

<PERSON>.
