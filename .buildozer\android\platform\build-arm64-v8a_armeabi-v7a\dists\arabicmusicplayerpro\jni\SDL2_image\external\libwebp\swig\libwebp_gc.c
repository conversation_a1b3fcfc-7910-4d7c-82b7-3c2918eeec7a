/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 2.0.10
 *
 * This file is not intended to be easily readable and contains a number of
 * coding conventions designed to improve portability and efficiency. Do not make
 * changes to this file unless you know what you are doing--modify the SWIG
 * interface file instead.
 * ----------------------------------------------------------------------------- */

/* This file should be compiled with 6c/8c.  */
#pragma dynimport _ _ "libwebp_go.so"

#include "runtime.h"
#include "cgocall.h"

#ifdef _64BIT
#define SWIG_PARM_SIZE 8
#else
#define SWIG_PARM_SIZE 4
#endif

#pragma dynimport _wrap_WebPGetDecoderVersion _wrap_WebPGetDecoderVersion ""
extern void (*_wrap_WebPGetDecoderVersion)(void*);
static void (*x_wrap_WebPGetDecoderVersion)(void*) = _wrap_WebPGetDecoderVersion;

void
·WebPGetDecoderVersion(struct {
  uint8 x[SWIG_PARM_SIZE];
} p)

{
  runtime·cgocall(x_wrap_WebPGetDecoderVersion, &p);
}



#pragma dynimport _wrap_wrapped_WebPGetInfo _wrap_wrapped_WebPGetInfo ""
extern void (*_wrap_wrapped_WebPGetInfo)(void*);
static void (*x_wrap_wrapped_WebPGetInfo)(void*) = _wrap_wrapped_WebPGetInfo;

void
·Wrapped_WebPGetInfo(struct {
  uint8 x[(2 * SWIG_PARM_SIZE) + (3 * SWIG_PARM_SIZE) + (3 * SWIG_PARM_SIZE) + SWIG_PARM_SIZE];
} p)

{
  runtime·cgocall(x_wrap_wrapped_WebPGetInfo, &p);
}



