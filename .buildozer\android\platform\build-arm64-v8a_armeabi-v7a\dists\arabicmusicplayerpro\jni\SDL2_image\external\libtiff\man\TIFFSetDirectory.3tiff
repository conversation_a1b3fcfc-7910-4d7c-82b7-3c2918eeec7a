.\"
.\" Copyright (c) 1988-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFSetDirectory 3TIFF "October 15, 1995" "libtiff"
.SH NAME
TIFFSetDirectory, TIFFSetSubDirectory \- set the current directory for an open
.SM TIFF
file
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.BI "int TIFFSetDirectory(TIFF *" tif ", tdir_t " dirnum ")"
.br
.BI "int TIFFSetSubDirectory(TIFF *" tif ", uint64 " diroff ")"
.SH DESCRIPTION
.I TIFFSetDirectory
changes the current directory and reads its contents with
.IR TIFFReadDirectory .
The parameter
.I dirnum
specifies the subfile/directory as an integer number, with the first directory
numbered zero.
.PP
.I TIFFSetSubDirectory
acts like 
.IR TIFFSetDirectory ,
except the directory is specified as a file offset instead of an index; this
is required for accessing subdirectories linked through a
.I SubIFD
tag.
.SH "RETURN VALUES"
On successful return 1 is returned. Otherwise, 0 is returned if 
.I dirnum
or
.I diroff
specifies a non-existent directory, or if an error was encountered while
reading the directory's contents.
.SH DIAGNOSTICS
All error messages are directed to the
.IR TIFFError (3TIFF)
routine.
.PP
.BR "%s: Error fetching directory count" .
An error was encountered while reading the ``directory count'' field.
.PP
.BR "%s: Error fetching directory link" .
An error was encountered while reading the ``link value'' that points to the
next directory in a file.
.SH "SEE ALSO"
.IR TIFFCurrentDirectory (3TIFF),
.IR TIFFOpen (3TIFF),
.IR TIFFReadDirectory (3TIFF),
.IR TIFFWriteDirectory (3TIFF),
.IR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
