/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 2.0.10
 *
 * This file is not intended to be easily readable and contains a number of
 * coding conventions designed to improve portability and efficiency. Do not make
 * changes to this file unless you know what you are doing--modify the SWIG
 * interface file instead.
 * ----------------------------------------------------------------------------- */

package libwebp

import _ "runtime/cgo"
import "unsafe"

type _ unsafe.Pointer

type _swig_fnptr *byte
type _swig_memberptr *byte

//extern libwebpSwigCgocall
func SwigCgocall()

//extern libwebpSwigCgocallDone
func SwigCgocallDone()

//extern libwebpSwigCgocallBack
func SwigCgocallBack()

//extern libwebpSwigCgocallBackDone
func SwigCgocallBackDone()

func WebPGetDecoderVersion() int
func Wrapped_WebPGetInfo(string, []int, []int) int

// WebPGetInfo has 2 output parameters, provide a version in the more natural
// go idiom:
func WebPGetInfo(webp []byte) (ok bool, width int, height int) {
	w := []int{0}
	h := []int{0}
	ok = Wrapped_WebPGetInfo(string(webp), w, h) != 0
	width = w[0]
	height = h[0]
	return
}
