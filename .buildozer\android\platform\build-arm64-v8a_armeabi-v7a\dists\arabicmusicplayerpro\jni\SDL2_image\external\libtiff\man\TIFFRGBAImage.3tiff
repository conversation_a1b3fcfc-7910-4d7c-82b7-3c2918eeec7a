.\"
.\" Copyright (c) 1991-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFRGBAImage 3TIFF "October 29, 2004" "libtiff"
.SH NAME
TIFFRGBAImageOK, TIFFRGBAImageBegin, TIFFRGBAImageGet, TIFFRGBAImageEnd
\- read and decode an image into a raster
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.B "typedef unsigned char TIFFRGBValue;"
.B "typedef struct _TIFFRGBAImage TIFFRGBAImage;"
.sp
.BI "int TIFFRGBAImageOK(TIFF *" tif ", char " emsg[1024] ")"
.br
.BI "int TIFFRGBAImageBegin(TIFFRGBAImage *" img ", TIFF* " tif ", int " stopOnError ", char " emsg[1024] ")"
.br
.BI "int TIFFRGBAImageGet(TIFFRGBAImage *" img ", uint32* " raster ", uint32 " width " , uint32 " height ")"
.br
.BI "void TIFFRGBAImageEnd(TIFFRGBAImage *" img ")"
.br
.SH DESCRIPTION
The routines described here provide a high-level interface
through which
.SM TIFF
images may be read into memory.
Images may be strip- or tile-based and have a variety of different
characteristics: bits/sample, samples/pixel, photometric, etc.
Decoding state is encapsulated in a
.I TIFFRGBAImage
structure making it possible to capture state for multiple images
and quickly switch between them.
The target raster format can be customized to a particular application's
needs by installing custom routines that manipulate image data
according to application requirements.
.PP
The default usage for these routines is: check if an image can
be processed using
.IR TIFFRGBAImageOK ,
construct a decoder state block using
.IR TIFFRGBAImageBegin ,
read and decode an image into a target raster using
.IR TIFFRGBAImageGet ,
and then
release resources using
.IR TIFFRGBAImageEnd .
.I TIFFRGBAImageGet
can be called multiple times to decode an image using different
state parameters.
If multiple images are to be displayed and there is not enough
space for each of the decoded rasters, multiple state blocks can
be managed and then calls can be made to
.I TIFFRGBAImageGet
as needed to display an image.
.PP
The generated raster is assumed to be an array of
.I width
times
.I height
32-bit entries, where
.I width
must be less than or equal to the width of the image (\c
.I height
may be any non-zero size).
If the raster dimensions are smaller than the image, the image data
is cropped to the raster bounds.
If the raster height is greater than that of the image, then the
image data are placed in the lower part of the raster.
(Note that the raster is assume to be organized such that the pixel
at location (\fIx\fP,\fIy\fP) is \fIraster\fP[\fIy\fP*\fIwidth\fP+\fIx\fP];
with the raster origin in the 
.B lower-left
hand corner.)
.PP
Raster pixels are 8-bit packed red, green, blue, alpha samples.
The macros
.IR TIFFGetR ,
.IR TIFFGetG ,
.IR TIFFGetB ,
and
.I TIFFGetA
should be used to access individual samples.
Images without Associated Alpha matting information have a constant
Alpha of 1.0 (255).
.PP
.I TIFFRGBAImageGet
converts non-8-bit images by scaling sample values.
Palette, grayscale, bilevel, 
.SM CMYK\c
, and YCbCr images are converted to
.SM RGB
transparently.
Raster pixels are returned uncorrected by any colorimetry information
present in the directory.
.PP
The parameter
.I stopOnError
specifies how to act if an error is encountered while reading
the image.
If
.I stopOnError
is non-zero, then an error will terminate the operation; otherwise
.I TIFFRGBAImageGet
will continue processing data until all the possible data in the
image have been requested.
.SH "ALTERNATE RASTER FORMATS"
To use the core support for reading and processing 
.SM TIFF
images, but write the resulting raster data in a different format
one need only override the ``\fIput methods\fP'' used to store raster data.
These methods are are defined in the
.I TIFFRGBAImage
structure and initially setup by
.I TIFFRGBAImageBegin
to point to routines that pack raster data in the default
.SM ABGR
pixel format.
Two different routines are used according to the physical organization
of the image data in the file: 
.IR PlanarConfiguration =1
(packed samples),
and 
.IR PlanarConfiguration =2
(separated samples).
Note that this mechanism can be used to transform the data before
storing it in the raster.
For example one can convert data
to colormap indices for display on a colormap display.
.SH "SIMULTANEOUS RASTER STORE AND DISPLAY"
It is simple to display an image as it is being read into memory
by overriding the put methods as described above for supporting
alternate raster formats.
Simply keep a reference to the default put methods setup by
.I TIFFRGBAImageBegin
and then invoke them before or after each display operation.
For example, the
.IR tiffgt (1)
utility uses the following put method to update the display as
the raster is being filled:
.sp
.nf
.ft C
static void
putContigAndDraw(TIFFRGBAImage* img, uint32* raster,
    uint32 x, uint32 y, uint32 w, uint32 h,
    int32 fromskew, int32 toskew,
    unsigned char* cp)
{
    (*putContig)(img, raster, x, y, w, h, fromskew, toskew, cp);
    if (x+w == width) {
	w = width;
	if (img->orientation == ORIENTATION_TOPLEFT)
	    lrectwrite(0, y-(h-1), w-1, y, raster-x-(h-1)*w);
	else
	    lrectwrite(0, y, w-1, y+h-1, raster);
    }
}
.ft R
.fi
.sp
(the original routine provided by the library is saved in the
variable 
.IR putContig .)
.SH "SUPPORTING ADDITIONAL TIFF FORMATS"
The
.I TIFFRGBAImage
routines support the most commonly encountered flavors of
.SM TIFF.
It is possible to extend this support by overriding the ``\fIget method\fP''
invoked by
.I TIFFRGBAImageGet
to read 
.SM TIFF
image data.
Details of doing this are a bit involved, it is best to make a copy
of an existing get method and modify it to suit the needs of an
application.
.SH NOTES
Samples must be either 1, 2, 4, 8, or 16 bits.
Colorimetric samples/pixel must be either 1, 3, or 4 (i.e.
.I SamplesPerPixel
minus
.IR ExtraSamples ).
.PP
Palette image colormaps that appear to be incorrectly written
as 8-bit values are automatically scaled to 16-bits.
.SH "RETURN VALUES"
All routines return
1 if the operation was successful.
Otherwise, 0 is returned if an error was encountered and
.I stopOnError
is zero.
.SH DIAGNOSTICS
All error messages are directed to the
.IR TIFFError (3TIFF)
routine.
.PP
.BR "Sorry, can not handle %d-bit pictures" .
The image had
.I BitsPerSample
other than 1, 2, 4, 8, or 16.
.PP
.BR "Sorry, can not handle %d-channel images" .
The image had
.I SamplesPerPixel
other than 1, 3, or 4.
.PP
\fBMissing needed "PhotometricInterpretation" tag\fP.
The image did not have a tag that describes how to display
the data.
.PP
\fBNo "PhotometricInterpretation" tag, assuming RGB\fP.
The image was missing a tag that describes how to display it,
but because it has 3 or 4 samples/pixel, it is assumed to be
.SM RGB.
.PP
\fBNo "PhotometricInterpretation" tag, assuming min-is-black\fP.
The image was missing a tag that describes how to display it,
but because it has 1 sample/pixel, it is assumed to be a grayscale
or bilevel image.
.PP
.BR "No space for photometric conversion table" .
There was insufficient memory for a table used to convert
image samples to 8-bit
.SM RGB.
.PP
\fBMissing required "Colormap" tag\fP.
A Palette image did not have a required
.I Colormap
tag.
.PP
.BR "No space for tile buffer" .
There was insufficient memory to allocate an i/o buffer.
.PP
.BR "No space for strip buffer" .
There was insufficient memory to allocate an i/o buffer.
.PP
.BR "Can not handle format" .
The image has a format (combination of
.IR BitsPerSample ,
.IR SamplesPerPixel ,
and
.IR PhotometricInterpretation )
that can not be handled.
.PP
.BR "No space for B&W mapping table" .
There was insufficient memory to allocate a table used to map
grayscale data to
.SM RGB.
.PP
.BR "No space for Palette mapping table" .
There was insufficient memory to allocate a table used to map
data to 8-bit
.SM RGB.
.SH "SEE ALSO"
.BR TIFFOpen (3TIFF),
.BR TIFFReadRGBAImage (3TIFF),
.BR TIFFReadRGBAImageOriented (3TIFF),
.BR TIFFReadRGBAStrip (3TIFF),
.BR TIFFReadRGBATile (3TIFF),
.BR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
