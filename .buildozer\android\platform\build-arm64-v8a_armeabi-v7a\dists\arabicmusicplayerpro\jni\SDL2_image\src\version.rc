
#include "winresrc.h"

LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US

/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 2,8,0,0
 PRODUCTVERSION 2,8,0,0
 FILEFLAGSMASK 0x3fL
 FILEFLAGS 0x0L
 FILEOS 0x40004L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "\0"
            VALUE "FileDescription", "SDL_image\0"
            VALUE "FileVersion", "2, 8, 0, 0\0"
            VALUE "InternalName", "SDL_image\0"
            VALUE "LegalCopyright", "Copyright (C) 2023 Sam <PERSON>\0"
            VALUE "OriginalFilename", "SDL_image.dll\0"
            VALUE "ProductName", "Simple DirectMedia Layer\0"
            VALUE "ProductVersion", "2, 8, 0, 0\0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
