# change DEPS_INC in order to point to the dependency headers.
DEPS_INC=-IC:\SDL2DEV\h\SDL2
# change DEPS_LIB in order to point to the dependency libraries.
DEPS_LIB=C:\SDL2DEV\lib

TARGETS = testimage.exe

OBJS = $(TARGETS:.exe=.obj)

all: $(TARGETS)

INCPATH = -I$(%WATCOM)/h/os2 -I$(%WATCOM)/h -I.. $(DEPS_INC)
CFLAGS_DEF = $(INCPATH) -bt=os2 -d0 -q -bm -5s -fp5 -fpi87 -sg -oteanbmier
CFLAGS_EXE = $(CFLAGS_DEF)
CFLAGS = $(CFLAGS_EXE) -ei -5s

CFLAGS+= -DLOAD_JPG -DLOAD_PNG -DLOAD_BMP -DLOAD_GIF -DLOAD_LBM &
         -DLOAD_PCX -DLOAD_PNM -DLOAD_TGA -DLOAD_XCF -DLOAD_XPM &
         -DLOAD_XV -DLOAD_SVG -DLOAD_TIF -DLOAD_WEBP -DLOAD_QOI

#CFLAGS+= -DLOAD_AVIF
#CFLAGS+= -DLOAD_JXL
CFLAGS+= -DSDL_IMAGE_SAVE_JPG=1
CFLAGS+= -DSDL_IMAGE_SAVE_PNG=1

LIBPATH = ..
LIBS = SDL2img.lib SDL2test.lib SDL2.lib

.obj.exe:
  wlink SYS os2v2 libpath $(LIBPATH) libpath $(DEPS_LIB) lib {$(LIBS)} op q file {$<} N $*

testimage.obj: main.c
  wcc386 $(CFLAGS) -fo=$^@ $<

clean: .SYMBOLIC
  @if exist *.obj rm *.obj
  @if exist *.err rm *.err

distclean: clean .SYMBOLIC
  @if exist *.exe rm *.exe
