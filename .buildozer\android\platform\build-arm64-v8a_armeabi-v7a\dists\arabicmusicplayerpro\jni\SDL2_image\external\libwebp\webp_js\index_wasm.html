<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>simple Javascript WebP decoding demo, using Web-Assembly (WASM)</title>
  <script type="text/javascript">
    var Module = {
      noInitialRun : true
    };
  </script>
  <script type="text/javascript">

'use strict';

// main wrapper for the function decoding a WebP into a canvas object
var WebpToCanvas;

function init() {
  var xhr = new XMLHttpRequest();
  xhr.open('GET', 'webp_wasm.wasm', true);
  xhr.responseType = 'arraybuffer';
  xhr.onload = function() {
    Module.wasmBinary = xhr.response;
    var script = document.createElement('script');
    script.src = "webp_wasm.js";
    document.body.appendChild(script);
  };
  xhr.send(null);
}
window.onload = init;

function decode(webp_data, canvas_id) {
  var result;
  if (Module["asm"] != undefined) {
    // wrapper for the function decoding a WebP into a canvas object
    WebpToCanvas = Module.cwrap('WebpToSDL', 'number', ['array', 'number']);
    // get the canvas to decode into
    var canvas = document.getElementById(canvas_id);
    if (canvas == null) return;
    // clear previous picture (if any)
    Module.canvas = canvas;
    canvas.getContext('2d').clearRect(0, 0, canvas.width, canvas.height);
    // decode and measure timing
    var start = new Date();
    var ret = WebpToCanvas(webp_data, webp_data.length);
    var end = new Date();
    var decode_time = end - start;
    result = 'decoding time: ' + decode_time +' ms.';
  } else {
    result = "WASM module not finished loading! Please retry";
  }
  // display timing result
  var speed_result = document.getElementById('timing');
  if (speed_result != null) {
    speed_result.innerHTML = '<p>'+ result + '</p>';
  }
}

function loadfile(filename, canvas_id) {
  var xhr = new XMLHttpRequest();
  xhr.open('GET', filename);
  xhr.responseType = 'arraybuffer';
  xhr.onreadystatechange = function() {
    if (xhr.readyState == 4 && xhr.status == 200) {
      var webp_data = new Uint8Array(xhr.response);
      decode(webp_data, canvas_id);
    }
  };
  xhr.send();
}
  </script>
</head>

<body>
  <p>
    <strong>WebP demo using Web-Assembly</strong> -
  </p>
  <p>
    WASM version of the WebP decoder, using libwebp compiled with
    <a href="https://github.com/kripken/emscripten/wiki">Emscripten</a>.
  </p>
  <p id="image_buttons">
    <input type="button" value="test image!"
           onclick="loadfile('./test_webp_wasm.webp', 'output_canvas')">
  </p>
  <p id="timing">Timing: N/A</p>
  <canvas id="output_canvas">Your browser does not support canvas</canvas>
</body>
</html>
