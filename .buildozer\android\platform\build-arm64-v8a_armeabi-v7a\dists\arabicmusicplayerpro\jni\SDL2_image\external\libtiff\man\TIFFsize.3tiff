.\"
.\" Copyright (c) 1988-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFSIZE 3TIFF "October 15, 1995" "libtiff"
.SH NAME
TIFFScanlineSize, TIFFRasterScanlineSize,
\- return the size of various items associated with an open
.SM TIFF
file
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.BI "tsize_t TIFFRasterScanlineSize(TIFF *" tif ")"
.br
.BI "tsize_t TIFFScanlineSize(TIFF *" tif ")"
.SH DESCRIPTION
.I TIFFScanlineSize
returns the size in bytes of a row of data as it would be returned in a call
to
.IR TIFFReadScanline ,
or as it would be expected in a call to
.IR TIFFWriteScanline .
.PP
.I TIFFRasterScanlineSize
returns the size in bytes of a complete decoded and packed raster scanline.
Note that this value may be different from the value returned by
.I TIFFScanlineSize
if data is stored as separate planes.
.SH DIAGNOSTICS
None.
.SH "SEE ALSO"
.BR TIFFOpen (3TIFF),
.BR TIFFReadScanline (3TIFF),
.BR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
