.\"
.\" Copyright (c) 1991-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFReadRGBAImage 3TIFF "October 13, 2006" "libtiff"
.SH NAME
TIFFReadRGBAImage, TIFFReadRGBAImageOriented \- read and decode an image
into a fixed-format raster
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.B "#define TIFFGetR(abgr) ((abgr) & 0xff)"
.br
.B "#define TIFFGetG(abgr) (((abgr) >> 8) & 0xff)"
.br
.B "#define TIFFGetB(abgr) (((abgr) >> 16) & 0xff)"
.br
.B "#define TIFFGetA(abgr) (((abgr) >> 24) & 0xff)"
.sp
.BI "int TIFFReadRGBAImage(TIFF *" tif ", uint32 " width ", uint32 " height ", uint32 *" raster ", int " stopOnError ")"
.br
.BI "int TIFFReadRGBAImageOriented(TIFF *" tif ", uint32 " width ", uint32 " height ", uint32 *" raster ", int " orientation ", int " stopOnError ")"
.br
.SH DESCRIPTION
.IR TIFFReadRGBAImage
reads a strip- or tile-based image into memory, storing the
result in the user supplied
.IR raster .
The raster is assumed to be an array of
.I width
times
.I height
32-bit entries, where
.I width
must be less than or equal to the width of the image (\c
.I height
may be any non-zero size).
If the raster dimensions are smaller than the image, the image data
is cropped to the raster bounds.
If the raster height is greater than that of the image, then the
image data are placed in the lower part of the raster.
(Note that the raster is assume to be organized such that the pixel
at location (\fIx\fP,\fIy\fP) is \fIraster\fP[\fIy\fP*\fIwidth\fP+\fIx\fP];
with the raster origin in the lower-left hand corner.)
.PP
.IR TIFFReadRGBAImageOriented
works like
.IR TIFFReadRGBAImage
with except of that user can specify the raster origin position with the
.I orientation
parameter. Four orientations supported:
.TP
.B ORIENTATION_TOPLEFT
origin in top-left corner,
.TP
.B ORIENTATION_TOPRIGHT
origin in top-right corner,
.TP
.B ORIENTATION_BOTLEFT
origin in bottom-left corner
and
.TP
.B ORIENTATION_BOTRIGHT
origin in bottom-right corner.
.LP
If you choose
.B ORIENTATION_BOTLEFT
result will be the same as returned by the
.IR TIFFReadRGBAImage.
.PP
Raster pixels are 8-bit packed red, green, blue, alpha samples.
The macros
.IR TIFFGetR ,
.IR TIFFGetG ,
.IR TIFFGetB ,
and
.I TIFFGetA
should be used to access individual samples.
Images without Associated Alpha matting information have a constant
Alpha of 1.0 (255).
.PP
.I TIFFReadRGBAImage
converts non-8-bit images by scaling sample values.
Palette, grayscale, bilevel, 
.SM CMYK\c
, and YCbCr images are converted to
.SM RGB
transparently.
Raster pixels are returned uncorrected by any colorimetry information
present in the directory.
.PP
The paramater
.I stopOnError
specifies how to act if an error is encountered while reading
the image.
If
.I stopOnError
is non-zero, then an error will terminate the operation; otherwise
.I TIFFReadRGBAImage
will continue processing data until all the possible data in the
image have been requested.
.SH NOTES
In C++ the
.I stopOnError
parameter defaults to 0.
.PP
Samples must be either 1, 2, 4, 8, or 16 bits.
Colorimetric samples/pixel must be either 1, 3, or 4 (i.e.
.I SamplesPerPixel
minus
.IR ExtraSamples ).
.PP
Palettte image colormaps that appear to be incorrectly written
as 8-bit values are automatically scaled to 16-bits.
.PP
.I TIFFReadRGBAImage
is just a wrapper around the more general
.IR TIFFRGBAImage (3TIFF)
facilities.
.SH "RETURN VALUES"
1 is returned if the image was successfully read and converted.
Otherwise, 0 is returned if an error was encountered and
.I stopOnError
is zero.
.SH DIAGNOSTICS
All error messages are directed to the
.IR TIFFError (3TIFF)
routine.
.PP
.BR "Sorry, can not handle %d-bit pictures" .
The image had
.I BitsPerSample
other than 1, 2, 4, 8, or 16.
.PP
.BR "Sorry, can not handle %d-channel images" .
The image had
.I SamplesPerPixel
other than 1, 3, or 4.
.PP
\fBMissing needed "PhotometricInterpretation" tag\fP.
The image did not have a tag that describes how to display
the data.
.PP
\fBNo "PhotometricInterpretation" tag, assuming RGB\fP.
The image was missing a tag that describes how to display it,
but because it has 3 or 4 samples/pixel, it is assumed to be
.SM RGB.
.PP
\fBNo "PhotometricInterpretation" tag, assuming min-is-black\fP.
The image was missing a tag that describes how to display it,
but because it has 1 sample/pixel, it is assumed to be a grayscale
or bilevel image.
.PP
.BR "No space for photometric conversion table" .
There was insufficient memory for a table used to convert
image samples to 8-bit
.SM RGB.
.PP
\fBMissing required "Colormap" tag\fP.
A Palette image did not have a required
.I Colormap
tag.
.PP
.BR "No space for tile buffer" .
There was insufficient memory to allocate an i/o buffer.
.PP
.BR "No space for strip buffer" .
There was insufficient memory to allocate an i/o buffer.
.PP
.BR "Can not handle format" .
The image has a format (combination of
.IR BitsPerSample ,
.IR SamplesPerPixel ,
and
.IR PhotometricInterpretation )
that
.I TIFFReadRGBAImage
can not handle.
.PP
.BR "No space for B&W mapping table" .
There was insufficient memory to allocate a table used to map
grayscale data to
.SM RGB.
.PP
.BR "No space for Palette mapping table" .
There was insufficient memory to allocate a table used to map
data to 8-bit
.SM RGB.
.SH "SEE ALSO"
.BR TIFFOpen (3TIFF),
.BR TIFFRGBAImage (3TIFF),
.BR TIFFReadRGBAStrip (3TIFF),
.BR TIFFReadRGBATile (3TIFF),
.BR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
