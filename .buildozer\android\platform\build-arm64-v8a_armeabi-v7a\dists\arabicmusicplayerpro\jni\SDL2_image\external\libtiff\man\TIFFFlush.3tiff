.\"
.\" Copyright (c) 1988-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFFlush 3TIFF "December 16, 1991" "libtiff"
.SH NAME
TIFFFlush, TIFFFlushData \- flush pending writes to an open
.SM TIFF
file
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.BI "int TIFFFlush(TIFF *" tif ")"
.br
.BI "int TIFFFlushData(TIFF *" tif ")"
.SH DESCRIPTION
.IR TIFFFlush
causes any pending writes for the specified file (including writes for the
current directory) to be done. In normal operation this call is never needed \-
the library automatically does any flushing required.
.PP
.IR TIFFFlushData
flushes any pending image data for the specified file to be written out;
directory-related data are not flushed. In normal operation this call is never
needed \- the library automatically does any flushing required.
.SH "RETURN VALUES"
0 is returned if an error is encountered, otherwise 1 is returned.
.SH DIAGNOSTICS
All error messages are directed to the
.BR TIFFError (3TIFF)
routine.
.SH "SEE ALSO"
.BR TIFFOpen (3TIFF),
.BR TIFFWriteEncodedStrip (3TIFF),
.BR TIFFWriteEncodedTile (3TIFF),
.BR TIFFWriteRawStrip (3TIFF),
.BR TIFFWriteRawTile (3TIFF),
.BR TIFFWriteScanline (3TIFF),
.BR TIFFWriteTile (3TIFF)
.BR libtiff (3TIFF),
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
