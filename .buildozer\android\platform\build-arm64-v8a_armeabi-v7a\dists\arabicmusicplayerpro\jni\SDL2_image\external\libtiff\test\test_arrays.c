/*
 * Copyright (c) 2004, <PERSON><PERSON>  <<EMAIL>>
 *
 * Permission to use, copy, modify, distribute, and sell this software and 
 * its documentation for any purpose is hereby granted without fee, provided
 * that (i) the above copyright notices and this permission notice appear in
 * all copies of the software and related documentation, and (ii) the names of
 * <PERSON>r and Silicon Graphics may not be used in any advertising or
 * publicity relating to the software without the specific, prior written
 * permission of <PERSON> and Silicon Graphics.
 * 
 * THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
 * EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
 * WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
 * 
 * IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
 * ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
 * OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
 * W<PERSON><PERSON>HER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
 * LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
 * OF THIS SOFTWARE.
 */

/*
 * TIFF Library
 *
 * Numerical arrays used to test libtiff's read/write functions.
 */

#include <stddef.h>

#include "test_arrays.h"

const unsigned char byte_array1[XSIZE * YSIZE]=
{
86, 84, 86, 90, 89, 85, 90, 78, 77, 79, 75, 77, 79, 86,
87, 83, 82, 87, 89, 88, 86, 87, 88, 87, 81, 84, 85, 85,
84, 86, 88, 91, 96, 95, 97, 95, 89,
85, 82, 81, 88, 89, 85, 89, 83, 74, 79, 76, 77, 80, 87,
87, 84, 84, 88, 90, 89, 87, 85, 87, 88, 83, 80, 82, 84,
85, 87, 90, 95, 96, 95, 95, 92, 90,
85, 81, 79, 84, 90, 87, 88, 88, 73, 79, 75, 76, 79, 88,
88, 87, 85, 90, 92, 89, 88, 88, 87, 86, 84, 82, 82, 83,
87, 89, 93, 94, 93, 93, 92, 92, 96,
85, 82, 76, 80, 88, 89, 88, 89, 73, 80, 75, 75, 77, 89,
92, 93, 91, 89, 94, 92, 90, 89, 88, 84, 84, 82, 82, 85,
88, 91, 94, 93, 93, 89, 90, 96, 96,
87, 83, 75, 77, 83, 89, 90, 90, 74, 78, 76, 76, 76, 84,
94, 100, 89, 92, 94, 92, 90, 89, 90, 85, 84, 83, 83, 87,
91, 92, 88, 92, 91, 88, 90, 97, 95,
89, 83, 74, 77, 82, 84, 90, 92, 78, 72, 76, 75, 75, 81,
95, 101, 95, 92, 95, 93, 90, 89, 90, 87, 86, 84, 86, 88,
90, 90, 87, 90, 89, 90, 89, 98, 98,
92, 84, 75, 76, 81, 81, 86, 91, 81, 72, 74, 74, 75, 81,
104, 108, 93, 92, 95, 94, 88, 87, 89, 87, 85, 85, 88, 89,
93, 91, 88, 88, 91, 88, 91, 106, 108,
93, 89, 78, 75, 77, 80, 85, 86, 85, 73, 72, 73, 74, 79,
102, 101, 88, 92, 93, 91, 87, 87, 86, 87, 85, 86, 88, 89,
94, 94, 90, 88, 85, 86, 98, 109, 113,
92, 93, 83, 76, 74, 79, 84, 85, 81, 75, 72, 73, 74, 79,
105, 86, 86, 92, 96, 98, 104, 86, 85, 85, 85, 88, 90, 90,
93, 92, 88, 87, 86, 89, 97, 110, 109,
92, 93, 89, 78, 79, 78, 89, 84, 75, 76, 73, 72, 73, 78,
105, 83, 82, 88, 83, 107, 95, 84, 85, 84, 86, 87, 90, 91,
92, 90, 88, 87, 89, 90, 91, 99, 107,
96, 94, 91, 82, 84, 86, 91, 87, 75, 74, 73, 73, 73, 77,
101, 86, 83, 89, 92, 99, 98, 86, 86, 87, 83, 84, 89, 89,
92, 92, 92, 96, 96, 87, 91, 90, 98,
96, 97, 94, 87, 88, 89, 92, 90, 79, 72, 73, 73, 74, 77,
100, 92, 84, 86, 98, 100, 92, 87, 88, 88, 84, 83, 87, 89,
91, 94, 94, 96, 93, 87, 87, 84, 109,
93, 92, 95, 92, 94, 93, 92, 91, 82, 72, 73, 74, 74, 76,
95, 89, 85, 84, 102, 89, 85, 88, 94, 86, 82, 83, 82, 91,
94, 97, 90, 92, 85, 90, 85, 79, 125,
89, 96, 94, 90, 94, 95, 91, 91, 85, 76, 72, 73, 74, 75,
88, 100, 83, 84, 84, 83, 85, 88, 90, 85, 84, 83, 84, 88,
92, 93, 90, 89, 84, 90, 94, 79, 139,
93, 97, 97, 93, 92, 95, 91, 90, 87, 81, 74, 73, 73, 74,
85, 97, 95, 95, 89, 86, 86, 92, 87, 85, 84, 90, 86, 85,
91, 87, 87, 86, 93, 124, 140, 106, 143,
101, 95, 97, 97, 96, 95, 84, 88, 87, 82, 78, 73, 73, 74,
82, 92, 104, 95, 88, 89, 87, 89, 86, 85, 86, 87, 87, 81,
81, 83, 91, 106, 131, 153, 151, 123, 133,
99, 101, 102, 99, 96, 90, 83, 82, 85, 84, 79, 76, 74, 74,
78, 81, 89, 96, 90, 93, 88, 88, 86, 88, 89, 95, 89, 82,
81, 85, 104, 118, 141, 160, 129, 137, 147,
103, 104, 98, 99, 90, 88, 81, 76, 81, 83, 79, 77, 75, 75,
75, 76, 80, 90, 94, 87, 86, 87, 92, 85, 85, 85, 87, 87,
89, 91, 112, 115, 145, 154, 145, 141, 147,
106, 103, 100, 99, 92, 82, 78, 75, 78, 81, 79, 77, 77, 78,
78, 76, 77, 81, 89, 87, 84, 84, 90, 86, 85, 84, 80, 85,
97, 104, 119, 119, 149, 147, 144, 146, 152,
107, 105, 103, 100, 93, 83, 78, 74, 74, 79, 78, 77, 76, 78,
80, 79, 76, 78, 83, 84, 81, 81, 84, 83, 82, 78, 78, 85,
86, 97, 105, 114, 145, 146, 148, 147, 150,
107, 105, 103, 97, 92, 84, 72, 72, 75, 77, 76, 75, 76, 79,
80, 80, 77, 76, 82, 81, 80, 81, 80, 80, 80, 77, 74, 74,
73, 77, 91, 110, 132, 141, 152, 152, 145,
107, 105, 103, 96, 92, 86, 73, 71, 73, 75, 75, 76, 76, 78,
80, 80, 80, 98, 80, 80, 82, 82, 80, 78, 76, 73, 71, 72,
71, 74, 80, 108, 119, 136, 158, 142, 137,
107, 104, 101, 97, 85, 87, 75, 70, 70, 74, 74, 75, 77, 78,
80, 82, 110, 117, 110, 78, 81, 83, 81, 78, 76, 73, 71, 69,
68, 71, 74, 95, 120, 138, 148, 143, 139
};

const size_t byte_array1_size = sizeof(byte_array1);

const unsigned char byte_array2[YSIZE * XSIZE] =
{
77, 73, 76, 80, 79, 75, 82, 65, 62, 64, 59, 59, 61, 72,
70, 67, 65, 70, 71, 70, 68, 66, 65, 67, 66, 66, 66, 66,
66, 66, 66, 66, 66, 65, 63, 63, 62,
75, 71, 71, 79, 81, 75, 81, 73, 59, 65, 60, 60, 64, 73,
73, 68, 66, 70, 72, 71, 68, 66, 66, 67, 66, 66, 66, 67,
67, 67, 66, 67, 66, 64, 63, 63, 63,
76, 71, 66, 73, 81, 78, 80, 79, 59, 66, 60, 59, 62, 74,
74, 71, 67, 70, 73, 71, 68, 66, 65, 65, 66, 66, 67, 67,
67, 67, 67, 67, 66, 64, 64, 64, 64,
76, 72, 64, 68, 79, 81, 80, 80, 59, 68, 60, 59, 60, 75,
75, 73, 67, 68, 73, 72, 68, 66, 65, 63, 67, 67, 67, 67,
68, 67, 67, 66, 65, 64, 65, 65, 65,
79, 72, 63, 66, 73, 80, 83, 82, 60, 65, 61, 61, 60, 66,
75, 75, 65, 70, 73, 72, 68, 66, 65, 64, 68, 67, 68, 68,
68, 67, 67, 66, 65, 65, 65, 66, 65,
81, 73, 62, 65, 72, 74, 82, 85, 66, 59, 62, 60, 60, 63,
75, 76, 68, 69, 72, 72, 68, 66, 66, 65, 67, 68, 68, 68,
68, 68, 66, 66, 64, 66, 65, 66, 66,
84, 74, 64, 64, 70, 71, 78, 84, 70, 58, 60, 59, 59, 63,
75, 80, 73, 67, 72, 72, 68, 66, 66, 65, 66, 68, 68, 68,
68, 68, 66, 65, 65, 65, 66, 67, 68,
87, 81, 66, 63, 65, 68, 76, 76, 75, 59, 58, 59, 59, 60,
71, 92, 65, 64, 74, 72, 69, 67, 65, 65, 65, 68, 69, 68,
69, 67, 65, 65, 65, 65, 67, 68, 69,
86, 86, 73, 64, 62, 67, 75, 76, 70, 61, 58, 58, 59, 60,
81, 68, 59, 63, 74, 90, 99, 67, 65, 65, 64, 67, 68, 68,
68, 67, 65, 65, 66, 65, 66, 68, 68,
85, 85, 80, 66, 67, 67, 81, 74, 62, 63, 59, 58, 58, 60,
93, 61, 59, 59, 68, 115, 76, 67, 66, 64, 64, 66, 68, 68,
68, 66, 65, 65, 66, 65, 64, 65, 69,
90, 87, 83, 71, 74, 77, 83, 79, 63, 60, 59, 59, 58, 58,
90, 61, 59, 59, 67, 80, 71, 68, 66, 64, 63, 63, 68, 68,
68, 66, 65, 66, 67, 65, 64, 62, 87,
91, 92, 86, 76, 78, 81, 85, 82, 67, 59, 59, 59, 59, 60,
88, 72, 59, 60, 74, 80, 70, 67, 66, 64, 62, 60, 65, 68,
67, 66, 65, 67, 66, 64, 62, 59, 111,
84, 84, 87, 85, 87, 85, 84, 84, 72, 59, 59, 59, 59, 59,
73, 71, 62, 59, 100, 70, 70, 67, 66, 64, 60, 58, 58, 67,
68, 66, 65, 66, 64, 63, 59, 56, 131,
80, 90, 87, 83, 88, 89, 84, 83, 76, 64, 59, 59, 59, 58,
59, 97, 64, 62, 71, 68, 70, 73, 66, 63, 61, 58, 58, 62,
67, 66, 64, 65, 63, 63, 61, 57, 149,
86, 91, 92, 87, 85, 88, 83, 81, 78, 69, 61, 59, 59, 59,
59, 61, 83, 72, 67, 67, 69, 69, 66, 64, 61, 72, 56, 57,
64, 64, 64, 64, 65, 115, 150, 93, 151,
97, 89, 91, 92, 89, 88, 74, 80, 78, 71, 65, 59, 58, 59,
58, 59, 71, 72, 67, 70, 70, 69, 67, 64, 63, 66, 56, 54,
57, 59, 64, 87, 139, 162, 160, 128, 141,
94, 96, 97, 94, 89, 82, 71, 70, 76, 73, 67, 61, 59, 59,
58, 59, 61, 71, 67, 75, 70, 68, 70, 65, 63, 63, 59, 56,
54, 55, 90, 121, 149, 168, 138, 144, 157,
99, 100, 93, 93, 82, 80, 70, 62, 70, 72, 67, 63, 60, 60,
58, 58, 60, 68, 70, 70, 69, 68, 79, 68, 64, 62, 60, 59,
57, 57, 88, 120, 151, 162, 154, 149, 155,
103, 99, 95, 94, 84, 73, 67, 62, 65, 69, 67, 64, 63, 64,
63, 59, 60, 65, 71, 69, 69, 67, 78, 65, 63, 61, 59, 61,
60, 68, 100, 128, 155, 155, 152, 155, 164,
104, 102, 99, 95, 86, 74, 67, 61, 61, 66, 65, 63, 63, 64,
65, 63, 60, 63, 70, 69, 67, 67, 67, 65, 62, 60, 58, 57,
62, 58, 71, 117, 150, 154, 157, 155, 163,
104, 101, 99, 91, 84, 74, 59, 59, 62, 64, 63, 61, 62, 64,
64, 64, 61, 60, 69, 68, 67, 69, 67, 65, 62, 59, 58, 57,
57, 56, 59, 104, 137, 147, 159, 161, 158,
104, 101, 99, 90, 85, 77, 60, 57, 60, 62, 62, 62, 63, 64,
65, 65, 66, 100, 67, 67, 69, 69, 67, 65, 63, 60, 58, 56,
54, 55, 56, 77, 122, 142, 166, 157, 150,
104, 101, 97, 92, 77, 79, 64, 57, 57, 62, 62, 62, 64, 65,
66, 65, 115, 138, 129, 64, 68, 70, 68, 66, 64, 60, 58, 56,
53, 53, 56, 62, 115, 143, 157, 156, 159
};

const size_t byte_array2_size = sizeof(byte_array2);

const unsigned char byte_array3[YSIZE * XSIZE] =
{
211, 221, 216, 201, 205, 216, 195, 236, 244, 237, 250, 250, 248, 218,
223, 232, 236, 224, 221, 221, 227, 231, 232, 227, 229, 227, 227, 225,
227, 225, 226, 226, 226, 228, 234, 234, 234, 216, 226, 228, 205, 200,
214, 198, 215, 250, 233, 247, 250, 242, 219, 220, 229, 235, 225, 217,
220, 227, 232, 230, 228, 229, 228, 227, 224, 225, 223, 226, 225, 226,
230, 233, 233, 234, 213, 227, 237, 220, 200, 204, 202, 201, 248, 231,
246, 250, 245, 214, 215, 223, 232, 225, 218, 218, 225, 230, 232, 231,
229, 227, 225, 224, 223, 226, 224, 225, 228, 229, 230, 232, 231, 215,
223, 242, 233, 206, 200, 201, 197, 250, 227, 250, 249, 248, 211, 212,
216, 233, 229, 216, 218, 225, 230, 232, 237, 226, 224, 224, 223, 225,
225, 224, 225, 228, 229, 231, 229, 231, 208, 220, 247, 238, 221, 202,
194, 194, 245, 237, 247, 247, 249, 234, 210, 212, 237, 222, 219, 217,
226, 229, 232, 235, 222, 222, 223, 223, 223, 224, 224, 227, 226, 229,
229, 228, 231, 200, 221, 247, 239, 224, 217, 196, 189, 229, 248, 245,
248, 250, 241, 210, 210, 230, 225, 218, 218, 224, 230, 230, 229, 224,
222, 222, 222, 222, 223, 225, 226, 231, 226, 228, 229, 230, 191, 216,
246, 245, 226, 228, 207, 191, 221, 251, 248, 249, 251, 245, 214, 214,
233, 229, 217, 217, 224, 229, 230, 229, 225, 220, 223, 221, 222, 224,
224, 227, 230, 227, 226, 229, 230, 187, 199, 238, 248, 242, 231, 213,
211, 209, 246, 248, 251, 251, 250, 226, 215, 236, 237, 217, 215, 222,
226, 229, 229, 227, 222, 222, 223, 222, 225, 227, 228, 226, 227, 228,
228, 230, 188, 189, 221, 243, 247, 237, 215, 209, 223, 241, 248, 248,
250, 248, 228, 234, 251, 239, 219, 210, 205, 224, 229, 228, 230, 221,
223, 223, 222, 226, 229, 228, 224, 227, 229, 230, 232, 190, 190, 201,
235, 236, 238, 198, 214, 243, 238, 248, 248, 250, 249, 215, 244, 250,
250, 240, 168, 220, 224, 228, 230, 231, 226, 221, 224, 223, 226, 230,
227, 226, 226, 230, 233, 234, 179, 185, 195, 224, 215, 210, 195, 204,
239, 245, 250, 250, 252, 254, 216, 243, 249, 249, 233, 210, 215, 223,
227, 230, 234, 234, 224, 223, 223, 227, 230, 226, 226, 228, 231, 235,
212, 178, 174, 190, 211, 207, 199, 189, 194, 230, 250, 250, 250, 253,
253, 222, 225, 250, 248, 218, 216, 217, 225, 226, 232, 239, 242, 229,
223, 224, 229, 230, 225, 228, 230, 236, 241, 183, 194, 194, 185, 190,
185, 190, 191, 191, 219, 250, 251, 250, 253, 254, 241, 225, 246, 249,
198, 217, 220, 224, 225, 234, 241, 242, 246, 224, 223, 227, 229, 227,
228, 234, 237, 245, 149, 203, 178, 182, 193, 185, 179, 191, 194, 211,
236, 252, 252, 254, 254, 253, 192, 240, 244, 235, 224, 220, 229, 224,
236, 239, 243, 244, 236, 224, 229, 230, 229, 231, 230, 233, 244, 128,
188, 177, 171, 184, 191, 182, 196, 197, 208, 224, 247, 253, 255, 252,
250, 248, 226, 216, 228, 230, 220, 220, 227, 234, 237, 231, 247, 244,
231, 231, 229, 228, 229, 182, 128, 196, 118, 160, 182, 174, 172, 179,
183, 216, 203, 206, 220, 236, 253, 254, 253, 253, 249, 225, 219, 232,
230, 220, 224, 227, 233, 237, 234, 244, 250, 245, 240, 224, 212, 174,
123, 124, 176, 127, 171, 163, 161, 167, 177, 198, 221, 228, 212, 215,
233, 245, 252, 255, 253, 252, 251, 223, 231, 216, 222, 227, 231, 231,
234, 227, 238, 245, 249, 244, 210, 177, 124, 129, 134, 124, 113, 156,
155, 172, 168, 197, 201, 224, 247, 224, 219, 233, 242, 249, 250, 252,
254, 252, 230, 230, 224, 224, 225, 225, 227, 232, 232, 235, 239, 239,
241, 213, 178, 131, 128, 128, 120, 114, 149, 157, 165, 168, 191, 218,
231, 246, 237, 226, 234, 241, 243, 239, 244, 252, 249, 237, 225, 226,
224, 227, 220, 229, 235, 235, 239, 238, 236, 230, 204, 177, 125, 131,
127, 117, 111, 146, 151, 158, 166, 187, 215, 230, 246, 246, 231, 238,
243, 246, 243, 241, 244, 253, 245, 226, 226, 229, 229, 229, 231, 236,
238, 241, 240, 241, 235, 224, 188, 134, 123, 127, 116, 116, 144, 151,
158, 173, 190, 214, 251, 250, 243, 236, 242, 249, 246, 241, 241, 244,
251, 251, 228, 230, 230, 226, 232, 231, 236, 241, 243, 244, 243, 243,
235, 200, 150, 128, 122, 119, 117, 144, 151, 156, 176, 190, 207, 246,
253, 244, 239, 244, 246, 244, 242, 240, 243, 249, 198, 239, 234, 226,
226, 228, 234, 238, 241, 244, 245, 247, 250, 244, 219, 182, 138, 118,
118, 116, 143, 150, 162, 173, 208, 205, 238, 253, 251, 241, 244, 244,
242, 243, 238, 246, 193, 146, 173, 246, 231, 223, 230, 232, 236, 240,
245, 247, 252, 252, 245, 233, 195, 138, 114, 118, 108
};

const size_t byte_array3_size = sizeof(byte_array3);

const float array_float1[YSIZE * XSIZE] =
{
234.866, 229.404, 234.866, 245.790, 243.059, 232.135, 245.790, 213.018,
210.287, 215.749, 204.825, 210.287, 215.749, 234.866, 237.597, 226.673,
223.942, 237.597, 243.059, 240.328, 234.866, 237.597, 240.328, 237.597,
221.211, 229.404, 232.135, 232.135, 229.404, 234.866, 240.328, 248.521,
262.176, 259.445, 264.907, 259.445, 243.059,
232.135, 223.942, 221.211, 240.328, 243.059, 232.135, 243.059, 226.673,
202.094, 215.749, 207.556, 210.287, 218.480, 237.597, 237.597, 229.404,
229.404, 240.328, 245.790, 243.059, 237.597, 232.135, 237.597, 240.328,
226.673, 218.480, 223.942, 229.404, 232.135, 237.597, 245.790, 259.445,
262.176, 259.445, 259.445, 251.252, 245.790,
232.135, 221.211, 215.749, 229.404, 245.790, 237.597, 240.328, 240.328,
199.363, 215.749, 204.825, 207.556, 215.749, 240.328, 240.328, 237.597,
232.135, 245.790, 251.252, 243.059, 240.328, 240.328, 237.597, 234.866,
229.404, 223.942, 223.942, 226.673, 237.597, 243.059, 253.983, 256.714,
253.983, 253.983, 251.252, 251.252, 262.176,
232.135, 223.942, 207.556, 218.480, 240.328, 243.059, 240.328, 243.059,
199.363, 218.480, 204.825, 204.825, 210.287, 243.059, 251.252, 253.983,
248.521, 243.059, 256.714, 251.252, 245.790, 243.059, 240.328, 229.404,
229.404, 223.942, 223.942, 232.135, 240.328, 248.521, 256.714, 253.983,
253.983, 243.059, 245.790, 262.176, 262.176,
237.597, 226.673, 204.825, 210.287, 226.673, 243.059, 245.790, 245.790,
202.094, 213.018, 207.556, 207.556, 207.556, 229.404, 256.714, 273.100,
243.059, 251.252, 256.714, 251.252, 245.790, 243.059, 245.790, 232.135,
229.404, 226.673, 226.673, 237.597, 248.521, 251.252, 240.328, 251.252,
248.521, 240.328, 245.790, 264.907, 259.445,
243.059, 226.673, 202.094, 210.287, 223.942, 229.404, 245.790, 251.252,
213.018, 196.632, 207.556, 204.825, 204.825, 221.211, 259.445, 275.831,
259.445, 251.252, 259.445, 253.983, 245.790, 243.059, 245.790, 237.597,
234.866, 229.404, 234.866, 240.328, 245.790, 245.790, 237.597, 245.790,
243.059, 245.790, 243.059, 267.638, 267.638,
251.252, 229.404, 204.825, 207.556, 221.211, 221.211, 234.866, 248.521,
221.211, 196.632, 202.094, 202.094, 204.825, 221.211, 284.024, 294.948,
253.983, 251.252, 259.445, 256.714, 240.328, 237.597, 243.059, 237.597,
232.135, 232.135, 240.328, 243.059, 253.983, 248.521, 240.328, 240.328,
248.521, 240.328, 248.521, 289.486, 294.948,
253.983, 243.059, 213.018, 204.825, 210.287, 218.480, 232.135, 234.866,
232.135, 199.363, 196.632, 199.363, 202.094, 215.749, 278.562, 275.831,
240.328, 251.252, 253.983, 248.521, 237.597, 237.597, 234.866, 237.597,
232.135, 234.866, 240.328, 243.059, 256.714, 256.714, 245.790, 240.328,
232.135, 234.866, 267.638, 297.679, 308.603,
251.252, 253.983, 226.673, 207.556, 202.094, 215.749, 229.404, 232.135,
221.211, 204.825, 196.632, 199.363, 202.094, 215.749, 286.755, 234.866,
234.866, 251.252, 262.176, 267.638, 284.024, 234.866, 232.135, 232.135,
232.135, 240.328, 245.790, 245.790, 253.983, 251.252, 240.328, 237.597,
234.866, 243.059, 264.907, 300.410, 297.679,
251.252, 253.983, 243.059, 213.018, 215.749, 213.018, 243.059, 229.404,
204.825, 207.556, 199.363, 196.632, 199.363, 213.018, 286.755, 226.673,
223.942, 240.328, 226.673, 292.217, 259.445, 229.404, 232.135, 229.404,
234.866, 237.597, 245.790, 248.521, 251.252, 245.790, 240.328, 237.597,
243.059, 245.790, 248.521, 270.369, 292.217,
262.176, 256.714, 248.521, 223.942, 229.404, 234.866, 248.521, 237.597,
204.825, 202.094, 199.363, 199.363, 199.363, 210.287, 275.831, 234.866,
226.673, 243.059, 251.252, 270.369, 267.638, 234.866, 234.866, 237.597,
226.673, 229.404, 243.059, 243.059, 251.252, 251.252, 251.252, 262.176,
262.176, 237.597, 248.521, 245.790, 267.638,
262.176, 264.907, 256.714, 237.597, 240.328, 243.059, 251.252, 245.790,
215.749, 196.632, 199.363, 199.363, 202.094, 210.287, 273.100, 251.252,
229.404, 234.866, 267.638, 273.100, 251.252, 237.597, 240.328, 240.328,
229.404, 226.673, 237.597, 243.059, 248.521, 256.714, 256.714, 262.176,
253.983, 237.597, 237.597, 229.404, 297.679,
253.983, 251.252, 259.445, 251.252, 256.714, 253.983, 251.252, 248.521,
223.942, 196.632, 199.363, 202.094, 202.094, 207.556, 259.445, 243.059,
232.135, 229.404, 278.562, 243.059, 232.135, 240.328, 256.714, 234.866,
223.942, 226.673, 223.942, 248.521, 256.714, 264.907, 245.790, 251.252,
232.135, 245.790, 232.135, 215.749, 341.375,
243.059, 262.176, 256.714, 245.790, 256.714, 259.445, 248.521, 248.521,
232.135, 207.556, 196.632, 199.363, 202.094, 204.825, 240.328, 273.100,
226.673, 229.404, 229.404, 226.673, 232.135, 240.328, 245.790, 232.135,
229.404, 226.673, 229.404, 240.328, 251.252, 253.983, 245.790, 243.059,
229.404, 245.790, 256.714, 215.749, 379.609,
253.983, 264.907, 264.907, 253.983, 251.252, 259.445, 248.521, 245.790,
237.597, 221.211, 202.094, 199.363, 199.363, 202.094, 232.135, 264.907,
259.445, 259.445, 243.059, 234.866, 234.866, 251.252, 237.597, 232.135,
229.404, 245.790, 234.866, 232.135, 248.521, 237.597, 237.597, 234.866,
253.983, 338.644, 382.340, 289.486, 390.533,
275.831, 259.445, 264.907, 264.907, 262.176, 259.445, 229.404, 240.328,
237.597, 223.942, 213.018, 199.363, 199.363, 202.094, 223.942, 251.252,
284.024, 259.445, 240.328, 243.059, 237.597, 243.059, 234.866, 232.135,
234.866, 237.597, 237.597, 221.211, 221.211, 226.673, 248.521, 289.486,
357.761, 417.843, 412.381, 335.913, 363.223,
270.369, 275.831, 278.562, 270.369, 262.176, 245.790, 226.673, 223.942,
232.135, 229.404, 215.749, 207.556, 202.094, 202.094, 213.018, 221.211,
243.059, 262.176, 245.790, 253.983, 240.328, 240.328, 234.866, 240.328,
243.059, 259.445, 243.059, 223.942, 221.211, 232.135, 284.024, 322.258,
385.071, 436.960, 352.299, 374.147, 401.457,
281.293, 284.024, 267.638, 270.369, 245.790, 240.328, 221.211, 207.556,
221.211, 226.673, 215.749, 210.287, 204.825, 204.825, 204.825, 207.556,
218.480, 245.790, 256.714, 237.597, 234.866, 237.597, 251.252, 232.135,
232.135, 232.135, 237.597, 237.597, 243.059, 248.521, 305.872, 314.065,
395.995, 420.574, 395.995, 385.071, 401.457,
289.486, 281.293, 273.100, 270.369, 251.252, 223.942, 213.018, 204.825,
213.018, 221.211, 215.749, 210.287, 210.287, 213.018, 213.018, 207.556,
210.287, 221.211, 243.059, 237.597, 229.404, 229.404, 245.790, 234.866,
232.135, 229.404, 218.480, 232.135, 264.907, 284.024, 324.989, 324.989,
406.919, 401.457, 393.264, 398.726, 415.112,
292.217, 286.755, 281.293, 273.100, 253.983, 226.673, 213.018, 202.094,
202.094, 215.749, 213.018, 210.287, 207.556, 213.018, 218.480, 215.749,
207.556, 213.018, 226.673, 229.404, 221.211, 221.211, 229.404, 226.673,
223.942, 213.018, 213.018, 232.135, 234.866, 264.907, 286.755, 311.334,
395.995, 398.726, 404.188, 401.457, 409.650,
292.217, 286.755, 281.293, 264.907, 251.252, 229.404, 196.632, 196.632,
204.825, 210.287, 207.556, 204.825, 207.556, 215.749, 218.480, 218.480,
210.287, 207.556, 223.942, 221.211, 218.480, 221.211, 218.480, 218.480,
218.480, 210.287, 202.094, 202.094, 199.363, 210.287, 248.521, 300.410,
360.492, 385.071, 415.112, 415.112, 395.995,
292.217, 286.755, 281.293, 262.176, 251.252, 234.866, 199.363, 193.901,
199.363, 204.825, 204.825, 207.556, 207.556, 213.018, 218.480, 218.480,
218.480, 267.638, 218.480, 218.480, 223.942, 223.942, 218.480, 213.018,
207.556, 199.363, 193.901, 196.632, 193.901, 202.094, 218.480, 294.948,
324.989, 371.416, 431.498, 387.802, 374.147,
292.217, 284.024, 275.831, 264.907, 232.135, 237.597, 204.825, 191.170,
191.170, 202.094, 202.094, 204.825, 210.287, 213.018, 218.480, 223.942,
300.410, 319.527, 300.410, 213.018, 221.211, 226.673, 221.211, 213.018,
207.556, 199.363, 193.901, 188.439, 185.708, 193.901, 202.094, 259.445,
327.720, 376.878, 404.188, 390.533, 379.609
};

const size_t array_float1_size = sizeof(array_float1);

const float array_float2[YSIZE * XSIZE] =
{
210.287, 199.363, 207.556, 218.480, 215.749, 204.825, 223.942, 177.515,
169.322, 174.784, 161.129, 161.129, 166.591, 196.632, 191.170, 182.977,
177.515, 191.170, 193.901, 191.170, 185.708, 180.246, 177.515, 182.977,
180.246, 180.246, 180.246, 180.246, 180.246, 180.246, 180.246, 180.246,
180.246, 177.515, 172.053, 172.053, 169.322,
204.825, 193.901, 193.901, 215.749, 221.211, 204.825, 221.211, 199.363,
161.129, 177.515, 163.860, 163.860, 174.784, 199.363, 199.363, 185.708,
180.246, 191.170, 196.632, 193.901, 185.708, 180.246, 180.246, 182.977,
180.246, 180.246, 180.246, 182.977, 182.977, 182.977, 180.246, 182.977,
180.246, 174.784, 172.053, 172.053, 172.053,
207.556, 193.901, 180.246, 199.363, 221.211, 213.018, 218.480, 215.749,
161.129, 180.246, 163.860, 161.129, 169.322, 202.094, 202.094, 193.901,
182.977, 191.170, 199.363, 193.901, 185.708, 180.246, 177.515, 177.515,
180.246, 180.246, 182.977, 182.977, 182.977, 182.977, 182.977, 182.977,
180.246, 174.784, 174.784, 174.784, 174.784,
207.556, 196.632, 174.784, 185.708, 215.749, 221.211, 218.480, 218.480,
161.129, 185.708, 163.860, 161.129, 163.860, 204.825, 204.825, 199.363,
182.977, 185.708, 199.363, 196.632, 185.708, 180.246, 177.515, 172.053,
182.977, 182.977, 182.977, 182.977, 185.708, 182.977, 182.977, 180.246,
177.515, 174.784, 177.515, 177.515, 177.515,
215.749, 196.632, 172.053, 180.246, 199.363, 218.480, 226.673, 223.942,
163.860, 177.515, 166.591, 166.591, 163.860, 180.246, 204.825, 204.825,
177.515, 191.170, 199.363, 196.632, 185.708, 180.246, 177.515, 174.784,
185.708, 182.977, 185.708, 185.708, 185.708, 182.977, 182.977, 180.246,
177.515, 177.515, 177.515, 180.246, 177.515,
221.211, 199.363, 169.322, 177.515, 196.632, 202.094, 223.942, 232.135,
180.246, 161.129, 169.322, 163.860, 163.860, 172.053, 204.825, 207.556,
185.708, 188.439, 196.632, 196.632, 185.708, 180.246, 180.246, 177.515,
182.977, 185.708, 185.708, 185.708, 185.708, 185.708, 180.246, 180.246,
174.784, 180.246, 177.515, 180.246, 180.246,
229.404, 202.094, 174.784, 174.784, 191.170, 193.901, 213.018, 229.404,
191.170, 158.398, 163.860, 161.129, 161.129, 172.053, 204.825, 218.480,
199.363, 182.977, 196.632, 196.632, 185.708, 180.246, 180.246, 177.515,
180.246, 185.708, 185.708, 185.708, 185.708, 185.708, 180.246, 177.515,
177.515, 177.515, 180.246, 182.977, 185.708,
237.597, 221.211, 180.246, 172.053, 177.515, 185.708, 207.556, 207.556,
204.825, 161.129, 158.398, 161.129, 161.129, 163.860, 193.901, 251.252,
177.515, 174.784, 202.094, 196.632, 188.439, 182.977, 177.515, 177.515,
177.515, 185.708, 188.439, 185.708, 188.439, 182.977, 177.515, 177.515,
177.515, 177.515, 182.977, 185.708, 188.439,
234.866, 234.866, 199.363, 174.784, 169.322, 182.977, 204.825, 207.556,
191.170, 166.591, 158.398, 158.398, 161.129, 163.860, 221.211, 185.708,
161.129, 172.053, 202.094, 245.790, 270.369, 182.977, 177.515, 177.515,
174.784, 182.977, 185.708, 185.708, 185.708, 182.977, 177.515, 177.515,
180.246, 177.515, 180.246, 185.708, 185.708,
232.135, 232.135, 218.480, 180.246, 182.977, 182.977, 221.211, 202.094,
169.322, 172.053, 161.129, 158.398, 158.398, 163.860, 253.983, 166.591,
161.129, 161.129, 185.708, 314.065, 207.556, 182.977, 180.246, 174.784,
174.784, 180.246, 185.708, 185.708, 185.708, 180.246, 177.515, 177.515,
180.246, 177.515, 174.784, 177.515, 188.439,
245.790, 237.597, 226.673, 193.901, 202.094, 210.287, 226.673, 215.749,
172.053, 163.860, 161.129, 161.129, 158.398, 158.398, 245.790, 166.591,
161.129, 161.129, 182.977, 218.480, 193.901, 185.708, 180.246, 174.784,
172.053, 172.053, 185.708, 185.708, 185.708, 180.246, 177.515, 180.246,
182.977, 177.515, 174.784, 169.322, 237.597,
248.521, 251.252, 234.866, 207.556, 213.018, 221.211, 232.135, 223.942,
182.977, 161.129, 161.129, 161.129, 161.129, 163.860, 240.328, 196.632,
161.129, 163.860, 202.094, 218.480, 191.170, 182.977, 180.246, 174.784,
169.322, 163.860, 177.515, 185.708, 182.977, 180.246, 177.515, 182.977,
180.246, 174.784, 169.322, 161.129, 303.141,
229.404, 229.404, 237.597, 232.135, 237.597, 232.135, 229.404, 229.404,
196.632, 161.129, 161.129, 161.129, 161.129, 161.129, 199.363, 193.901,
169.322, 161.129, 273.100, 191.170, 191.170, 182.977, 180.246, 174.784,
163.860, 158.398, 158.398, 182.977, 185.708, 180.246, 177.515, 180.246,
174.784, 172.053, 161.129, 152.936, 357.761,
218.480, 245.790, 237.597, 226.673, 240.328, 243.059, 229.404, 226.673,
207.556, 174.784, 161.129, 161.129, 161.129, 158.398, 161.129, 264.907,
174.784, 169.322, 193.901, 185.708, 191.170, 199.363, 180.246, 172.053,
166.591, 158.398, 158.398, 169.322, 182.977, 180.246, 174.784, 177.515,
172.053, 172.053, 166.591, 155.667, 406.919,
234.866, 248.521, 251.252, 237.597, 232.135, 240.328, 226.673, 221.211,
213.018, 188.439, 166.591, 161.129, 161.129, 161.129, 161.129, 166.591,
226.673, 196.632, 182.977, 182.977, 188.439, 188.439, 180.246, 174.784,
166.591, 196.632, 152.936, 155.667, 174.784, 174.784, 174.784, 174.784,
177.515, 314.065, 409.650, 253.983, 412.381,
264.907, 243.059, 248.521, 251.252, 243.059, 240.328, 202.094, 218.480,
213.018, 193.901, 177.515, 161.129, 158.398, 161.129, 158.398, 161.129,
193.901, 196.632, 182.977, 191.170, 191.170, 188.439, 182.977, 174.784,
172.053, 180.246, 152.936, 147.474, 155.667, 161.129, 174.784, 237.597,
379.609, 442.422, 436.960, 349.568, 385.071,
256.714, 262.176, 264.907, 256.714, 243.059, 223.942, 193.901, 191.170,
207.556, 199.363, 182.977, 166.591, 161.129, 161.129, 158.398, 161.129,
166.591, 193.901, 182.977, 204.825, 191.170, 185.708, 191.170, 177.515,
172.053, 172.053, 161.129, 152.936, 147.474, 150.205, 245.790, 330.451,
406.919, 458.808, 376.878, 393.264, 428.767,
270.369, 273.100, 253.983, 253.983, 223.942, 218.480, 191.170, 169.322,
191.170, 196.632, 182.977, 172.053, 163.860, 163.860, 158.398, 158.398,
163.860, 185.708, 191.170, 191.170, 188.439, 185.708, 215.749, 185.708,
174.784, 169.322, 163.860, 161.129, 155.667, 155.667, 240.328, 327.720,
412.381, 442.422, 420.574, 406.919, 423.305,
281.293, 270.369, 259.445, 256.714, 229.404, 199.363, 182.977, 169.322,
177.515, 188.439, 182.977, 174.784, 172.053, 174.784, 172.053, 161.129,
163.860, 177.515, 193.901, 188.439, 188.439, 182.977, 213.018, 177.515,
172.053, 166.591, 161.129, 166.591, 163.860, 185.708, 273.100, 349.568,
423.305, 423.305, 415.112, 423.305, 447.884,
284.024, 278.562, 270.369, 259.445, 234.866, 202.094, 182.977, 166.591,
166.591, 180.246, 177.515, 172.053, 172.053, 174.784, 177.515, 172.053,
163.860, 172.053, 191.170, 188.439, 182.977, 182.977, 182.977, 177.515,
169.322, 163.860, 158.398, 155.667, 169.322, 158.398, 193.901, 319.527,
409.650, 420.574, 428.767, 423.305, 445.153,
284.024, 275.831, 270.369, 248.521, 229.404, 202.094, 161.129, 161.129,
169.322, 174.784, 172.053, 166.591, 169.322, 174.784, 174.784, 174.784,
166.591, 163.860, 188.439, 185.708, 182.977, 188.439, 182.977, 177.515,
169.322, 161.129, 158.398, 155.667, 155.667, 152.936, 161.129, 284.024,
374.147, 401.457, 434.229, 439.691, 431.498,
284.024, 275.831, 270.369, 245.790, 232.135, 210.287, 163.860, 155.667,
163.860, 169.322, 169.322, 169.322, 172.053, 174.784, 177.515, 177.515,
180.246, 273.100, 182.977, 182.977, 188.439, 188.439, 182.977, 177.515,
172.053, 163.860, 158.398, 152.936, 147.474, 150.205, 152.936, 210.287,
333.182, 387.802, 453.346, 428.767, 409.650,
284.024, 275.831, 264.907, 251.252, 210.287, 215.749, 174.784, 155.667,
155.667, 169.322, 169.322, 169.322, 174.784, 177.515, 180.246, 177.515,
314.065, 376.878, 352.299, 174.784, 185.708, 191.170, 185.708, 180.246,
174.784, 163.860, 158.398, 152.936, 144.743, 144.743, 152.936, 169.322,
314.065, 390.533, 428.767, 426.036, 434.229
};

const size_t array_float2_size = sizeof(array_float2);

const double array_double1[YSIZE * XSIZE] =
{
148.914762, 145.451628, 148.914762, 155.841030, 154.109463, 147.183195,
155.841030, 135.062226, 133.330659, 136.793793, 129.867525, 133.330659,
136.793793, 148.914762, 150.646329, 143.720061, 141.988494, 150.646329,
154.109463, 152.377896, 148.914762, 150.646329, 152.377896, 150.646329,
140.256927, 145.451628, 147.183195, 147.183195, 145.451628, 148.914762,
152.377896, 157.572597, 166.230432, 164.498865, 167.961999, 164.498865,
154.109463,
147.183195, 141.988494, 140.256927, 152.377896, 154.109463, 147.183195,
154.109463, 143.720061, 128.135958, 136.793793, 131.599092, 133.330659,
138.525360, 150.646329, 150.646329, 145.451628, 145.451628, 152.377896,
155.841030, 154.109463, 150.646329, 147.183195, 150.646329, 152.377896,
143.720061, 138.525360, 141.988494, 145.451628, 147.183195, 150.646329,
155.841030, 164.498865, 166.230432, 164.498865, 164.498865, 159.304164,
155.841030,
147.183195, 140.256927, 136.793793, 145.451628, 155.841030, 150.646329,
152.377896, 152.377896, 126.404391, 136.793793, 129.867525, 131.599092,
136.793793, 152.377896, 152.377896, 150.646329, 147.183195, 155.841030,
159.304164, 154.109463, 152.377896, 152.377896, 150.646329, 148.914762,
145.451628, 141.988494, 141.988494, 143.720061, 150.646329, 154.109463,
161.035731, 162.767298, 161.035731, 161.035731, 159.304164, 159.304164,
166.230432,
147.183195, 141.988494, 131.599092, 138.525360, 152.377896, 154.109463,
152.377896, 154.109463, 126.404391, 138.525360, 129.867525, 129.867525,
133.330659, 154.109463, 159.304164, 161.035731, 157.572597, 154.109463,
162.767298, 159.304164, 155.841030, 154.109463, 152.377896, 145.451628,
145.451628, 141.988494, 141.988494, 147.183195, 152.377896, 157.572597,
162.767298, 161.035731, 161.035731, 154.109463, 155.841030, 166.230432,
166.230432,
150.646329, 143.720061, 129.867525, 133.330659, 143.720061, 154.109463,
155.841030, 155.841030, 128.135958, 135.062226, 131.599092, 131.599092,
131.599092, 145.451628, 162.767298, 173.156700, 154.109463, 159.304164,
162.767298, 159.304164, 155.841030, 154.109463, 155.841030, 147.183195,
145.451628, 143.720061, 143.720061, 150.646329, 157.572597, 159.304164,
152.377896, 159.304164, 157.572597, 152.377896, 155.841030, 167.961999,
164.498865,
154.109463, 143.720061, 128.135958, 133.330659, 141.988494, 145.451628,
155.841030, 159.304164, 135.062226, 124.672824, 131.599092, 129.867525,
129.867525, 140.256927, 164.498865, 174.888267, 164.498865, 159.304164,
164.498865, 161.035731, 155.841030, 154.109463, 155.841030, 150.646329,
148.914762, 145.451628, 148.914762, 152.377896, 155.841030, 155.841030,
150.646329, 155.841030, 154.109463, 155.841030, 154.109463, 169.693566,
169.693566,
159.304164, 145.451628, 129.867525, 131.599092, 140.256927, 140.256927,
148.914762, 157.572597, 140.256927, 124.672824, 128.135958, 128.135958,
129.867525, 140.256927, 180.082968, 187.009236, 161.035731, 159.304164,
164.498865, 162.767298, 152.377896, 150.646329, 154.109463, 150.646329,
147.183195, 147.183195, 152.377896, 154.109463, 161.035731, 157.572597,
152.377896, 152.377896, 157.572597, 152.377896, 157.572597, 183.546102,
187.009236,
161.035731, 154.109463, 135.062226, 129.867525, 133.330659, 138.525360,
147.183195, 148.914762, 147.183195, 126.404391, 124.672824, 126.404391,
128.135958, 136.793793, 176.619834, 174.888267, 152.377896, 159.304164,
161.035731, 157.572597, 150.646329, 150.646329, 148.914762, 150.646329,
147.183195, 148.914762, 152.377896, 154.109463, 162.767298, 162.767298,
155.841030, 152.377896, 147.183195, 148.914762, 169.693566, 188.740803,
195.667071,
159.304164, 161.035731, 143.720061, 131.599092, 128.135958, 136.793793,
145.451628, 147.183195, 140.256927, 129.867525, 124.672824, 126.404391,
128.135958, 136.793793, 181.814535, 148.914762, 148.914762, 159.304164,
166.230432, 169.693566, 180.082968, 148.914762, 147.183195, 147.183195,
147.183195, 152.377896, 155.841030, 155.841030, 161.035731, 159.304164,
152.377896, 150.646329, 148.914762, 154.109463, 167.961999, 190.472370,
188.740803,
159.304164, 161.035731, 154.109463, 135.062226, 136.793793, 135.062226,
154.109463, 145.451628, 129.867525, 131.599092, 126.404391, 124.672824,
126.404391, 135.062226, 181.814535, 143.720061, 141.988494, 152.377896,
143.720061, 185.277669, 164.498865, 145.451628, 147.183195, 145.451628,
148.914762, 150.646329, 155.841030, 157.572597, 159.304164, 155.841030,
152.377896, 150.646329, 154.109463, 155.841030, 157.572597, 171.425133,
185.277669,
166.230432, 162.767298, 157.572597, 141.988494, 145.451628, 148.914762,
157.572597, 150.646329, 129.867525, 128.135958, 126.404391, 126.404391,
126.404391, 133.330659, 174.888267, 148.914762, 143.720061, 154.109463,
159.304164, 171.425133, 169.693566, 148.914762, 148.914762, 150.646329,
143.720061, 145.451628, 154.109463, 154.109463, 159.304164, 159.304164,
159.304164, 166.230432, 166.230432, 150.646329, 157.572597, 155.841030,
169.693566,
166.230432, 167.961999, 162.767298, 150.646329, 152.377896, 154.109463,
159.304164, 155.841030, 136.793793, 124.672824, 126.404391, 126.404391,
128.135958, 133.330659, 173.156700, 159.304164, 145.451628, 148.914762,
169.693566, 173.156700, 159.304164, 150.646329, 152.377896, 152.377896,
145.451628, 143.720061, 150.646329, 154.109463, 157.572597, 162.767298,
162.767298, 166.230432, 161.035731, 150.646329, 150.646329, 145.451628,
188.740803,
161.035731, 159.304164, 164.498865, 159.304164, 162.767298, 161.035731,
159.304164, 157.572597, 141.988494, 124.672824, 126.404391, 128.135958,
128.135958, 131.599092, 164.498865, 154.109463, 147.183195, 145.451628,
176.619834, 154.109463, 147.183195, 152.377896, 162.767298, 148.914762,
141.988494, 143.720061, 141.988494, 157.572597, 162.767298, 167.961999,
155.841030, 159.304164, 147.183195, 155.841030, 147.183195, 136.793793,
216.445875,
154.109463, 166.230432, 162.767298, 155.841030, 162.767298, 164.498865,
157.572597, 157.572597, 147.183195, 131.599092, 124.672824, 126.404391,
128.135958, 129.867525, 152.377896, 173.156700, 143.720061, 145.451628,
145.451628, 143.720061, 147.183195, 152.377896, 155.841030, 147.183195,
145.451628, 143.720061, 145.451628, 152.377896, 159.304164, 161.035731,
155.841030, 154.109463, 145.451628, 155.841030, 162.767298, 136.793793,
240.687813,
161.035731, 167.961999, 167.961999, 161.035731, 159.304164, 164.498865,
157.572597, 155.841030, 150.646329, 140.256927, 128.135958, 126.404391,
126.404391, 128.135958, 147.183195, 167.961999, 164.498865, 164.498865,
154.109463, 148.914762, 148.914762, 159.304164, 150.646329, 147.183195,
145.451628, 155.841030, 148.914762, 147.183195, 157.572597, 150.646329,
150.646329, 148.914762, 161.035731, 214.714308, 242.419380, 183.546102,
247.614081,
174.888267, 164.498865, 167.961999, 167.961999, 166.230432, 164.498865,
145.451628, 152.377896, 150.646329, 141.988494, 135.062226, 126.404391,
126.404391, 128.135958, 141.988494, 159.304164, 180.082968, 164.498865,
152.377896, 154.109463, 150.646329, 154.109463, 148.914762, 147.183195,
148.914762, 150.646329, 150.646329, 140.256927, 140.256927, 143.720061,
157.572597, 183.546102, 226.835277, 264.929751, 261.466617, 212.982741,
230.298411,
171.425133, 174.888267, 176.619834, 171.425133, 166.230432, 155.841030,
143.720061, 141.988494, 147.183195, 145.451628, 136.793793, 131.599092,
128.135958, 128.135958, 135.062226, 140.256927, 154.109463, 166.230432,
155.841030, 161.035731, 152.377896, 152.377896, 148.914762, 152.377896,
154.109463, 164.498865, 154.109463, 141.988494, 140.256927, 147.183195,
180.082968, 204.324906, 244.150947, 277.050720, 223.372143, 237.224679,
254.540349,
178.351401, 180.082968, 169.693566, 171.425133, 155.841030, 152.377896,
140.256927, 131.599092, 140.256927, 143.720061, 136.793793, 133.330659,
129.867525, 129.867525, 129.867525, 131.599092, 138.525360, 155.841030,
162.767298, 150.646329, 148.914762, 150.646329, 159.304164, 147.183195,
147.183195, 147.183195, 150.646329, 150.646329, 154.109463, 157.572597,
193.935504, 199.130205, 251.077215, 266.661318, 251.077215, 244.150947,
254.540349,
183.546102, 178.351401, 173.156700, 171.425133, 159.304164, 141.988494,
135.062226, 129.867525, 135.062226, 140.256927, 136.793793, 133.330659,
133.330659, 135.062226, 135.062226, 131.599092, 133.330659, 140.256927,
154.109463, 150.646329, 145.451628, 145.451628, 155.841030, 148.914762,
147.183195, 145.451628, 138.525360, 147.183195, 167.961999, 180.082968,
206.056473, 206.056473, 258.003483, 254.540349, 249.345648, 252.808782,
263.198184,
185.277669, 181.814535, 178.351401, 173.156700, 161.035731, 143.720061,
135.062226, 128.135958, 128.135958, 136.793793, 135.062226, 133.330659,
131.599092, 135.062226, 138.525360, 136.793793, 131.599092, 135.062226,
143.720061, 145.451628, 140.256927, 140.256927, 145.451628, 143.720061,
141.988494, 135.062226, 135.062226, 147.183195, 148.914762, 167.961999,
181.814535, 197.398638, 251.077215, 252.808782, 256.271916, 254.540349,
259.735050,
185.277669, 181.814535, 178.351401, 167.961999, 159.304164, 145.451628,
124.672824, 124.672824, 129.867525, 133.330659, 131.599092, 129.867525,
131.599092, 136.793793, 138.525360, 138.525360, 133.330659, 131.599092,
141.988494, 140.256927, 138.525360, 140.256927, 138.525360, 138.525360,
138.525360, 133.330659, 128.135958, 128.135958, 126.404391, 133.330659,
157.572597, 190.472370, 228.566844, 244.150947, 263.198184, 263.198184,
251.077215,
185.277669, 181.814535, 178.351401, 166.230432, 159.304164, 148.914762,
126.404391, 122.941257, 126.404391, 129.867525, 129.867525, 131.599092,
131.599092, 135.062226, 138.525360, 138.525360, 138.525360, 169.693566,
138.525360, 138.525360, 141.988494, 141.988494, 138.525360, 135.062226,
131.599092, 126.404391, 122.941257, 124.672824, 122.941257, 128.135958,
138.525360, 187.009236, 206.056473, 235.493112, 273.587586, 245.882514,
237.224679,
185.277669, 180.082968, 174.888267, 167.961999, 147.183195, 150.646329,
129.867525, 121.209690, 121.209690, 128.135958, 128.135958, 129.867525,
133.330659, 135.062226, 138.525360, 141.988494, 190.472370, 202.593339,
190.472370, 135.062226, 140.256927, 143.720061, 140.256927, 135.062226,
131.599092, 126.404391, 122.941257, 119.478123, 117.746556, 122.941257,
128.135958, 164.498865, 207.788040, 238.956246, 256.271916, 247.614081,
240.687813
};

const size_t array_double1_size = sizeof(array_double1);

const double array_double2[YSIZE * XSIZE] =
{
133.330659, 126.404391, 131.599092, 138.525360, 136.793793, 129.867525,
141.988494, 112.551855, 107.357154, 110.820288, 102.162453, 102.162453,
105.625587, 124.672824, 121.209690, 116.014989, 112.551855, 121.209690,
122.941257, 121.209690, 117.746556, 114.283422, 112.551855, 116.014989,
114.283422, 114.283422, 114.283422, 114.283422, 114.283422, 114.283422,
114.283422, 114.283422, 114.283422, 112.551855, 109.088721, 109.088721,
107.357154,
129.867525, 122.941257, 122.941257, 136.793793, 140.256927, 129.867525,
140.256927, 126.404391, 102.162453, 112.551855, 103.894020, 103.894020,
110.820288, 126.404391, 126.404391, 117.746556, 114.283422, 121.209690,
124.672824, 122.941257, 117.746556, 114.283422, 114.283422, 116.014989,
114.283422, 114.283422, 114.283422, 116.014989, 116.014989, 116.014989,
114.283422, 116.014989, 114.283422, 110.820288, 109.088721, 109.088721,
109.088721,
131.599092, 122.941257, 114.283422, 126.404391, 140.256927, 135.062226,
138.525360, 136.793793, 102.162453, 114.283422, 103.894020, 102.162453,
107.357154, 128.135958, 128.135958, 122.941257, 116.014989, 121.209690,
126.404391, 122.941257, 117.746556, 114.283422, 112.551855, 112.551855,
114.283422, 114.283422, 116.014989, 116.014989, 116.014989, 116.014989,
116.014989, 116.014989, 114.283422, 110.820288, 110.820288, 110.820288,
110.820288,
131.599092, 124.672824, 110.820288, 117.746556, 136.793793, 140.256927,
138.525360, 138.525360, 102.162453, 117.746556, 103.894020, 102.162453,
103.894020, 129.867525, 129.867525, 126.404391, 116.014989, 117.746556,
126.404391, 124.672824, 117.746556, 114.283422, 112.551855, 109.088721,
116.014989, 116.014989, 116.014989, 116.014989, 117.746556, 116.014989,
116.014989, 114.283422, 112.551855, 110.820288, 112.551855, 112.551855,
112.551855,
136.793793, 124.672824, 109.088721, 114.283422, 126.404391, 138.525360,
143.720061, 141.988494, 103.894020, 112.551855, 105.625587, 105.625587,
103.894020, 114.283422, 129.867525, 129.867525, 112.551855, 121.209690,
126.404391, 124.672824, 117.746556, 114.283422, 112.551855, 110.820288,
117.746556, 116.014989, 117.746556, 117.746556, 117.746556, 116.014989,
116.014989, 114.283422, 112.551855, 112.551855, 112.551855, 114.283422,
112.551855,
140.256927, 126.404391, 107.357154, 112.551855, 124.672824, 128.135958,
141.988494, 147.183195, 114.283422, 102.162453, 107.357154, 103.894020,
103.894020, 109.088721, 129.867525, 131.599092, 117.746556, 119.478123,
124.672824, 124.672824, 117.746556, 114.283422, 114.283422, 112.551855,
116.014989, 117.746556, 117.746556, 117.746556, 117.746556, 117.746556,
114.283422, 114.283422, 110.820288, 114.283422, 112.551855, 114.283422,
114.283422,
145.451628, 128.135958, 110.820288, 110.820288, 121.209690, 122.941257,
135.062226, 145.451628, 121.209690, 100.430886, 103.894020, 102.162453,
102.162453, 109.088721, 129.867525, 138.525360, 126.404391, 116.014989,
124.672824, 124.672824, 117.746556, 114.283422, 114.283422, 112.551855,
114.283422, 117.746556, 117.746556, 117.746556, 117.746556, 117.746556,
114.283422, 112.551855, 112.551855, 112.551855, 114.283422, 116.014989,
117.746556,
150.646329, 140.256927, 114.283422, 109.088721, 112.551855, 117.746556,
131.599092, 131.599092, 129.867525, 102.162453, 100.430886, 102.162453,
102.162453, 103.894020, 122.941257, 159.304164, 112.551855, 110.820288,
128.135958, 124.672824, 119.478123, 116.014989, 112.551855, 112.551855,
112.551855, 117.746556, 119.478123, 117.746556, 119.478123, 116.014989,
112.551855, 112.551855, 112.551855, 112.551855, 116.014989, 117.746556,
119.478123,
148.914762, 148.914762, 126.404391, 110.820288, 107.357154, 116.014989,
129.867525, 131.599092, 121.209690, 105.625587, 100.430886, 100.430886,
102.162453, 103.894020, 140.256927, 117.746556, 102.162453, 109.088721,
128.135958, 155.841030, 171.425133, 116.014989, 112.551855, 112.551855,
110.820288, 116.014989, 117.746556, 117.746556, 117.746556, 116.014989,
112.551855, 112.551855, 114.283422, 112.551855, 114.283422, 117.746556,
117.746556,
147.183195, 147.183195, 138.525360, 114.283422, 116.014989, 116.014989,
140.256927, 128.135958, 107.357154, 109.088721, 102.162453, 100.430886,
100.430886, 103.894020, 161.035731, 105.625587, 102.162453, 102.162453,
117.746556, 199.130205, 131.599092, 116.014989, 114.283422, 110.820288,
110.820288, 114.283422, 117.746556, 117.746556, 117.746556, 114.283422,
112.551855, 112.551855, 114.283422, 112.551855, 110.820288, 112.551855,
119.478123,
155.841030, 150.646329, 143.720061, 122.941257, 128.135958, 133.330659,
143.720061, 136.793793, 109.088721, 103.894020, 102.162453, 102.162453,
100.430886, 100.430886, 155.841030, 105.625587, 102.162453, 102.162453,
116.014989, 138.525360, 122.941257, 117.746556, 114.283422, 110.820288,
109.088721, 109.088721, 117.746556, 117.746556, 117.746556, 114.283422,
112.551855, 114.283422, 116.014989, 112.551855, 110.820288, 107.357154,
150.646329,
157.572597, 159.304164, 148.914762, 131.599092, 135.062226, 140.256927,
147.183195, 141.988494, 116.014989, 102.162453, 102.162453, 102.162453,
102.162453, 103.894020, 152.377896, 124.672824, 102.162453, 103.894020,
128.135958, 138.525360, 121.209690, 116.014989, 114.283422, 110.820288,
107.357154, 103.894020, 112.551855, 117.746556, 116.014989, 114.283422,
112.551855, 116.014989, 114.283422, 110.820288, 107.357154, 102.162453,
192.203937,
145.451628, 145.451628, 150.646329, 147.183195, 150.646329, 147.183195,
145.451628, 145.451628, 124.672824, 102.162453, 102.162453, 102.162453,
102.162453, 102.162453, 126.404391, 122.941257, 107.357154, 102.162453,
173.156700, 121.209690, 121.209690, 116.014989, 114.283422, 110.820288,
103.894020, 100.430886, 100.430886, 116.014989, 117.746556, 114.283422,
112.551855, 114.283422, 110.820288, 109.088721, 102.162453, 96.967752,
226.835277,
138.525360, 155.841030, 150.646329, 143.720061, 152.377896, 154.109463,
145.451628, 143.720061, 131.599092, 110.820288, 102.162453, 102.162453,
102.162453, 100.430886, 102.162453, 167.961999, 110.820288, 107.357154,
122.941257, 117.746556, 121.209690, 126.404391, 114.283422, 109.088721,
105.625587, 100.430886, 100.430886, 107.357154, 116.014989, 114.283422,
110.820288, 112.551855, 109.088721, 109.088721, 105.625587, 98.699319,
258.003483,
148.914762, 157.572597, 159.304164, 150.646329, 147.183195, 152.377896,
143.720061, 140.256927, 135.062226, 119.478123, 105.625587, 102.162453,
102.162453, 102.162453, 102.162453, 105.625587, 143.720061, 124.672824,
116.014989, 116.014989, 119.478123, 119.478123, 114.283422, 110.820288,
105.625587, 124.672824, 96.967752, 98.699319, 110.820288, 110.820288,
110.820288, 110.820288, 112.551855, 199.130205, 259.735050, 161.035731,
261.466617,
167.961999, 154.109463, 157.572597, 159.304164, 154.109463, 152.377896,
128.135958, 138.525360, 135.062226, 122.941257, 112.551855, 102.162453,
100.430886, 102.162453, 100.430886, 102.162453, 122.941257, 124.672824,
116.014989, 121.209690, 121.209690, 119.478123, 116.014989, 110.820288,
109.088721, 114.283422, 96.967752, 93.504618, 98.699319, 102.162453,
110.820288, 150.646329, 240.687813, 280.513854, 277.050720, 221.640576,
244.150947,
162.767298, 166.230432, 167.961999, 162.767298, 154.109463, 141.988494,
122.941257, 121.209690, 131.599092, 126.404391, 116.014989, 105.625587,
102.162453, 102.162453, 100.430886, 102.162453, 105.625587, 122.941257,
116.014989, 129.867525, 121.209690, 117.746556, 121.209690, 112.551855,
109.088721, 109.088721, 102.162453, 96.967752, 93.504618, 95.236185,
155.841030, 209.519607, 258.003483, 290.903256, 238.956246, 249.345648,
271.856019,
171.425133, 173.156700, 161.035731, 161.035731, 141.988494, 138.525360,
121.209690, 107.357154, 121.209690, 124.672824, 116.014989, 109.088721,
103.894020, 103.894020, 100.430886, 100.430886, 103.894020, 117.746556,
121.209690, 121.209690, 119.478123, 117.746556, 136.793793, 117.746556,
110.820288, 107.357154, 103.894020, 102.162453, 98.699319, 98.699319,
152.377896, 207.788040, 261.466617, 280.513854, 266.661318, 258.003483,
268.392885,
178.351401, 171.425133, 164.498865, 162.767298, 145.451628, 126.404391,
116.014989, 107.357154, 112.551855, 119.478123, 116.014989, 110.820288,
109.088721, 110.820288, 109.088721, 102.162453, 103.894020, 112.551855,
122.941257, 119.478123, 119.478123, 116.014989, 135.062226, 112.551855,
109.088721, 105.625587, 102.162453, 105.625587, 103.894020, 117.746556,
173.156700, 221.640576, 268.392885, 268.392885, 263.198184, 268.392885,
283.976988,
180.082968, 176.619834, 171.425133, 164.498865, 148.914762, 128.135958,
116.014989, 105.625587, 105.625587, 114.283422, 112.551855, 109.088721,
109.088721, 110.820288, 112.551855, 109.088721, 103.894020, 109.088721,
121.209690, 119.478123, 116.014989, 116.014989, 116.014989, 112.551855,
107.357154, 103.894020, 100.430886, 98.699319, 107.357154, 100.430886,
122.941257, 202.593339, 259.735050, 266.661318, 271.856019, 268.392885,
282.245421,
180.082968, 174.888267, 171.425133, 157.572597, 145.451628, 128.135958,
102.162453, 102.162453, 107.357154, 110.820288, 109.088721, 105.625587,
107.357154, 110.820288, 110.820288, 110.820288, 105.625587, 103.894020,
119.478123, 117.746556, 116.014989, 119.478123, 116.014989, 112.551855,
107.357154, 102.162453, 100.430886, 98.699319, 98.699319, 96.967752,
102.162453, 180.082968, 237.224679, 254.540349, 275.319153, 278.782287,
273.587586,
180.082968, 174.888267, 171.425133, 155.841030, 147.183195, 133.330659,
103.894020, 98.699319, 103.894020, 107.357154, 107.357154, 107.357154,
109.088721, 110.820288, 112.551855, 112.551855, 114.283422, 173.156700,
116.014989, 116.014989, 119.478123, 119.478123, 116.014989, 112.551855,
109.088721, 103.894020, 100.430886, 96.967752, 93.504618, 95.236185,
96.967752, 133.330659, 211.251174, 245.882514, 287.440122, 271.856019,
259.735050,
180.082968, 174.888267, 167.961999, 159.304164, 133.330659, 136.793793,
110.820288, 98.699319, 98.699319, 107.357154, 107.357154, 107.357154,
110.820288, 112.551855, 114.283422, 112.551855, 199.130205, 238.956246,
223.372143, 110.820288, 117.746556, 121.209690, 117.746556, 114.283422,
110.820288, 103.894020, 100.430886, 96.967752, 91.773051, 91.773051,
96.967752, 107.357154, 199.130205, 247.614081, 271.856019, 270.124452,
275.319153
};

const size_t array_double2_size = sizeof(array_double2);

/* vim: set ts=8 sts=8 sw=8 noet: */
