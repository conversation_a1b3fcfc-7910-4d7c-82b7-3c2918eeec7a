.\"
.\" Copyright (c) 1991-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFPrintDirectory 3TIFF "December 12, 1991" "libtiff"
.SH NAME
TIFFPrintDirectory \- print a description of a
.SM TIFF
directory
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.BI "void TIFFPrintDirectory(TIFF *" tif ", FILE *" fd ", long " flags ")"
.SH DESCRIPTION
.I TIFFPrintDirectory
prints a description of the current directory in the specified
.SM TIFF
file to the standard I/O output stream
.IR fd .
The
.I flags
parameter is used to control the
.I "level of detail"
of the printed information; it is a bit-or of the flags defined in
.BR tiffio.h :
.sp .5
.nf
.ta \w'#define 'u +\w'TIFFPRINT_JPEGDCTABLES  'u +\w'0x200   'u
#define	TIFFPRINT_NONE	0x0	/* no extra info */
#define	TIFFPRINT_STRIPS	0x1	/* strips/tiles info */
#define	TIFFPRINT_CURVES	0x2	/* color/gray response curves */
#define	TIFFPRINT_COLORMAP	0x4	/* colormap */
#define	TIFFPRINT_JPEGQTABLES	0x100	/* JPEG Q matrices */
#define	TIFFPRINT_JPEGACTABLES	0x200	/* JPEG AC tables */
#define	TIFFPRINT_JPEGDCTABLES	0x200	/* JPEG DC tables */
.fi
.SH NOTES
In C++ the
.I flags
parameter defaults to 0.
.SH "RETURN VALUES"
None.
.SH DIAGNOSTICS
None.
.SH "SEE ALSO"
.IR libtiff (3TIFF),
.IR TIFFOpen (3TIFF),
.IR TIFFReadDirectory (3TIFF),
.IR TIFFSetDirectory (3TIFF)
