.\"
.\" Copyright (c) 2002, <PERSON><PERSON> <<EMAIL>>
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFDataWidth 3TIFF "September 12, 2002" "libtiff"
.SH NAME
TIFFDataWidth \- Get the size of TIFF data types
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.BI "int TIFFDataWidth(TIFFDataType " type ")"
.SH DESCRIPTION
.I TIFFDataWidth
returns a size of
.I type
in bytes.
Currently following data types are supported:
.br
.I TIFF_BYTE
.br
.I TIFF_ASCII
.br
.I TIFF_SBYTE
.br
.I TIFF_UNDEFINED
.br
.I TIFF_SHORT
.br
.I TIFF_SSHORT
.br
.I TIFF_LONG
.br
.I TIFF_SLONG
.br
.I TIFF_FLOAT
.br
.I TIFF_IFD
.br
.I TIFF_RATIONAL
.br
.I TIFF_SRATIONAL
.br
.I TIFF_DOUBLE
.br
.SH "RETURN VALUES"
.br
.IR TIFFDataWidth
returns a number of bytes occupied by the item of given type. 0 returned when
uknown data type supplied.
.SH "SEE ALSO"
.BR libtiff (3TIFF),
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
