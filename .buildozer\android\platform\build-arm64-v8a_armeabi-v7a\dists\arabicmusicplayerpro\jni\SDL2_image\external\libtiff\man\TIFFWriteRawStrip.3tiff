.\"
.\" Copyright (c) 1988-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFWriteRawstrip 3TIFF "October 15, 1995" "libtiff"
.SH NAME
TIFFWriteRawStrip \- write a strip of raw data to an open
.SM TIFF
file
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.BI "tsize_t TIFFWriteRawStrip(TIFF *" tif ", tstrip_t " strip ", tdata_t " buf ", tsize_t " size ")"
.SH DESCRIPTION
Append
.I size
bytes of raw data to the specified strip.
.SH NOTES
The strip number must be valid according to the current settings of the
.I ImageLength
and
.I RowsPerStrip
tags.
An image may be dynamically grown by increasing the value of
.I ImageLength
prior to each call to
.IR TIFFWriteRawStrip .
.SH "RETURN VALUES"
\-1 is returned if an error occurred.
Otherwise, the value of
.IR size 
is returned.
.SH DIAGNOSTICS
All error messages are directed to the
.BR TIFFError (3TIFF)
routine.
.PP
\fB%s: File not open for writing\fP.
The file was opened for reading, not writing.
.PP
\fBCan not write scanlines to a tiled image\fP. The image is assumed to be
organized in tiles because the
.I TileWidth
and
.I TileLength
tags have been set with
.BR TIFFSetField (3TIFF).
.PP
\fB%s: Must set "ImageWidth" before writing data\fP.
The image's width has not be set before the first write.
See
.BR TIFFSetField (3TIFF)
for information on how to do this.
.PP
\fB%s: Must set "PlanarConfiguration" before writing data\fP.
The organization of data has not be defined before the first write.
See
.BR TIFFSetField (3TIFF)
for information on how to do this.
.PP
\fB%s: No space for strip arrays"\fP.
There was not enough space for the arrays that hold strip
offsets and byte counts.
.PP
\fB%s: Strip %d out of range, max %d\fP.
The specified strip is not a valid strip according to the
currently specified image dimensions.
.SH "SEE ALSO"
.BR TIFFOpen (3TIFF),
.BR TIFFWriteEncodedStrip (3TIFF),
.BR TIFFWriteScanline (3TIFF),
.BR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
