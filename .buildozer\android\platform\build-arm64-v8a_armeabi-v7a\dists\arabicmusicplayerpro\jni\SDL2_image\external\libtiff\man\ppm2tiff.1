.\"
.\" Copyright (c) 1991-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH PPM2TIFF 1 "March 1, 2006" "libtiff"
.SH NAME
ppm2tiff \- create a
.SM TIFF
file from 
.SM PPM, PGM
and
.SM PBM
image files
.SH SYNOPSIS
.B ppm2tiff
[
.I options
] [
.I input.ppm
]
.I output.tif
.SH DESCRIPTION
.I ppm2tiff
converts a file in the 
.SM PPM, PGM
and
.SM PBM
image formats to
.SM TIFF.
By default, the
.SM TIFF
image is created with data samples packed (\c
.IR PlanarConfiguration =1),
compressed with the Packbits algorithm (\c
.IR Compression =32773),
and with each strip no more than 8 kilobytes. These characteristics can be
overridden, or explicitly specified with the options described below
.PP
If the
.SM PPM
file contains greyscale data, then the
.I PhotometricInterpretation
tag is set to 1 (min-is-black), otherwise it is set to 2 (RGB).
.PP
If no
.SM PPM
file is specified on the command line,
.I ppm2tiff
will read from the standard input.
.SH OPTIONS
.TP
.B \-c
Specify a compression scheme to use when writing image data:
.B none 
for no compression,
.B packbits
for PackBits compression (will be used by default),
.B lzw
for Lempel-Ziv & Welch compression,
.B jpeg
for baseline JPEG compression,
.B zip
for Deflate compression,
.B g3
for CCITT Group 3 (T.4) compression,
and
.B g4
for CCITT Group 4 (T.6) compression.
.TP
.B \-r
Write data with a specified number of rows per strip; by default the number of
rows/strip is selected so that each strip is approximately 8 kilobytes.
.TP
.B \-R
Mark the resultant image to have the specified X and Y resolution (in
dots/inch).
.SH "SEE ALSO"
.BR tiffinfo (1),
.BR tiffcp (1),
.BR tiffmedian (1),
.BR libtiff (3)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
