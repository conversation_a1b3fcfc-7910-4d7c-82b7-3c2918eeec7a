2020-12-19  <PERSON>  <b<PERSON><PERSON><PERSON>@simple.dallas.tx.us>

	* libtiff 4.2.0 released.

	* configure.ac: Pass tar-ustar option to AM_INIT_AUTOMAKE rather
	than tar-pax since ustar POSIX 1003.1-1988 format is more portable
	than PAX POSIX 1003.1-2001 format.

2020-12-12  Even Rouault  <<EMAIL>>

	Merge branch 'w_adjust-deflate_names' into 'master'
	Set 'deflate' to DEFLATE_NAMES, instead of 'libdeflate'

	See merge request libtiff/libtiff!174

2020-12-12  Le<PERSON>res Lemniscati  <<EMAIL>>

	Set 'deflate' to DEFLATE_NAMES, instead of 'libdeflate'
	'lib' will be automatically added as a prefix while doing find_library()

2020-12-12  Even Rouault  <<EMAIL>>

	DoubleToRational(): avoid casting NaN to uint32 (fixes #227)

2020-12-12  Even Rouault  <<EMAIL>>

	Merge branch 'fix_221' into 'master'
	tiffio.h: do not define __attribute__ but defines TIFF_ATTRIBUTE instead (fixes #221)

	Closes #221

	See merge request libtiff/libtiff!173

2020-12-12  Even Rouault  <<EMAIL>>

	tiffio.h: do not define __attribute__ but defines TIFF_ATTRIBUTE instead (fixes #221)

2020-12-08  Even Rouault  <<EMAIL>>

	TIFFReadDirEntryArrayWithLimit(): properly read from offline tag value when we clamp the number of strips to 1.
	Fixes regression of commit 7057734d986001b7fd6d2afde9667da7754ff2cc on reading
	a file with StripByteCounts with 1 element (broken) and StripOffsets with
	896 elements, and where StripOffsets[0] is correct

	$ tiffdump foo.tif
	Magic: 0x4949 <little-endian> Version: 0x2a <ClassicTIFF>
	Directory 0: offset 25725448 (0x1888a08) next 0 (0)
	SubFileType (254) LONG (4) 1<0>
	ImageWidth (256) LONG (4) 1<640>
	ImageLength (257) LONG (4) 1<20098>
	BitsPerSample (258) SHORT (3) 1<16>
	Photometric (262) SHORT (3) 1<1>
	SamplesPerPixel (277) SHORT (3) 1<1>
	ResolutionUnit (296) SHORT (3) 1<2>
	StripByteCounts (279) LONG (4) 1<1806>
	StripOffsets (273) LONG (4) 896<8 648 1288 1928 2568 3208 3848 4488 5128 5768 6408 7048 7688 8328 8968 9608 10248 10888 11528 12168 12808 13448 14088 14728 ...>

2020-12-02  Even Rouault  <<EMAIL>>

	tif_jpeg.c: avoid potential harmless unsigned integer overflow on data->fileoffset in JPEGFixupTagsSubsamplingSkip() by validating earlier. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=28200

2020-11-27  Even Rouault  <<EMAIL>>

	Merge branch 'Jamaika1-master-patch-47839' into 'master'
	Change ULARGE_INTEGER to LARGE_INTEGER

	See merge request libtiff/libtiff!170

2020-11-27  Even Rouault  <<EMAIL>>

	Merge branch 'Jamaika1-master-patch-46397' into 'master'
	Added stdint.h

	See merge request libtiff/libtiff!171

2020-11-27  Jamaika  <<EMAIL>>

	Added stdint.h.

	``` tif_win32.c: In function '_tiffSizeProc': tif_win32.c:159:23: warning: passing argument 2 of 'GetFileSizeEx' from incompatible pointer type [-Wincompatible-pointer-types]   159 |  if (GetFileSizeEx(fd,&m))       |                       ^~       |                       |       |                       ULARGE_INTEGER * In file included from c:\msys1021\x86_64-w64-mingw32\include\winbase.h:18,                  from c:\msys1021\x86_64-w64-mingw32\include\windows.h:70,                  from tif_win32.c:32: c:\msys1021\x86_64-w64-mingw32\include\fileapi.h:78:73: note: expected 'PLARGE_INTEGER' {aka 'LARGE_INTEGER *'} but argument is of type 'ULARGE_INTEGER *'    78 |   WINBASEAPI WINBOOL WINAPI GetFileSizeEx (HANDLE hFile, PLARGE_INTEGER lpFileSize);       |                                                          ~~~~~~~~~~~~~~~^~~~~~~~~~ ```

2020-11-21  Even Rouault  <<EMAIL>>

	Merge branch 'issue-113' into 'master'
	tiffcrop: fix buffer overrun in extractContigSamples24bits()

	Closes #113

	See merge request libtiff/libtiff!169

2020-11-21  Even Rouault  <<EMAIL>>

	Merge branch 'issue-156' into 'master'
	tiff2pdf: Check output size before writing

	Closes #156

	See merge request libtiff/libtiff!168

2020-11-21  Even Rouault  <<EMAIL>>

	Merge branch 'issue-201' into 'master'
	tiff2pdf: enforce memory limit for tiled pictures too

	Closes #201

	See merge request libtiff/libtiff!167

2020-11-20  Even Rouault  <<EMAIL>>

	Merge branch 'issue-207' into 'master'
	enforce (configurable) memory limit in tiff2rgba

	Closes #209 et #207

	See merge request libtiff/libtiff!165

2020-11-20  Even Rouault  <<EMAIL>>

	tif_lzw.c: avoid false positive -Wnull-dereference of mingw32 gcc 7.3.

2020-11-17  Thomas Bernard  <<EMAIL>>

	tiffcrop: fix buffer overrun in extractContigSamples24bits()
	fixes #113

	tiff2pdf: Check output size before writing.
	fixes #156

	tiff2pdf: enforce memory limit for tiled pictures too.
	fixes #201

2020-11-15  Thomas Bernard  <<EMAIL>>

	tiff2rgba.1: -M option.

	enforce (configurable) memory limit in tiff2rgba.
	fixes #207
	fixes #209

2020-11-14  Even Rouault  <<EMAIL>>

	Merge branch 'issue-220' into 'master'
	tiff2pdf.c: properly calculate datasize when saving to JPEG YCbCr

	Closes #220

	See merge request libtiff/libtiff!159

2020-11-14  Thomas Bernard  <<EMAIL>>

	tiff2pdf.c: properly calculate datasize when saving to JPEG YCbCr.
	fixes #220

2020-11-14  Even Rouault  <<EMAIL>>

	Merge branch 'issue-204' into 'master'
	avoid buffer overflow while writing jpeg end of file marker

	Closes #204

	See merge request libtiff/libtiff!161

2020-11-14  Even Rouault  <<EMAIL>>

	Merge branch 'issue-193' into 'master'
	fix buffer overflow in tiff2ps.c

	Closes #193

	See merge request libtiff/libtiff!162

2020-11-14  Even Rouault  <<EMAIL>>

	Merge branch 'skal65535-master-patch-91082' into 'master'
	More overflow fixes for large widths

	See merge request libtiff/libtiff!164

2020-11-14  skal  <<EMAIL>>

	More overflow fixes for large width.
	Also: use INT_MAX instead of hard-coded constants.

2020-11-12  Even Rouault  <<EMAIL>>

	Merge branch 'skal65535-master-patch-56655' into 'master'
	Fix potential overflow in gtStripContig()

	See merge request libtiff/libtiff!163

2020-11-12  Even Rouault  <<EMAIL>>

	Merge branch 'issue-211' into 'master'
	check for tile width overflow

	Closes #211

	See merge request libtiff/libtiff!160

2020-11-12  skal  <<EMAIL>>

	Fix potential overflow in gtStripContig()
	(w + w) might not fit in int32 if too large.

2020-11-09  Thomas Bernard  <<EMAIL>>

	tiff2ps.c: fix buffer overread.
	fixes #193

	fix undefined behaviour (int shifted too much to the left)

	avoid buffer overflow while writing jpeg end of file marker.
	fixes #204

	gtTileContig(): check Tile width for overflow.
	fixes #211

	fix warning messages (v32 is unsigned)

2020-10-26  Even Rouault  <<EMAIL>>

	TIFFStartStrip(): avoid potential crash in WebP codec when using scanline access on corrupted files. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=26650

2020-10-20  Even Rouault  <<EMAIL>>

	tif_webp.c: validate tile/strip dimension to avoid unsigned integer overflow in RGBA.size computation

2020-10-19  Even Rouault  <<EMAIL>>

	tif_zip.c: fix typo in comment.

2020-10-16  Even Rouault  <<EMAIL>>

	tiff.h: remove irrelevant warning about webp related pseudo-tags not being registered: they are purely internal libtiff concepts

2020-10-16  Even Rouault  <<EMAIL>>

	Merge branch 'libdeflate' into 'master'
	Add support for building against libdeflate for faster Zip/Deflate compression/decompression

	See merge request libtiff/libtiff!158

2020-10-16  Even Rouault  <<EMAIL>>

	test: add testdeflatelaststripextradata.sh.

2020-10-16  Even Rouault  <<EMAIL>>

	Add support for optional building against libdeflate for faster Zip/Deflate compression/decompression.
	So we can have 2 kind of builds with the Zip/Deflate codec:
	- zlib only
	- zlib + libdeflate

	Speed improvements in the 35%-50% range can be expected when libdeflate is used.
	Compression level up to 12 is now supported (capped to 9 when zlib is used).
	Still requires zlib for situations where libdeflate cannot be used (that
	is for scanline access, since libdeflate has no streaming mode)

	Pseudo-tag TIFFTAG_DEFLATE_SUBCODEC=DEFLATE_SUBCODEC_ZLIB/DEFLATE_SUBCODEC_LIBDEFLATE
	is added to control which subcodec (zlib or libdeflate) should be used (it defaults
	of course to libdeflate, when it is available).
	This is mostly aimed at being used on the writing side, to be able to reproduce
	output of previous libtiff versions at a binary level, in situations where this would
	be really needed. Or as a safety belt in case there would be unforeseen issues
	with using libdeflate.
	It can be used to know when libdeflate is available at runtime (DEFLATE_SUBCODEC_LIBDEFLATE
	will be the default value in that situation).

	Of course, deflate codestreams produced by libdeflate can be read by zlib, and vice-versa.

2020-10-14  Even Rouault  <<EMAIL>>

	tif_webp.c: fix compiler warnings with MSVC.

2020-10-12  Even Rouault  <<EMAIL>>

	Merge branch 'various_fixes' into 'master'
	Fix compiler warnings about unused variables when assert() expands to nothing

	See merge request libtiff/libtiff!157

2020-10-12  Even Rouault  <<EMAIL>>

	.gitignore: add entries for new files in test/

	Fix compiler warnings about unused variables when assert() expands to nothing

2020-10-09  Roger Leigh  <<EMAIL>>

	Merge branch '215-cygwin-appveyor-fail' into 'master'
	Update Appveyor CI build to build with VS2019 image

	Closes #215

	See merge request libtiff/libtiff!154

2020-10-09  Roger Leigh  <<EMAIL>>

	wip.

	wip.

	wip.

	wip.

	wip.

	wip.

2020-10-09  Roger Leigh  <<EMAIL>>

	Merge branch 'TIFF-217_m_lib_path' into 'master'
	cmake: Do not use absolute libm path

	Closes #217

	See merge request libtiff/libtiff!156

2020-10-09  Roger Leigh  <<EMAIL>>

	cmake: Do not use absolute libm path.

2020-10-08  Even Rouault  <<EMAIL>>

	tif_fax3.h: restore systematic calls to CLEANUP_RUNS()
	now that SETVALUE() no longer cause overflows.
	Those were removed per b351db8be1b4d3f712bdb9424a79d3174cc03202 and
	3440ac216463fcad170bbb391491e69730a59ffa.

	As SETVALUE() now returns an error, this allow the decoder to exit.

	Otherwise, the assert(x == lastx) in _TIFFFax3fillruns() can trigger.

	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=26201

2020-10-06  Even Rouault  <<EMAIL>>

	Merge branch 'check_TIFFFlushData1' into 'master'
	FAX/JPEG/LZMA/PixarLog/ZIP/ZSTD codecs: make sure to check TIFFFlushData1() return value

	See merge request libtiff/libtiff!155

2020-10-04  Even Rouault  <<EMAIL>>

	Merge branch 'shared-memory' into 'master'
	Set the --shared-memory linker flag for Emscripten builds

	See merge request libtiff/libtiff!153

2020-10-03  Even Rouault  <<EMAIL>>

	tiff2rgba.c: fix -Wold-style-declaration warning.

	FAX/JPEG/LZMA/PixarLog/ZIP/ZSTD codecs: make sure to check TIFFFlushData1() return value

2020-09-26  Even Rouault  <<EMAIL>>

	tif_fax3.h: extra buffer overflow checks. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=25934

2020-09-25  Roger Leigh  <<EMAIL>>

	wip.

	wip.

	wip.

	wip.

	wip.

	wip.

	Update AppVeyor image.

	test-appveyor.

2020-09-24  Attila Oláh  <<EMAIL>>

	Also pass --shared-memory to raw_decode.
	This is needed when building for Emscripten with *both* WEBP and JPEG
	support.

	Set the --shared-memory linker flag for Emscripten builds.
	This is only needed when building with WEBP support, which uses atomics,
	therefore the linker needs the --shared-memory flag. The flag cannot be
	added globally because not all executables link against libwebp.

2020-09-22  Even Rouault  <<EMAIL>>

	tif_fax3.h: return error when a buffer overflow occurs. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=25552 and https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=25849

2020-09-11  Even Rouault  <<EMAIL>>

	Merge branch 'fix-float-compare' into 'master'
	Fix comparison for max negative float value.

	See merge request libtiff/libtiff!152

2020-09-11  Dirk Lemstra  <<EMAIL>>

	Fix comparison for max negative float value.

2020-09-07  Even Rouault  <<EMAIL>>

	Fax3PreDecode(): reset curruns and refruns state variables.
	to avoid out-of-bounds write triggered by GDAL when repeatedly
	reading a corrupt strip.

	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=25493

2020-06-06  Thomas Bernard  <<EMAIL>>

	Merge branch 'issue-17' into 'master'
	normalize tools behaviour regarding -h

	Closes #17

	See merge request libtiff/libtiff!115

2020-05-31  Even Rouault  <<EMAIL>>

	TWebPSetupEncode(): fix logic problem (and instead of or) in test that checks input is 8bit unsigned data

2020-05-12  Even Rouault  <<EMAIL>>

	TIFFGetConfiguredCODECs(): fix to avoid wrong structure to be returned for registered (ie non built-in) codecs

2020-05-09  Even Rouault  <<EMAIL>>

	Merge branch 'zstd-webp-update' into 'master'
	gitlab-ci: use latest zstd and webp versions

	See merge request libtiff/libtiff!148

2020-05-09  Even Rouault  <<EMAIL>>

	Merge branch 'deprecated' into 'master'
	ojpeg: s/Depreciated/Deprecated/

	See merge request libtiff/libtiff!149

2020-05-09  Aaron Boxer  <<EMAIL>>

	ojpeg: s/Depreciated/Deprecated/

2020-04-27  Even Rouault  <<EMAIL>>

	Fix typos.

	tif_jpeg.c: avoid potential division in previous fix (master only)

2020-04-26  Thomas Bernard  <<EMAIL>>

	gitlab-ci: use latest zstd and webp versions.

2020-04-26  Even Rouault  <<EMAIL>>

	tiff.h: fixes to use ASCII only characters (master only)

2020-04-26  Thomas Bernard  <<EMAIL>>

	tiffsplit: use EXIT_SUCCESS / EXIT_FAILURE.

	tiffset: print usage on stdout when -h is used.
	also use EXIT_FAILURE / EXIT_SUCCESS
	see #17

	tiffmedian: shopw usage on stdout when -h is used.
	aslo use EXIT_SUCCESS/EXIT_FAILURE
	see #17

	tiffinfo: print usage on stdout when -h is used.
	also use EXIT_FAILURE / EXIT_SUCCESS
	see #17

	raw2tiff: print usage to stdout when -h is used.
	see #17

	tiff2pdf: print usage on stdout when -h is used.
	see #17

	tiffgt: output usage on stdout with -h.
	also use EXIT_SUCCESS / EXIT_FAILURE

	tiffdump: use EXIT_FAILURE / EXIT_SUCCESS.
	see #17

	tiffdither: print usage on stdout when -h is used.
	see #17

2020-04-26  Thomas Bernard  <<EMAIL>>

	tiffcrop: -h / -v prints usage/version to stdout.
	also uses the standard C EXIT_SUCCESS / EXIT_FAILURE
	macros

	see #17

2020-04-26  Thomas Bernard  <<EMAIL>>

	tiffcp: output usage to stdout when using -h.
	also use EXIT_FAILURE / EXIT_SUCCESS
	see #17

	tiffcmp: match exit status for posix cmp and diff tools.

	tiff2rgba: output usage to stdout when using -h.
	also uses std C EXIT_FAILURE / EXIT_SUCCESS
	see #17

	tiff2ps: sue EXIT_FAILURE / EXIT_SUCCESS.
	see #17

	tiff2bw: output usage on stdout when using -h.
	also uses EXIT_SUCCESS / EXIT_FAILURE
	see #17

	thumbnail: use EXIT_FAILURE / EXIT_SUCCESS.
	the -h option was already used so it cannot be used for help/usage
	see #17

	rgb2ycbcr: use EXIT_FAILURE / EXIT_SUCCESS.
	the -h option was already used so it cannot be used for help/usage
	see #17

	ppm2tiff: output usage to stdout when using -h option.
	also uses std C EXIT_SUCCESS / EXIT_FAILURE
	see #17

	pal2rgb: output usage to stdout when -h is used.
	see #17

	fax2tiff.c: print usage on stdout when using -h option.
	see #17

	fax2ps: output usage to stdout when using -h option.
	also use EXIT_SUCCESS, EXIT_FAILURE from C standard

2020-04-25  Even Rouault  <<EMAIL>>

	Merge branch 'jpeg_multiscan_dos_logic' into 'master'
	tif_jpeg.c: revise logic to detect potential excessive memory usage when...

	See merge request libtiff/libtiff!147

2020-04-24  Even Rouault  <<EMAIL>>

	Merge branch 'issue-176' into 'master'
	tiff2pdf: get rid of uninitialized memory content

	Closes #176

	See merge request libtiff/libtiff!143

2020-04-24  Even Rouault  <<EMAIL>>

	tif_jpeg.c: revise logic to detect potential excessive memory usage when decoding multiscan JPEG compressed images

2020-04-19  Thomas Bernard  <<EMAIL>>

	tiff2pdf: test the return code of TIFFReadRawStrip() and TIFFReadRawTile()

	tiff2pdf.c: fix some whitespace problems in source.

	tiff2pdf: get rid of uninitialized memory content.
	fixes #176

2020-04-19  Even Rouault  <<EMAIL>>

	Merge branch 'issue-18' into 'master'
	tiffset: pass size for TIFFTAG_INKNAMES

	Closes #18

	See merge request libtiff/libtiff!146

2020-04-18  Olivier Paquet  <<EMAIL>>

	Merge branch 'issue-80' into 'master'
	tiffinfo: fix dump of Tiled images

	Closes #80

	See merge request libtiff/libtiff!144

2020-04-15  Even Rouault  <<EMAIL>>

	Fix wrong file size checks for memory-mapped BigTIFF files that could lead to image rejection

2020-04-05  Thomas Bernard  <<EMAIL>>

	tiffset: pass size for TIFFTAG_INKNAMES.
	Uses TIFFFieldPassCount() to know which arguments need to be
	passed to TiffSetField()

	fixes #18
	see http://bugzilla.maptools.org/show_bug.cgi?id=2202

2020-04-04  Thomas Bernard  <<EMAIL>>

	tiffinfo: showdata for tiled images.

	tiffinfo: fix dump of Tiled images.
	fixes #80

2020-04-03  Even Rouault  <<EMAIL>>

	Merge branch 'issue-117' into 'master'
	tiffcrop: enforce memory allocation limit

	Closes #117

	See merge request libtiff/libtiff!140

2020-04-03  Thomas Bernard  <<EMAIL>>

	tiffcrop: enforce memory allocation limit.
	uses -k option to change limit (default to 256MiB)
	fixes #117 / http://bugzilla.maptools.org/show_bug.cgi?id=2757

2020-04-02  Even Rouault  <<EMAIL>>

	Merge branch 'issue-45' into 'master'
	tiffcp: disable strip chopping when trying to convert to JBIG compression

	Closes #45

	See merge request libtiff/libtiff!138

2020-04-02  Even Rouault  <<EMAIL>>

	Merge branch 'issue-124' into 'master'
	TIFFGetFields(3tiff): TIFFTAG_*BYTECOUNTS TIFFTAG_*OFFSETS are uint64

	Closes #124

	See merge request libtiff/libtiff!137

2020-04-02  Even Rouault  <<EMAIL>>

	Merge branch 'aix_itrunc' into 'master'
	Rename itrunc to fix name clash with a different itrunc in math.h on AIX. Fixes issue #189

	Closes #189

	See merge request libtiff/libtiff!139

2020-04-01  Rob Boehne  <<EMAIL>>

	Rename itrunc to fix name clash with a different itrunc in math.h on AIX. Fixes issue #189

2020-04-01  Thomas Bernard  <<EMAIL>>

	tiffcp: disable strip chopping when trying to convert to JBIG compression
	fixes #45

2020-03-29  Thomas Bernard  <<EMAIL>>

	TIFFGetFields(3tiff): TIFFTAG_*BYTECOUNTS TIFFTAG_*OFFSETS are uint64.
	fixes #124 / http://bugzilla.maptools.org/show_bug.cgi?id=2774

2020-03-29  Even Rouault  <<EMAIL>>

	Merge branch 'issue-48' into 'master'
	tiff2pdf: fix "raw" copy of Deflate streams

	Closes #48

	See merge request libtiff/libtiff!136

2020-03-27  Thomas Bernard  <<EMAIL>>

	tiff2pdf: fix "raw" copy of Deflate streams.
	The Predictor parametter was not copied from the source tiff to the PDF.
	fixes #48 / http://bugzilla.maptools.org/show_bug.cgi?id=2442

2020-03-26  Thomas Bernard  <<EMAIL>>

	tif_fax3: quit Fax3Decode2D() when a buffer overflow occurs.
	fixes #186

2020-03-24  Even Rouault  <<EMAIL>>

	Merge branch 'issue-143-144' into 'master'
	tiffdump: avoid unaligned memory access

	Closes #144 et #143

	See merge request libtiff/libtiff!133

2020-03-24  Even Rouault  <<EMAIL>>

	Merge branch 'issue-133' into 'master'
	tiff2pdf: avoid divide by 0

	Closes #133

	See merge request libtiff/libtiff!126

2020-03-24  Thomas Bernard  <<EMAIL>>

	tiff2pdf: normalizePoint() macro to normalize the white point.

2020-03-23  Thomas Bernard  <<EMAIL>>

	tiffdump: avoid unaligned memory access.
	fixes #143
	fixes #144

2020-03-23  Even Rouault  <<EMAIL>>

	Merge branch 'out-of-memory' into 'master'
	tiffcp/tiff2pdf/tiff2ps: enforce maximum malloc size

	Closes #153, #84, #116 et #115

	See merge request libtiff/libtiff!130

2020-03-23  Even Rouault  <<EMAIL>>

	Merge branch 'issue-157' into 'master'
	tiffset: check memory allocation

	Closes #157

	See merge request libtiff/libtiff!132

2020-03-23  Even Rouault  <<EMAIL>>

	Merge branch 'issue-185' into 'master'
	tif_fax3: more buffer overflow checks in Fax3Decode2D()

	Closes #185

	See merge request libtiff/libtiff!131

2020-03-23  Thomas Bernard  <<EMAIL>>

	tiffset: check memory allocation.
	fixes #157 / http://bugzilla.maptools.org/show_bug.cgi?id=2850

	tif_fax3: more buffer overflow checks in Fax3Decode2D()
	fixes #185

2020-03-21  Thomas Bernard  <<EMAIL>>

	tiff2ps: enforce memory allocation limit.
	fixes #153 / http://bugzilla.maptools.org/show_bug.cgi?id=2845

	tiff2pdf: enforce maximum data size.
	fixes #116 / http://bugzilla.maptools.org/show_bug.cgi?id=2756
	fixes #84 / http://bugzilla.maptools.org/show_bug.cgi?id=2683

	update man page for tiffcp regarding the -m option.

	tiffcp.c:  _TIFFmalloc() => limitMalloc()

2020-03-21  Thomas Bernard  <<EMAIL>>

	tiffcp: enforce maximum malloc size.
	default is 256MB. use -m option to change

	fixes #115 / http://bugzilla.maptools.org/show_bug.cgi?id=2755

2020-03-21  Even Rouault  <<EMAIL>>

	Merge branch 'issue-184' into 'master'
	CmakeLists.txt: define WORDS_BIGENDIAN when the CPU is big endian

	Closes #184

	See merge request libtiff/libtiff!127

2020-03-21  Even Rouault  <<EMAIL>>

	Merge branch 'issue-44' into 'master'
	tiff2pdf: "" causes the relevant argument not to be written

	Closes #44

	See merge request libtiff/libtiff!128

2020-03-21  Even Rouault  <<EMAIL>>

	Merge branch 'issue-56' into 'master'
	fix man for TIFFReadEncodedStrip(), TIFFStripSize, TIFFVStripSize, TIFFRawStripSize

	Closes #56

	See merge request libtiff/libtiff!129

2020-03-20  Thomas Bernard  <<EMAIL>>

	fix man for TIFFReadEncodedStrip(), TIFFStripSize, TIFFVStripSize, TIFFRawStripSize
	fixes #56
	http://bugzilla.maptools.org/show_bug.cgi?id=2507

	tiff2pdf: "" causes the relevant argument not to be written.
	fixes #44

	CmakeLists.txt: define WORDS_BIGENDIAN when the CPU is big endian.
	fixes #184

2020-03-17  Thomas Bernard  <<EMAIL>>

	tiff2pdf: avoid divide by 0.
	fixes #133 http://bugzilla.maptools.org/show_bug.cgi?id=2796

2020-03-17  Even Rouault  <<EMAIL>>

	Merge branch 'issue-22' into 'master'
	do not _tiffMapProc 0 size files

	Closes #22

	See merge request libtiff/libtiff!125

2020-03-13  Thomas Bernard  <<EMAIL>>

	tif_win32.c: do not _tiffMapProc() 0 sized files.
	see #22

	tif_unix.c: do not _tiffMapProc 0 size files.
	fixes #22
	http://bugzilla.maptools.org/show_bug.cgi?id=2249

2020-03-12  Even Rouault  <<EMAIL>>

	tif_fax3.c: fix warning C4018: '<': signed/unsigned mismatch introduced in past commits

2020-03-11  Even Rouault  <<EMAIL>>

	tiff.h: mention TIFFTAG_RPCCOEFFICIENT, TIFFTAG_TIFF_RSID, TIFFTAG_GEO_METADATA

2020-03-11  Even Rouault  <<EMAIL>>

	Merge branch 'issue-60' into 'master'
	added support for more private tags

	Closes #60

	See merge request libtiff/libtiff!124

2020-03-11  Even Rouault  <<EMAIL>>

	Merge branch 'issue-160' into 'master'
	Fax3SetupState(): check consistency of rowbytes and rowpixels

	Closes #160

	See merge request libtiff/libtiff!123

2020-03-11  Thomas Bernard  <<EMAIL>>

	added support for more private tags.
	see https://gitlab.com/libtiff/libtiff/-/issues/60
	bugzilla.maptools.org/show_bug.cgi?id=2525

	closes #60

	original author : <EMAIL>

2020-03-11  Thomas Bernard  <<EMAIL>>

	Fax3SetupState(): check consistency of rowbytes and rowpixels.
	also add some parameter documentation to Fax3Decode1D()

	fixes #160
	http://bugzilla.maptools.org/show_bug.cgi?id=2854

2020-03-10  Even Rouault  <<EMAIL>>

	Merge branch 'issue-11-const-pointers' into 'master'
	Make pointers returned via TIFFGetField const

	Closes #11

	See merge request libtiff/libtiff!118

2020-03-10  Even Rouault  <<EMAIL>>

	tif_ojpeg.c: relax again too strict sanity checks to allow reading of valid images such as https://gitlab.com/libtiff/libtiff/-/issues/181#note_302535232. Fixes #181

2020-03-09  Even Rouault  <<EMAIL>>

	Merge branch 'issue-52' into 'master'
	contrib/win_dib/tiff2dib: fix Uninitialized variable: lpBits

	Closes #52

	See merge request libtiff/libtiff!121

2020-03-09  Thomas Bernard  <<EMAIL>>

	contrib/win_dib/tiff2dib: fix Uninitialized variable: lpBits.
	fixes #52
	http://bugzilla.maptools.org/show_bug.cgi?id=2469

2020-03-08  Even Rouault  <<EMAIL>>

	Merge branch 'issue-58' into 'master'
	Make TIFFTAG_CFAPATTERN variable count

	Closes #58

	See merge request libtiff/libtiff!120

2020-03-08  Even Rouault  <<EMAIL>>

	Merge branch 'issue-158-no-predictor-in-webp' into 'master'
	TIFFTAG_PREDICTOR is not supported for WebP

	Closes #158

	See merge request libtiff/libtiff!119

2020-03-08  Sam Hasinoff  <<EMAIL>>

	Make TIFFTAG_CFAPATTERN variable count.
	The TIFFTAG_CFAPATTERN tag (33422) from TIFF/EP, recently introduced in libtiff
	3363eda09d082e3e1dfffa6281f53085cac51ad3 / http://bugzilla.maptools.org/show_bug.cgi?id=2457
	is described as having a fixed count of 4.
	But the TIFF/EP spec says this should support a variable count (= CFARepeatRows * CFARepeatCols):

	TIFF/EP, ISO 12234-2:2001
	http://www.barrypearson.co.uk/top2009/downloads/TAG2000-22_DIS12234-2.pdf
	page 18 and 26

2020-03-08  Thomas Bernard  <<EMAIL>>

	TIFFTAG_PREDICTOR is not supported for WebP.
	fixes #158
	https://gitlab.com/libtiff/libtiff/-/issues/158

	this bug was introduced by 9eacd59fecc4ef593ac17689bc530ab451c8ec14
	merge request !32

2020-03-07  Adam Goode  <<EMAIL>>

	Make the default whitepoint and ycbcrcoeffs arrays const.
	Now that we are returning const pointers in TIFFGetFieldDefaulted,
	we can now make these static default arrays const.

	see #11

2020-03-07  Adam Goode  <<EMAIL>>

	Make pointers returned via TIFFGetField const.
	According to http://bugzilla.maptools.org/show_bug.cgi?id=2125#c6
	callers are not allowed to modify pointer or array values returned from
	TIFFGetField or the like. So, make this explicit in the documentation
	by specifying these things as const. Note that this is not an ABI
	change, since C does not encode const in libraries. Also, this is
	not really an API change, since the varargs call strips away all
	the types anyway. So it really is more of a documentation change.

	fixes #11

2020-03-07  Even Rouault  <<EMAIL>>

	CMake: Skip custom_dir_EXIF_231 test on shared builds to avoid issues on Windows

2020-03-07  Even Rouault  <<EMAIL>>

	Merge branch 'EXIF231_GPS_upgrade' into 'master'
	EXIF 2.32 and GPS TIFF-tags and functionality upgraded.

	See merge request libtiff/libtiff!91

2020-03-07  Su_Laus  <<EMAIL>>

	EXIF 2.32 and GPS tags and functionality upgraded.
	- Existing EXIF field definition of tags is upgraded to EXIF version 2.3.2
	- EXIF-GPS structure, tags and access functions are added as special CustomDirectory (like it was done for EXIF).
	- Test program custom_dir_EXIF_231.c added to test writing/reading of EXID IFD and GPS IFD tags
	  and to highlight some quirks of IFD-handling and peculiarities of reading/writing the different data types.
	- Reading error for FileSource and SceneType tags corrected.

	- EXIF_GPS_upgrade rebased onto c8c5309b765ef4ff097d2aaffbdb8f403db8967d (Merge branch 'Rational2DoublePrecision_correction' into 'master')
	and adapted:
	- tif_dirinfo.c:         All rational tags set to TIFF_SETGET_FLOAT but only the GPSTAG_ tags set to TIFF_SETGET_DOUBLE.
	- custom_dir_EXIF_231.c: Editorials amended and gcc warnigs fixed.
	- CMakeLists.txt: add_test(NAME "custom_dir_EXIF_231"  COMMAND "custom_dir_EXIF_231")  added.

2020-03-07  Even Rouault  <<EMAIL>>

	Merge branch 'issue-55' into 'master'
	ppm2tiff: support any bps value from 1 to 16

	Closes #55

	See merge request libtiff/libtiff!106

2020-03-07  Thomas Bernard  <<EMAIL>>

	ppm2tiff: Add test for 16bpc PPM.

	ppm2tiff: remove unused argument warning.

2020-03-07  Ludolf Holzheid  <<EMAIL>>

	ppm2tiff: support any bps value from 1 to 16.
	fix #55
	http://bugzilla.maptools.org/show_bug.cgi?id=2505

	Patch originally submited by Ludolf Holzheid <<EMAIL>>

2020-03-06  Even Rouault  <<EMAIL>>

	Merge branch 'fax-test' into 'master'
	add test for fax4 decoding

	See merge request libtiff/libtiff!114

2020-03-05  Thomas Bernard  <<EMAIL>>

	add test for fax4 decoding.
	This will check for regression on #46
	https://gitlab.com/libtiff/libtiff/issues/46
	http://bugzilla.maptools.org/show_bug.cgi?id=2434

2020-03-05  Even Rouault  <<EMAIL>>

	Merge branch 'freebsd-tests' into 'master'
	make tests pass under FreeBSD.

	See merge request libtiff/libtiff!113

2020-03-05  Thomas Bernard  <<EMAIL>>

	make tests pass under FreeBSD.
	the -I option for the GNU diff and the FreeBSD diff
	behaves differently regarding escaping the ( ) and |

	By using two -I option, we avoid using such charracters.

2020-03-05  Even Rouault  <<EMAIL>>

	Merge branch 'issue-31' into 'master'
	HTML

	Closes #31

	See merge request libtiff/libtiff!111

2020-03-05  Even Rouault  <<EMAIL>>

	Merge branch 'issue-179' into 'master'
	tif_fax3.h: check for buffer overflow in EXPAND2D before "calling" CLEANUP_RUNS()

	Closes #179

	See merge request libtiff/libtiff!112

2020-03-05  Thomas Bernard  <<EMAIL>>

	v4.1.0.html: fix for validation.
	long <!----------------> comments were replaced
	because they confused some parsers

	add DOCTYPE on v*.html.

	fix HTML files so they are valid according to https://validator.w3.org.

2020-03-05  Thomas Bernard  <<EMAIL>>

	tif_fax3.h: check for buffer overflow in EXPAND2D before "calling" CLEANUP_RUNS()
	fixes #179

	this fixes the regression introduced in 02bb0175 / 72c4acef
	( merge request !110 )

	It may be a better fix to do the overflow check in SETVALUE() but the
	macro do { } while(0) construct makes it difficult to quit the loop
	properly.

2020-03-01  Thomas Bernard  <<EMAIL>>

	index.html: fix unclosed <tt> tag.

2020-03-01  Thomas Bernard  <<EMAIL>>

	html: do not force colors (which are default anyway)
	If needed, style should be set using CSS.

	fixes #31
	https://gitlab.com/libtiff/libtiff/issues/31
	http://bugzilla.maptools.org/show_bug.cgi?id=2326

2020-03-01  Even Rouault  <<EMAIL>>

	TIFFReadCustomDirectory(): fix potential heap buffer overflow when reading a custom directory, after a regular directory where a codec was active. Fixes https://gitlab.com/libtiff/libtiff/issues/178

2020-03-01  Even Rouault  <<EMAIL>>

	Merge branch 'issue-46' into 'master'
	fix decoding of fax4 images

	Closes #46

	See merge request libtiff/libtiff!110

2020-02-29  Thomas Bernard  <<EMAIL>>

	tif_fax3: better fix for CVE-2011-0192.
	There are some legitimate case which were forbidden by the previous fix

	tif_fax3.h: allow 0 length run in DECODE2D.
	fixes #46
	https://gitlab.com/libtiff/libtiff/issues/46
	http://bugzilla.maptools.org/show_bug.cgi?id=2434

2020-02-29  Even Rouault  <<EMAIL>>

	Merge branch 'mingwlibm' into 'master'
	Don't use libm with libtiff due to conflict with libmsvcrt

	See merge request libtiff/libtiff!73

2020-02-29  Even Rouault  <<EMAIL>>

	Merge branch 'Rational2DoublePrecision_correction' into 'master'
	tif_dirwrite.c: bugfix DoubleToSrational() for plain signed integers

	See merge request libtiff/libtiff!109

2020-02-29  Su_Laus  <<EMAIL>>

	tif_dirwrite.c: bugfix DoubleToSrational(), which returns plain signed interger values always as unsigned rationals. Add a test into rational_precision2double.c for "-1.0" and some editorials in tif_dirwrite.c. (code is related to 6df997c786928757caea0dd68d26ea5f098f49df changes).

2020-02-29  Even Rouault  <<EMAIL>>

	Merge branch 'issue-174' into 'master'
	tif_fax3.c: check buffer overflow in Fax4Decode()

	Closes #174

	See merge request libtiff/libtiff!108

2020-02-29  Thomas Bernard  <<EMAIL>>

	Fax4Decode(): log error message in case of buffer overrun.

	tif_fax3.c: check buffer overflow in Fax4Decode()
	fixes #174

2020-02-28  Even Rouault  <<EMAIL>>

	typo fixes in code comments.

	ToRationalEuclideanGCD: remove useless test that confuses Coverity Scan about a potential later modulo by zero

2020-02-27  Even Rouault  <<EMAIL>>

	tif_dirwrite.c: fix other warnings related to 6df997c786928757caea0dd68d26ea5f098f49df changes

	rational_precision2double.c: fix many warnings, and do not build it on CMake on shared lib builds

	tif_dirwrite.c: fix various warnings found when building GDAL with internal libtiff after 6df997c786928757caea0dd68d26ea5f098f49df changes

	tif_dirwrite.c: qualify ToRationalEuclideanGCD() with static.

2020-02-27  Even Rouault  <<EMAIL>>

	Merge branch 'Rational2DoublePrecision' into 'master'
	Rational with Double Precision Upgrade

	See merge request libtiff/libtiff!100

2020-02-27  Su_Laus  <<EMAIL>>

	Rational with Double Precision Upgrade.
	Unfortunately, custom rational tags (TIFF_RATIONAL with field_bit=FIELD_CUSTOM) are defined as TIFF_SETGET_DOUBLE
	but for the reading interface and LibTiff internally they are stored ALLWAYS as floating point SINGLE precision.
	Double precision custom rational tags are not supported by LibTiff.

	For the GPS tags in WGS84 a higher accuracy / precision is needed.
	Therefore, this upgrade is made, keeping the old interface for the already defined tags and allowing a double precision definition,
	as well as calculating rationals with higher accuracy / precision.
	This higher accuracy can be used for newly defined tags like that in EXIF/GPS.

	Refer also to the very old Bugzilla issue 2542 (#69)

	A test file rational_precision2double.c is added, which shows prevention of the old interface to the already defined custom rational tags
	with the standard library as well as with the upgraded library.

	Also TIFFTAG_XRESOLUTION, TIFFTAG_YRESOLUTION, TIFFTAG_XPOSITION, TIFFTAG_YPOSITION amended from TIFF_SETGET_DOUBLE to TIFF_SETGET_FLOAT and testcase inserted in rational_precision2double.c

2020-02-26  Chris Degawa  <<EMAIL>>

	mingw-w64 cmake: Don't find libm.
	mingw-w64 will provide libm symbols by default without -lm and mingw-64's
	libm is just a stub.

	This is just to make sure that on systems with msys2 and also cygwin, cmake
	doesn't find a libm that actually contains math functions.

2020-02-26  Even Rouault  <<EMAIL>>

	Merge branch 'division-by-zero' into 'master'
	tools/tiffcp.c: fix potential division by zero

	See merge request libtiff/libtiff!83

2020-02-26  Even Rouault  <<EMAIL>>

	Merge branch 'fix-unused-warning' into 'master'
	warnings: mark conditionally used parameters

	See merge request libtiff/libtiff!49

2020-02-26  Even Rouault  <<EMAIL>>

	Merge branch 'master' into 'master'
	fix issue #78 warnings regarding RichTIFFIPTC data type

	Closes #78

	See merge request libtiff/libtiff!99

2020-02-26  Even Rouault  <<EMAIL>>

	Merge branch 'win64-handle-casts-warn-fix' into 'master'
	Avoid warnings about casts between HANDLE and int in Win64 builds

	Closes #2

	See merge request libtiff/libtiff!93

2020-02-26  Even Rouault  <<EMAIL>>

	Merge branch 'bug2839' into 'master'
	raw2tiff: avoid divide by 0

	Closes #151

	See merge request libtiff/libtiff!103

2020-02-26  Even Rouault  <<EMAIL>>

	Merge branch 'bug2669' into 'master'
	tiff2pdf: palette bound check in t2p_sample_realize_palette()

	Closes #82

	See merge request libtiff/libtiff!104

2020-02-26  Even Rouault  <<EMAIL>>

	Merge branch 'int-shift' into 'master'
	tiffcrop: fix asan runtime error caused by integer promotion

	See merge request libtiff/libtiff!105

2020-02-26  Even Rouault  <<EMAIL>>

	Merge branch 'bug-2538' into 'master'
	libtiff.html: fix function casing

	Closes #68

	See merge request libtiff/libtiff!107

2020-02-16  Thomas Bernard  <<EMAIL>>

	raw2tiff: avoid divide by 0.
	fixes #151 / http://bugzilla.maptools.org/show_bug.cgi?id=2839

	first memcmp() lines before computing corellation
	and always avoid divide by 0 anyway

2020-02-09  Even Rouault  <<EMAIL>>

	Merge branch 'bug2855' into 'master'
	tiff2ps: fix heap buffer read overflow in PSDataColorContig()

	Closes #161

	See merge request libtiff/libtiff!102

2020-02-08  Thomas Bernard  <<EMAIL>>

	libtiff.html: fix function casing.

	libtiff.html: fix function casing.
	fixes #68 / http://bugzilla.maptools.org/show_bug.cgi?id=2538

2020-02-08  Thomas Bernard  <<EMAIL>>

	tiffcrop: fix asan runtime error caused by integer promotion.
	tiffcrop.c:4027:20: runtime error: left shift of 190 by 24 places cannot be represented in type 'int'

	C treats (byte << 24) as an int expression.
	casting explicitely to unsigned type uint32 avoids the problem.

	the same issue has been fixed elsewhere with a24213691616e7cd35aa3e2805493de80c7e4fcf

	I detected the bug with the test file of #86

2020-02-08  Thomas Bernard  <<EMAIL>>

	tiff2pdf: palette bound check in t2p_sample_realize_palette()
	fixes #82

2020-02-08  Thomas Bernard  <<EMAIL>>

	tiff2ps: fix heap buffer read overflow in PSDataColorContig()
	fixes #161 / http://bugzilla.maptools.org/show_bug.cgi?id=2855

	in 05029fb7f1ecf771abaf90b5705b6cab9eb522a7 I missed that 1 extra byte is read
	in this loop.

2020-02-05  Even Rouault  <<EMAIL>>

	tif_dirread.c: suppress CLang static Analyzer 9.0 false positive.

2020-02-01  Even Rouault  <<EMAIL>>

	TIFFSetupStrips: enforce 2GB limitation of Strip/Tile Offsets/ByteCounts arrays
	TIFFWriteDirectoryTagData() has an assertion that checks that the
	arrays are not larger than 2GB. So error out earlier if in that situation.

2020-01-29  Bob Friesenhahn  <<EMAIL>>

	Simplify nmake configuration for building port directory.  Now there is only one boolean setting to enable building strtoll() and strtoull() port functions.  The boolean setting enables the necessary port files to be built, but the remainder of the logic is via pre-processor code in the common tif_config.h, which was prepared before entering the port directory to do a build.

2020-01-28  Bob Friesenhahn  <<EMAIL>>

	Make sure that tif_config.h is produced prior to entering the port directory and add an include path so that the port files can include tif_config.h.  Do not actually include tif_config.h at this time since CMake and Autotools builds are not prepared for that.  This issue could be handled by updating the CMake and Autotools builds or by adding a define which directs libport.h to include tif_config.h.

2020-01-26  Bob Friesenhahn  <<EMAIL>>

	Fix nmake build mistakes in my last commit:
	tif_config.vc.h:

	  Always define HAVE_STRTOL/HAVE_STRTOUL.
	  Define HAVE_STRTOLL/HAVE_STRTOULL if _MSC_VER >= 1900.

	nmake.opt:

	  Provide defaults suitable for MSVC prior to 14.0.

	libport.h:

	  The sense of the pre-processor logic was inverted from what it
	  should be.  The intention is to only provide the prototype if the
	  function is missing.

2020-01-25  Bob Friesenhahn  <<EMAIL>>

	Add nmake build support for manually configuring the 'port' files to be built based on MSVC features. Include tif_config.h in tools/tiffset.c.

2020-01-23  Even Rouault  <<EMAIL>>

	Adjust previous fix to avoid undue warning in some situations triggered by GDAL

2020-01-12  Even Rouault  <<EMAIL>>

	_TIFFPartialReadStripArray: bring back support for non-conformant SLONG8 data type
	Such as in https://github.com/OSGeo/gdal/issues/2165

2020-01-07  Even Rouault  <<EMAIL>>

	test: add test for single-strip OJPEG file without RowsPerStrip tag (like in CR2 files)

	OJPEGReadHeaderInfo: if rowsperstrip not defined, then assume one-single-strip. Complementary fix to 0356ea76bac908c61160d735f078437ace953bd3

2019-12-16  Angel Sánchez  <<EMAIL>>

	fix issue #78 warnings regarding RichTIFFIPTC data type.

2019-12-14  Even Rouault  <<EMAIL>>

	contrib/oss-fuzz/build.sh: fix broken if construct.

2019-11-28  Even Rouault  <<EMAIL>>

	contrib/oss-fuzz/build.sh: other attempt at fixing build failure.

2019-11-20  Even Rouault  <<EMAIL>>

	contrib/oss-fuzz/build.sh: install liblzma-dev for x86_64 builds.

2019-11-17  Even Rouault  <<EMAIL>>

	contrib/oss-fuzz/build.sh: install liblzma-dev:i386 on i386 builds.

2019-11-15  Even Rouault  <<EMAIL>>

	Merge branch 'cmake-parse' into 'master'
	CMake: simplify parsing variables from configure

	See merge request libtiff/libtiff!98

2019-11-15  Rolf Eike Beer  <<EMAIL>>

	CMake: simplify parsing variables from configure.

2019-11-14  Even Rouault  <<EMAIL>>

	contrib/oss-fuzz/build.sh: fix ossfuzz build by statically linking to lzma

2019-11-12  Even Rouault  <<EMAIL>>

	Merge branch 'fix_ojpeg_172' into 'master'
	OJPEG: fix broken sanity check added in 4.1.0 (#fixes 172)

	See merge request libtiff/libtiff!97

2019-11-11  Even Rouault  <<EMAIL>>

	OJPEG: fix broken sanity check added in 4.1.0, and add two OJPEG test files

	test/: add missing generated .sh files.

2019-11-04  Even Rouault  <<EMAIL>>

	Merge branch 'fix-missing-checks-TIFFGetField-tiffcrop' into 'master'
	adds missing checks on TIFFGetField in tiffcrop tool

	Closes #170

	See merge request libtiff/libtiff!96

2019-11-04  Bug Checkers  <<EMAIL>>

	adds missing checks on TIFFGetField in tiffcrop tool (fixes #170)

2019-11-04  Even Rouault  <<EMAIL>>

	Merge branch 'adds-missing-TIFFClose-rgb2ycbcr' into 'master'
	adds a missing TIFFClose in rgb2ycbcr tool

	See merge request libtiff/libtiff!95

2019-11-04  Mansour Ahmadi  <<EMAIL>>

	adds a missing TIFFClose in rgb2ycbcr tool.

2019-11-03  Bob Friesenhahn  <<EMAIL>>

	libtiff 4.1.0 released.

	Added a step for updating the legacy ChangeLog file.

	Ignore emacs temporary files (ending with tilde character).

	Added release summary page for the 4.1.0 release.

	Fix Cmake HAVE_GETOPT for systems which declare getopt in stdio.h. Fix utility baked-in getopt prototype which appears when HAVE_GETOPT is not defined.

	Fax2tiff.sh needs to remove its output file in advance. Syntax changes so that bash is not required.

2019-10-26  Even Rouault  <<EMAIL>>

	tif_jpeg.c: extra cast to silence Coverity warning. GDAL CID 1406475.

2019-10-23  Even Rouault  <<EMAIL>>

	tif_jpeg.c: fix warning added by previous commit (on 32bit builds)

2019-10-23  Even Rouault  <<EMAIL>>

	Merge branch 'coverity-fixes' into 'master'
	Coverity fixes

	See merge request libtiff/libtiff!94

2019-10-22  Timothy Lyanguzov  <<EMAIL>>

	Use 64-bit calculations correctly.

	Fix size calculation to use 64-bit tmsize_t correctly.

	Make bytesperclumpline calculations using tmsize_t type.

2019-10-03  Even Rouault  <<EMAIL>>

	tif_read: align code of TIFFReadRawStrip() and TIFFReadRawTile() that differed for non good reason. Non-functional change normally. (fixes GitLab #162)

2019-10-01  Even Rouault  <<EMAIL>>

	HTML: update for GitLab issues.

2019-09-29  Even Rouault  <<EMAIL>>

	html/v3.5.6-beta.html: redact URL of defunct web site.

	Website: update links to mailing list.

2019-09-17  Even Rouault  <<EMAIL>>

	TIFFReadAndRealloc(): avoid too large memory allocation attempts. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=17244

2019-09-03  Even Rouault  <<EMAIL>>

	ByteCountLooksBad and EstimateStripByteCounts: avoid unsigned integer overflows. Fixes https://oss-fuzz.com/testcase-detail/5686156066291712 and https://oss-fuzz.com/testcase-detail/6332499206078464

2019-09-02  Even Rouault  <<EMAIL>>

	tif_ojpeg.c: avoid relying on isTiled macro being wrapped in ()

	tif_ojpeg.c: avoid use of uninitialized memory on edge/broken file. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16844

	tiff_read_rgba_fuzzer.cc: add a -DSTANDALONE mode for easier reproduction of oss-fuzz reports

2019-09-01  Even Rouault  <<EMAIL>>

	tif_dirread.c: allocChoppedUpStripArrays(). avoid unsigned integer overflow. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16846

2019-08-27  Even Rouault  <<EMAIL>>

	tif_ojpeg.c: avoid unsigned integer overflow. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16793

2019-08-26  Even Rouault  <<EMAIL>>

	TIFFReadDirEntryData(): rewrite to avoid unsigned integer overflow (not a bug). Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16792

	TIFFFetchDirectory(): fix invalid cast from uint64 to tmsize_t. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16784

2019-08-25  Even Rouault  <<EMAIL>>

	JPEG: avoid use of unintialized memory on corrupted files.
	Follow-up of cf3ce6fab894414a336546f62adc57f02590a22c
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16602
	Credit to OSS Fuzz

2019-08-23  Even Rouault  <<EMAIL>>

	_TIFFPartialReadStripArray(): avoid unsigned integer overflow. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16685

	OJPEGWriteHeaderInfo(): avoid unsigned integer overflow on strile dimensions close to UINT32_MAX. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16683

	TIFFFillStrip(): avoid harmless unsigned integer overflow. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16653

	EstimateStripByteCounts(): avoid unsigned integer overflow. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16643&

	tif_ojpeg: avoid unsigned integer overflow (probably not a bug). Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16635

	tif_thunder: avoid unsigned integer overflow (not a bug). Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16632

2019-08-22  Even Rouault  <<EMAIL>>

	_TIFFMultiply32() / _TIFFMultiply64(): avoid relying on unsigned integer overflow (not a bug)

	EstimateStripByteCounts(): avoid unsigned integer overflow.

2019-08-21  Even Rouault  <<EMAIL>>

	EstimateStripByteCounts(): avoid unsigned integer overflow.

2019-08-20  Even Rouault  <<EMAIL>>

	EstimateStripByteCounts(): avoid harmless unsigned integer overflow.

	_TIFFPartialReadStripArray(): avoid triggering unsigned integer overflow with -fsanitize=unsigned-integer-overflow (not a bug, this is well defined by itself)

2019-08-18  Even Rouault  <<EMAIL>>

	tiff2ps: fix use of wrong data type that caused issues (/Height being written as 0) on 64-bit big endian platforms

2019-08-16  Even Rouault  <<EMAIL>>

	setByteArray(): fix previous commit.

	setByteArray(): avoid potential signed integer overflow. Pointed by Hendra Gunadi. No actual problem known (which does not mean there wouldn't be any. Particularly on 32bit builds)

2019-08-15  Even Rouault  <<EMAIL>>

	RGBA interface: fix integer overflow potentially causing write heap buffer overflow, especially on 32 bit builds. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16443. Credit to OSS Fuzz

2019-08-14  Even Rouault  <<EMAIL>>

	Merge branch 'fix_integer_overflow' into 'master'
	Fix integer overflow in _TIFFCheckMalloc() and other implementation-defined behaviour (CVE-2019-14973)

	See merge request libtiff/libtiff!90

2019-08-13  Even Rouault  <<EMAIL>>

	Fix integer overflow in _TIFFCheckMalloc() and other implementation-defined behaviour (CVE-2019-14973)
	_TIFFCheckMalloc()/_TIFFCheckRealloc() used a unsafe way to detect overflow
	in the multiplication of nmemb and elem_size (which are of type tmsize_t, thus
	signed), which was especially easily triggered on 32-bit builds (with recent
	enough compilers that assume that signed multiplication cannot overflow, since
	this is undefined behaviour by the C standard). The original issue which lead to
	this fix was trigged from tif_fax3.c

	There were also unsafe (implementation defied), and broken in practice on 64bit
	builds, ways of checking that a uint64 fits of a (signed) tmsize_t by doing
	(uint64)(tmsize_t)uint64_var != uint64_var comparisons. Those have no known
	at that time exploits, but are better to fix in a more bullet-proof way.
	Or similarly use of (int64)uint64_var <= 0.

2019-08-12  Even Rouault  <<EMAIL>>

	TIFFClientOpen(): fix memory leak if one of the required callbacks is not provided. Fixed Coverity GDAL CID 1404110

	OJPEGReadBufferFill(): avoid very long processing time on corrupted files. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16400. master only

2019-08-10  Even Rouault  <<EMAIL>>

	oss-fuzz/tiff_read_rgba_fuzzer.cc: fix wrong env variable value in previous commit

	oss-fuzz/tiff_read_rgba_fuzzer.cc: avoid issue with libjpeg-turbo and MSAN

	OJPEG: fix integer division by zero on corrupted subsampling factors. Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=15824. Credit to OSS Fuzz

	Merge branch 'ossfuzz_i386'

	contrib/oss-fuzz/build.sh: fix for i386 build of jbigkit, and use $LIB_FUZZING_ENGINE

2019-08-10  Even Rouault  <<EMAIL>>

	Merge branch 'patch-1' into 'master'
	fix two tiny typos

	See merge request libtiff/libtiff!89

2019-08-10  Reto Kromer  <<EMAIL>>

	fix two tiny typos.

2019-08-09  Even Rouault  <<EMAIL>>

	Merge branch 'patch-1' into 'master'
	fix a typo in man page

	See merge request libtiff/libtiff!88

2019-08-09  Reto Kromer  <<EMAIL>>

	fix typo.

2019-08-04  Even Rouault  <<EMAIL>>

	Merge branch 'TIFFTAGID_Zero_reading_IGNORE' into 'master'
	Suppressed Reading of Tiff tags with ID = 0 (like GPSVERSIONID) corrected.

	See merge request libtiff/libtiff!77

2019-08-04  Su Laus  <<EMAIL>>

	Reading of Tiff tags with ID = 0 (like GPSVERSIONID) corrected.
	  IGNORE placeholder in tif_dirread.c is now replaced by a field dir_ignore in the TIFFDirEntry structure

	  Currently, in tif_dirread.c a special IGNORE value for the tif tags is defined
	  in order to flag status preventing already processed tags from further processing.
	  This irrational behaviour prevents reading of custom tags with id code 0 - like tag GPSVERSIONID from EXIF 2.31 definition.

	  An additional field 'tdir_ignore' is now added to the TIFFDirEntry structure and code is changed
	  to allow tags with id code 0 to be read correctly.

	  This change was already proposed as pending improvement in tif_dirread.c around line 32.

	    Reference is also made to:
		- Discussion in https://gitlab.com/libtiff/libtiff/merge_requests/39
		- http://bugzilla.maptools.org/show_bug.cgi?id=2540

	Comments and indention adapted.

	Preparation to rebase onto master

2019-07-16  Even Rouault  <<EMAIL>>

	Merge branch 'cmake_amd64' into 'master'
	CMakeLists.txt: properly set value of HOST_FILLORDER to LSB2MSB for Windows CMake builds

	See merge request libtiff/libtiff!87

2019-07-15  Even Rouault  <<EMAIL>>

	CMakeLists.txt: properly set value of HOST_FILLORDER to LSB2MSB for Windows CMake builds
	As can be seen in https://ci.appveyor.com/project/rleigh-codelibre/libtiff-didfs/builds/25846668/job/ory5w098j8wcij9x
	log, the HOST_FILLORDER is not properly set:

	[00:02:58] -- CMAKE_HOST_SYSTEM_PROCESSOR set to AMD64
	[00:02:58] -- HOST_FILLORDER set to FILLORDER_MSB2LSB

	Ther reason is that we match the "amd64.*" lowercase string whereas
	CMAKE_HOST_SYSTEM_PROCESSOR is set to AMD64 uppercase.

2019-07-09  Even Rouault  <<EMAIL>>

	TIFFWriteCheck(): call TIFFForceStrileArrayWriting() when needed (should have gone with eaeca6274ae71cdfaeb9f673b6fb0f3cfc0e6ce5) (master only)

2019-07-09  Even Rouault  <<EMAIL>>

	Merge branch 'fix_chromium_925269' into 'master'
	OJPEG: avoid use of unintialized memory on corrupted files

	See merge request libtiff/libtiff!86

2019-07-05  Even Rouault  <<EMAIL>>

	OJPEG: avoid use of unintialized memory on corrupted files.
	Fixes https://bugs.chromium.org/p/chromium/issues/detail?id=925269
	Patch from Lei Zhang with little adaptations.

2019-06-29  Even Rouault  <<EMAIL>>

	Merge branch 'fix-division-by-zero' into 'master'
	Return infinite distance when denominator is zero.

	See merge request libtiff/libtiff!85

2019-06-29  Dirk Lemstra  <<EMAIL>>

	Return infinite distance when denominator is zero.

2019-06-29  Even Rouault  <<EMAIL>>

	Merge branch 'typetests' into 'master'
	Add test to check that libtiff types have the correct size

	See merge request libtiff/libtiff!57

2019-05-31  Thomas Bernard  <<EMAIL>>

	make TIFF_SSIZE_T the same bitwidth as TIFF_SIZE_T.
	it was previously the same bitwidth as unsigned char *
	Pointers can be larger than size_t.

2019-05-31  Thomas Bernard  <<EMAIL>>

	Add test to check that libtiff types have the correct size.
	in configure/CMakeList.txt :

	- TIFF_INT8_T/TIFF_UINT8_T is signed/unsigned char
	sizeof(char)==1 in C standard
	- TIFF_INT16_T/TIFF_UINT16_T is signed/unsigned short
	sizeof(short)>=2 in C standard
	- TIFF_INT32_T/TIFF_UINT32_T is defined so its sizeof() is 4

	- TIFF_INT64_T/TIFF_UINT64_T is defined so its sizeof() is 8

	- TIFF_SIZE_T is defined so it has same sizeof() than size_t

	- TIFF_SSIZE_T is defined so it has same sizeof() than unsigned char *

2019-05-29  Even Rouault  <<EMAIL>>

	Merge branch 'defer_strile_writing' into 'master'
	Add TIFFDeferStrileArrayWriting() and TIFFForceStrileArrayWriting()

	See merge request libtiff/libtiff!82

2019-05-29  Even Rouault  <<EMAIL>>

	Merge branch 'TIFFReadFromUserBuffer' into 'master'
	Add TIFFReadFromUserBuffer()

	See merge request libtiff/libtiff!81

2019-05-26  Even Rouault  <<EMAIL>>

	Fix vulnerability in 'D' (DeferStrileLoad) mode (master only) (fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=14908)

2019-05-25  Even Rouault  <<EMAIL>>

	Replace 'stripped' by 'striped' in error messages.

2019-05-25  Even Rouault  <<EMAIL>>

	Add TIFFDeferStrileArrayWriting() and TIFFForceStrileArrayWriting()
	Those advanced writing functions must be used in a particular sequence
	to make their intended effect. Their aim is to control when/where
	the [Strip/Tile][Offsets/ByteCounts] arrays are written into the file.

	The purpose of this is to generate 'cloud-optimized geotiff' files where
	the first KB of the file only contain the IFD entries without the potentially
	large strile arrays. Those are written afterwards.

	The typical sequence of calls is:
	TIFFOpen()
	[ TIFFCreateDirectory(tif) ]
	Set fields with calls to TIFFSetField(tif, ...)
	TIFFDeferStrileArrayWriting(tif)
	TIFFWriteCheck(tif, ...)
	 TIFFWriteDirectory(tif)
	 ... potentially create other directories and come back to the above directory
	TIFFForceStrileArrayWriting(tif): emit the arrays at the end of file

	See test/defer_strile_writing.c for a practical example.

2019-05-24  Even Rouault  <<EMAIL>>

	Fix vulnerability introduced by defer strile loading (master only)
	Found on GDAL with https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=14894
	Disabling the TIFF_DEFERSTRILELOAD bit in ChopupStripArray() was a
	bad idea since when using TIFFReadDirectory() to reload the directory again
	would lead to a different value of td_rowsperstrip, which could confuse
	readers if they relied on the value found initially.

	Fix typo in error message (master only)

2019-05-22  Even Rouault  <<EMAIL>>

	Add TIFFReadFromUserBuffer()
	This function replaces the use of TIFFReadEncodedStrip()/TIFFReadEncodedTile()
	when the user can provide the buffer for the input data, for example when
	he wants to avoid libtiff to read the strile offset/count values from the
	[Strip|Tile][Offsets/ByteCounts] array.

	libtiff.def: add missing new symbols.

	test/defer_strile_loading.c: fix warning with Visual C++

	_TIFFRewriteField(): fix for bigtiff case (master only)
	116cf67f4c59196605abdb244657c3070c4310af made StripByteCount/TileByteCount to
	always be rewritten as TIFF_LONG8.

2019-05-21  Even Rouault  <<EMAIL>>

	Merge branch 'ondemand_strile_offbytecount_loading' into 'master'
	Make defer strile offset/bytecount loading available at runtime

	See merge request libtiff/libtiff!79

2019-05-21  Even Rouault  <<EMAIL>>

	Merge branch 'bigtiff_write_bytecount_on_long_when_possible' into 'master'
	Create TileByteCounts/StripByteCounts tag with SHORT (ClassicTIFF/BigTIFF) or  LONG (BigTIFF) type when possible

	See merge request libtiff/libtiff!78

2019-05-21  Even Rouault  <<EMAIL>>

	Merge branch 'html_link' into 'master'
	libtiff.html, bigtiffpr.html: absolute => relative link

	See merge request libtiff/libtiff!80

2019-05-14  Thomas Bernard  <<EMAIL>>

	libtiff.html, bigtiffpr.html: absolute => relative link.

2019-05-10  Even Rouault  <<EMAIL>>

	Make defer strile offset/bytecount loading available at runtime.
	... and add per-strile offset/bytecount loading capabilities.

	Part of this commit makes the behaviour that was previously met when
	libtiff was compiled with -DDEFER_STRILE_LOAD available for default builds
	when specifying the new 'D' (Deferred) TIFFOpen() flag. In that mode, the [Tile/Strip][ByteCounts/Offsets]
	arrays are only loaded when first accessed. This can speed-up the opening
	of files stored on the network when just metadata retrieval is needed.
	This mode has been used for years by the GDAL library when compiled with
	its embeded libtiff copy.

	To avoid potential out-of-tree code (typically codecs) that would use
	the td_stripbytecount and td_stripoffset array inconditionnaly assuming they
	have been loaded, those have been suffixed with _p (for protected). The
	use of the new functions mentionned below is then recommended.

	Another addition of this commit is the capability of loading only the
	values of the offset/bytecount of the strile of interest instead of the
	whole array. This is enabled with the new 'O' (Ondemand) flag of TIFFOpen()
	(which implies 'D'). That behaviour has also been used by GDAL, which hacked
	into the td_stripoffset/td_stripbytecount arrays directly. The new code
	added in the _TIFFFetchStrileValue() and _TIFFPartialReadStripArray() internal
	functions is mostly a port of what was in GDAL GTiff driver previously.

	Related to that, the public TIFFGetStrileOffset[WithErr]() and TIFFGetStrileByteCount[WithErr]()
	functions have been added to API. They are of particular interest when
	using sparse files (with offset == bytecount == 0) and you want to detect
	if a strile is present or not without decompressing the data, or updating
	an existing sparse file.
	They will also be used to enable a future enhancement where client code can entirely
	skip bytecount loading in some situtations

	A new test/defer_strile_loading.c test has been added to test the above
	capabilities.

2019-05-10  Even Rouault  <<EMAIL>>

	Creation: use SHORT type when possible for StripByteCounts/TileByteCounts
	This follows the same logic as previous commit.

2019-05-09  Even Rouault  <<EMAIL>>

	BigTIFF creation: write TileByteCounts/StripByteCounts tag with LONG when possible
	In most situations of BigTIFF file, the tile/strip sizes are of reasonable size,
	that is they fit on a 4-byte LONG. So in that case, use LONG instead of LONG8
	to save some space. For uncompressed file, it is easy to detect such situations
	by checking at the TIFFTileSize64()/TIFFStripSize64() return. For compressed file,
	we must take into account the fact that compression may sometimes result in
	larger compressed data. So we allow this optimization only for a few select
	compression times, and take a huge security margin (10x factor). We also only
	apply this optimization on multi-strip files, so as to allow easy on-the-fly
	growing of single-strip files whose strip size could grow above the 4GB threshold.

	This change is compatible with the BigTIFF specification. According to
	https://www.awaresystems.be/imaging/tiff/bigtiff.html:
	"The StripOffsets, StripByteCounts, TileOffsets, and TileByteCounts tags are
	allowed to have the datatype TIFF_LONG8 in BigTIFF. Old datatypes TIFF_LONG,
	and TIFF_SHORT where allowed in the TIFF 6.0 specification, are still valid in BigTIFF, too. "
	On a practical point of view, this is also compatible on reading/writing of
	older libtiff 4.X versions.

	The only glitch I found, which is rather minor, is when using such a BigTIFF
	file with TileByteCounts/StripByteCounts written with TIFF_LONG, and updating
	it with an older libtiff 4.X version with a change in the
	[Tile/Strip][ByteCounts/Offsets] array. In that case the _TIFFRewriteField()
	function will rewrite the directory and array with TIFF_LONG8, instead of updating
	the existing array (this is an issue fixed by this commit). The file will
	still be valid however, hence the minor severity of this.

2019-05-08  Even Rouault  <<EMAIL>>

	Merge branch 'bug2799' into 'master'
	fix fax2tiff

	See merge request libtiff/libtiff!55

2019-05-08  Even Rouault  <<EMAIL>>

	Merge branch 'bug_2829' into 'master'
	WIN32: use tif_win32.c when building with CMake

	See merge request libtiff/libtiff!75

2019-05-06  Even Rouault  <<EMAIL>>

	Merge branch 'FILESOURCE_SCENETYPE_reading' into 'master'
	Reading error for FileSource and SceneType tags corrected.

	See merge request libtiff/libtiff!76

2019-05-06  Su Laus  <<EMAIL>>

	Reading error for FileSource and SceneType tags corrected.
	EXIF tags FILESOURCE and SCENETYPE are defined as TIFF_UNDEFINED and field_readcount==1!
	There is a bug in TIFFReadDirEntryByte() preventing to read correctly type TIFF_UNDEFINED fields with field_readcount==1
	Upgrade of TIFFReadDirEntryByte() with added TIFF_UNDEFINED switch-entry allows libtiff to read those tags correctly.

2019-04-25  Thomas Bernard  <<EMAIL>>

	WIN32: use tif_win32.c when building with CMake.
	see http://bugzilla.maptools.org/show_bug.cgi?id=2829

	the top CMakeLists.txt defines
	win32_io and USE_WIN32_FILEIO

	WIN32_IO is defined nowhere in CMake (only in automake things)

2019-04-25  Even Rouault  <<EMAIL>>

	Merge branch 'gitlab_pages' into 'master'
	Advertise https://libtiff.gitlab.io/libtiff/ as mirror

	See merge request libtiff/libtiff!70

2019-04-25  Even Rouault  <<EMAIL>>

	Merge branch 'bug_2844' into 'master'
	tiff2ps.c: PSDataColorContig(): avoid heap buffer overrun

	See merge request libtiff/libtiff!69

2019-04-25  Even Rouault  <<EMAIL>>

	Merge branch 'issue_2785' into 'master'
	tiff2pdf.c: don't call t2p_tile_collapse_left() for Ycbcr

	See merge request libtiff/libtiff!64

2019-04-11  Even Rouault  <<EMAIL>>

	Merge branch 'fix_gdal_1439' into 'master'
	TIFFWriteEncodedStrip/TIFFWriteEncodedTile: fix rewriting of LZW-compressed data

	See merge request libtiff/libtiff!74

2019-04-11  Even Rouault  <<EMAIL>>

	TIFFWriteEncodedStrip/TIFFWriteEncodedTile: fix rewriting of LZW-compressed data
	Fixes https://github.com/OSGeo/gdal/issues/1439

	When rewriting a LZW tile/strip whose existing size is very close to a multiple of
	1024 bytes (and larger than 8192 bytes) with compressed data that is larger,
	the new data was not placed at the end of the file, causing corruption.

2019-04-08  Even Rouault  <<EMAIL>>

	Merge branch 'bug2848' into 'master'
	tif_luv.c: LogLuvSetupEncode() error must return 0

	See merge request libtiff/libtiff!72

2019-04-03  Thomas Bernard  <<EMAIL>>

	build/gitlab-ci: fix typo.

	show test-suite.log in gitlab-ci.
	useful when build fails

	Add output check for tiff2ps.
	note : the reference files have been generated in master branch

2019-03-23  Even Rouault  <<EMAIL>>

	tif_read.c: potentially fix false positive from Coverity Scan. CID 1400288

	tif_read.c: potentially fix false positive from Coverity Scan. CID 1400271

	tif_zip.c: remove dead code. CID 1400360.

	tif_webp.c: remove false positive warning about dereference before null check. CID 1400255

	tif_pixarlog.c: remove dead code. CID 1400342.

	tif_pixarlog.c: avoid false positive Coverity Scan warnings about overflow. CID 1400300 and 1400367

	tif_lzw.c: silence CoverityScan false positive. CID 1400355.

	tif_luv.c: silence CoverityScan false positive. CID 1400231, 1400251, 1400254, 1400272, 1400318, 1400356

	TryChopUpUncompressedBigTiff(): avoid potential division by zero. master only. GDAL Coverity CID 1400263

2019-03-22  Thomas Bernard  <<EMAIL>>

	tif_luv.c: LogLuvSetupEncode() error must return 0.
	see http://bugzilla.maptools.org/show_bug.cgi?id=2848

	if wrongly returning 1, the processing of incorrect file continues,
	which causes problems.

2019-03-22  Thomas Bernard  <<EMAIL>>

	add a test for fax2tiff tool.

2019-02-28  Thomas Bernard  <<EMAIL>>

	tiff2pdf.c: don't call t2p_tile_collapse_left() when buffer size is wrong
	see http://bugzilla.maptools.org/show_bug.cgi?id=2785

	Advertise https://libtiff.gitlab.io/libtiff/ as mirror.
	I'm put it above the maptools.org mirror because
	Even Rouault believe at some point it will be completely removed

2019-02-28  Even Rouault  <<EMAIL>>

	Merge branch 'bug_2826' into 'master'
	tiff2pdf.c: check colormap pointers when loading CMYK with colormap

	See merge request libtiff/libtiff!65

2019-02-28  Thomas Bernard  <<EMAIL>>

	tiff2pdf.c: check colormap pointers.
	Avoid access to non initialized pointers
	http://bugzilla.maptools.org/show_bug.cgi?id=2826

2019-02-27  Even Rouault  <<EMAIL>>

	Merge branch 'fix_warnings' into 'master'
	tiff2ps.c: fix warning caused by integer promotion

	See merge request libtiff/libtiff!68

2019-02-23  Thomas Bernard  <<EMAIL>>

	PSDataColorContig(): avoid heap buffer overrun.
	fixes http://bugzilla.maptools.org/show_bug.cgi?id=2844
	each iteration of the loop read nc bytes

2019-02-22  Thomas Bernard  <<EMAIL>>

	tiff2ps.c: fix warning caused by integer promotion.
	uint8 value is promoted to int in (value << 24) so -fsanitize
	yield runtime errors :
	tiff2ps.c:2969:33: runtime error: left shift of 246 by 24 places cannot be represented in type 'int'

2019-02-22  Even Rouault  <<EMAIL>>

	Merge branch 'large_strile_improvements' into 'master'
	Large strile support improvements

	See merge request libtiff/libtiff!63

2019-02-21  Even Rouault  <<EMAIL>>

	Merge branch 'gitlab-pages' into 'master'
	ci: Add pages job

	See merge request libtiff/libtiff!45

2019-02-19  Even Rouault  <<EMAIL>>

	Merge branch 'issue_2833' into 'master'
	tiffcp.c: check that (Tile Width)*(Samples/Pixel) do no overflow

	See merge request libtiff/libtiff!60

2019-02-19  Even Rouault  <<EMAIL>>

	Merge branch 'issue_2831' into 'master'
	tiffcrop.c: fix invertImage() for bps 2 and 4

	See merge request libtiff/libtiff!61

2019-02-19  Even Rouault  <<EMAIL>>

	Merge branch 'issue_2842' into 'master'
	move _TIFFClampDoubleToFloat() to tif_aux.c

	See merge request libtiff/libtiff!62

2019-02-19  Even Rouault  <<EMAIL>>

	tif_zip.c: allow reading and writing strips/tiles with more than 4 GB of compressed or uncompressed data

	tif_dirread.c: when strip chopping is enabled, extend this mechanism to multi-strip uncompressed files with strips larger than 2GB to expose them as strips of ~500 MB

2019-02-19  Even Rouault  <<EMAIL>>

	Merge branch 'size_t_typo' into 'master'
	CMakeLists.txt: fix TIFF_SIZE_T

	See merge request libtiff/libtiff!59

2019-02-12  Thomas Bernard  <<EMAIL>>

	move _TIFFClampDoubleToFloat() to tif_aux.c.
	the same function was declared in tif_dir.c and tif_dirwrite.c

	see http://bugzilla.maptools.org/show_bug.cgi?id=2842

2019-02-11  Thomas Bernard  <<EMAIL>>

	tiffcrop.c: fix invertImage() for bps 2 and 4.
	too much bytes were processed, causing a heap buffer overrun
	    http://bugzilla.maptools.org/show_bug.cgi?id=2831
	the loop counter must be
	    for (col = 0; col < width; col += 8 / bps)

	Also the values were not properly calculated. It should be
	255-x, 15-x, 3-x for bps 8, 4, 2.

	But anyway it is easyer to invert all bits as 255-x = ~x, etc.
	(substracting from a binary number composed of all 1 is like inverting
	the bits)

2019-02-11  Thomas Bernard  <<EMAIL>>

	tiffcp.c: use INT_MAX.

	check that (Tile Width)*(Samples/Pixel) do no overflow.
	fixes bug 2833

2019-02-03  Thomas Bernard  <<EMAIL>>

	CMakeLists.txt: fix TIFF_SIZE_T.

2019-02-02  Even Rouault  <<EMAIL>>

	Merge branch 'master' into 'master'
	Fix for simple memory leak that was assigned CVE-2019-6128.

	See merge request libtiff/libtiff!50

2019-02-02  Even Rouault  <<EMAIL>>

	Merge branch 'bug2835' into 'master'
	tiff2ps: fix heap-buffer-overflow

	See merge request libtiff/libtiff!53

2019-02-02  Even Rouault  <<EMAIL>>

	Fix warning (use of uninitialized value) added per d0a842c5dbad2609aed43c701a12ed12461d3405 (fixes https://gitlab.com/libtiff/libtiff/merge_requests/54#note_137742985)

2019-02-02  Yuri Aksenov  <<EMAIL>>

	fix fax2tiff.
	see http://bugzilla.maptools.org/show_bug.cgi?id=2799
	fixes d9bc8472e72549f29c0062c1cbd3d56f279f3be2

2019-02-02  Even Rouault  <<EMAIL>>

	Merge branch 'tiffcrop' into 'master'
	tiffcrop: shut up clang warnings

	See merge request libtiff/libtiff!52

2019-02-01  Even Rouault  <<EMAIL>>

	Merge branch 'bug2833' into 'master'
	TIFFWriteDirectoryTagTransferfunction() : fix NULL dereferencing

	See merge request libtiff/libtiff!54

2019-02-01  Even Rouault  <<EMAIL>>

	Merge branch 'gitignore' into 'master'
	add test/ files to .gitignore

	See merge request libtiff/libtiff!56

2019-02-01  Even Rouault  <<EMAIL>>

	Merge branch 'master' into 'master'
	tif_dir: unset transferfunction field if necessary (CVE-2018-19210)

	See merge request libtiff/libtiff!47

2019-01-29  Thomas Bernard  <<EMAIL>>

	add test/ files to .gitignore.

2019-01-29  Thomas Bernard  <<EMAIL>>

	TIFFWriteDirectoryTagTransferfunction() : fix NULL dereferencing.
	http://bugzilla.maptools.org/show_bug.cgi?id=2833

	we must check the pointer is not NULL before memcmp() the memory

2019-01-29  Thomas Bernard  <<EMAIL>>

	tiff2ps: fix heap-buffer-overflow.
	http://bugzilla.maptools.org/show_bug.cgi?id=2834

	usually the test (i < byte_count) is OK because the byte_count is divisible by samplesperpixel.
	But if that is not the case, (i + ncomps) < byte_count should be used, or
	maybe (i + samplesperpixel) <= byte_count

2019-01-28  Thomas Bernard  <<EMAIL>>

	tiffcrop: shut up clang warnings.
	make the out filename building a bit more simple
	and remove the use of strcat()

2019-01-23  Scott Gayou  <<EMAIL>>

	Fix for simple memory leak that was assigned CVE-2019-6128.
	pal2rgb failed to free memory on a few errors. This was reported
	here: http://bugzilla.maptools.org/show_bug.cgi?id=2836.

2019-01-05  Bob Friesenhahn  <<EMAIL>>

	Fix tiff2ps error regarding "Inconsistent value of es" by allowing es to be zero. Problem was reported to the tiff mailing list by Julian H. Stacey on January 5, 2019.

2018-12-13  Hugo Lefeuvre  <<EMAIL>>

	tif_dir: unset transferfunction field if necessary.
	The number of entries in the transfer table is determined as following:

	(td->td_samplesperpixel - td->td_extrasamples) > 1 ? 3 : 1

	This means that whenever td->td_samplesperpixel or td->td_extrasamples are
	modified we also need to make sure that the number of required entries in
	the transfer table didn't change.

	If it changed and the number of entries is higher than before we should
	invalidate the transfer table field and free previously allocated values.
	In the other case there's nothing to do, additional tf entries won't harm
	and properly written code will just ignore them since spp - es < 1.

	For instance this situation might happen when reading an OJPEG compressed
	image with missing SamplesPerPixel tag. In this case the SamplesPerPixel
	field might be updated after setting the transfer table.

	see http://bugzilla.maptools.org/show_bug.cgi?id=2500

	This commit addresses CVE-2018-19210.

2018-12-08  Bob Friesenhahn  <<EMAIL>>

	Do not attempt to re-sync zip stream after reported data error from inflate().

2018-12-07  Even Rouault  <<EMAIL>>

	Merge branch 'resource-leaks' into 'master'
	Fix two resource leaks

	See merge request libtiff/libtiff!43

2018-12-07  Even Rouault  <<EMAIL>>

	Merge branch 'build-jbig' into 'master'
	add jbig support to the fuzzer

	See merge request libtiff/libtiff!42

2018-12-01  Bob Friesenhahn  <<EMAIL>>

	tiffcrop.c: Avoid new clang warning about tools/tiffcrop.c "size argument in 'strncat' call appears to be size of the source".

2018-11-28  Even Rouault  <<EMAIL>>

	Merge branch 'webp_memleak' into 'master'
	fixed mem leak in webp compression

	See merge request libtiff/libtiff!48

2018-11-28  Norman Barker  <<EMAIL>>

	fixed mem leak in webp compression.

2018-11-20  Even Rouault  <<EMAIL>>

	Merge branch 'lossless_webp' into 'master'
	fixed lossless webp compression config

	See merge request libtiff/libtiff!46

2018-11-20  Norman Barker  <<EMAIL>>

	fixed lossless webp compression config.

2018-11-18  Bob Friesenhahn  <<EMAIL>>

	snprintf porting fix for Visual Studio 2003.

2018-11-18  Roger Leigh  <<EMAIL>>

	ci: Add pages job.

2018-11-10  Bob Friesenhahn  <<EMAIL>>

	Change references from defunct ftp site to https site.

2018-11-10  Bob Friesenhahn  <<EMAIL>>

	* configure.ac: libtiff 4.0.10 released.

	Change COMPRESSION_ZSTD to 50000 and COMPRESSION_WEBP to 50001.

2018-11-04  Bob Friesenhahn  <<EMAIL>>

	Added preliminary release notes for release 4.0.10.

2018-11-03  Bob Friesenhahn  <<EMAIL>>

	tiff2pdf: Eliminate compiler warning about snprintf output truncation when formatting pdf_datetime.

2018-11-03  Olivier Paquet  <<EMAIL>>

	Merge branch 'no_tif_platform_console' into 'master'
	Remove builtin support for GUI warning and error message boxes

	See merge request libtiff/libtiff!24

2018-11-03  Bob Friesenhahn  <<EMAIL>>

	tiffcrop.c: Eliminate compiler warning about snprintf output truncation when formatting filenum.

	TWebPVGetField(): Add apparently missing break statement impacting TIFFTAG_WEBP_LOSSLESS.

	Eliminate compiler warnings about duplicate definitions of streq/strneq macros.

	Ignore generated files.

	Remove and ignore files which are a product of autogen.sh.

2018-11-02  Bob Friesenhahn  <<EMAIL>>

	Fix TIFFErrorExt() formatting of size_t type for 32-bit compiles.

2018-10-30  Even Rouault  <<EMAIL>>

	tiff2bw: avoid null pointer dereference in case of out of memory situation. Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2819 / CVE-2018-18661

	tiffio.h: fix comment.

2018-10-26  Even Rouault  <<EMAIL>>

	Merge branch 'header2' into 'master'
	Fix 725279bd: Standalone tif_predict.h: tiff.h should be tiffiop.h

	See merge request libtiff/libtiff!41

2018-10-26  Kurt Schwehr  <<EMAIL>>

	Fix 725279bd: Standalone tif_predict.h: tiff.h should be tiffiop.h.

2018-10-25  Even Rouault  <<EMAIL>>

	Merge branch 'headers' into 'master'
	Add includes to headers to allow them to stand alone.

	See merge request libtiff/libtiff!40

2018-10-24  Kurt Schwehr  <<EMAIL>>

	Add includes to headers to allow them to stand alone.
	This allows compilers that can do header stand alone header parsing
	to process libtiff.

2018-10-18  Even Rouault  <<EMAIL>>

	LZMAPreEncode: emit verbose error if lzma_stream_encoder() fails (typically because not enough memory available)

2018-10-17  Even Rouault  <<EMAIL>>

	tif_webp.c: fix previous commit that broke scanline decoding.

	tif_webp.c: fix potential read outside libwebp buffer on corrupted images

2018-10-14  Even Rouault  <<EMAIL>>

	Merge branch 'jbig_decode_overflow' into 'master'
	JBIG: fix potential out-of-bounds write in JBIGDecode()

	See merge request libtiff/libtiff!38

2018-10-14  Even Rouault  <<EMAIL>>

	JBIG: fix potential out-of-bounds write in JBIGDecode()
	JBIGDecode doesn't check if the user provided buffer is large enough
	to store the JBIG decoded image, which can potentially cause out-of-bounds
	write in the buffer.
	This issue was reported and analyzed by Thomas Dullien.

	Also fixes a (harmless) potential use of uninitialized memory when
	tif->tif_rawsize > tif->tif_rawcc

	And in case libtiff is compiled with CHUNKY_STRIP_READ_SUPPORT, make sure
	that whole strip data is provided to JBIGDecode()

2018-10-05  Even Rouault  <<EMAIL>>

	tif_webp.c: fix scanline reading/writing.

	WEBP codec: initialize nSamples in TWebPSetupDecode() and TWebPSetupEncode()

2018-10-05  Even Rouault  <<EMAIL>>

	Merge branch 'tif_webp' into 'master'
	webp support

	See merge request libtiff/libtiff!32

2018-10-05  Norman Barker  <<EMAIL>>

	webp in tiff.

2018-09-17  Even Rouault  <<EMAIL>>

	Merge branch 'master' into 'master'
	fix three potential vulnerabilities.

	See merge request libtiff/libtiff!33

2018-09-08  Young_X  <<EMAIL>>

	fix out-of-bound read on some tiled images.

	avoid potential int32 overflows in multiply_ms()

	only read/write TIFFTAG_GROUP3OPTIONS or TIFFTAG_GROUP4OPTIONS if compression is COMPRESSION_CCITTFAX3 or COMPRESSION_CCITTFAX4

2018-08-15  Even Rouault  <<EMAIL>>

	TIFFSetupStrips(): avoid potential uint32 overflow on 32-bit systems with large number of strips. Probably relates to http://bugzilla.maptools.org/show_bug.cgi?id=2788 / CVE-2018-10779

2018-08-07  Even Rouault  <<EMAIL>>

	ZSTD: fix flush issue that can cause endless loop in ZSTDEncode()
	Fixes https://github.com/OSGeo/gdal/issues/833

2018-08-07  Even Rouault  <<EMAIL>>

	Merge branch 'fix_bug_2800' into 'master'
	Fix libtiff 4.0.8 regression when reading LZW-compressed strips with scanline API

	See merge request libtiff/libtiff!31

2018-08-07  Even Rouault  <<EMAIL>>

	Fix libtiff 4.0.8 regression when reading LZW-compressed strips with scanline API
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2800

2018-07-05  Even Rouault  <<EMAIL>>

	Add tag and pseudo-tag definitions for ESRI LERC codec (out of tree codec whose source is at https://github.com/OSGeo/gdal/blob/master/gdal/frmts/gtiff/tif_lerc.c)

2018-07-02  Even Rouault  <<EMAIL>>

	Fix TIFFTAG_ZSTD_LEVEL pseudo tag value to be > 65536, and the next one in the series

2018-05-25  Stefan Weil  <<EMAIL>>

	Remove builtin support for GUI warning and error message boxes.
	Now warnings always go to the console by default unless applications
	define their own warning and error handlers.

	GUI applications (and Windows CE) are required to define such handlers.

2018-05-12  Even Rouault  <<EMAIL>>

	LZWDecodeCompat(): fix potential index-out-of-bounds write. Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2780 / CVE-2018-8905
	The fix consists in using the similar code LZWDecode() to validate we
	don't write outside of the output buffer.

	TIFFFetchNormalTag(): avoid (probably false positive) clang-tidy clang-analyzer-core.NullDereference warnings

	TIFFWriteDirectorySec: avoid assertion. Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2795. CVE-2018-10963

2018-05-04  Even Rouault  <<EMAIL>>

	tif_color.c: fix code comment.

2018-04-17  Even Rouault  <<EMAIL>>

	Merge branch 'fuzzer-fix' into 'master'
	remove a pointless multiplication and a variable that's not necessary

	See merge request libtiff/libtiff!29

2018-04-17  Paul Kehrer  <<EMAIL>>

	remove a pointless multiplication and a variable that's not necessary.

2018-04-17  Even Rouault  <<EMAIL>>

	Merge branch 'ossfuzz' into 'master'
	move oss-fuzz build script and fuzzer into libtiff tree

	See merge request libtiff/libtiff!28

2018-04-17  Paul Kehrer  <<EMAIL>>

	move oss-fuzz build script and fuzzer into libtiff tree.

2018-04-14  Even Rouault  <<EMAIL>>

	_TIFFGetMaxColorChannels: update for LOGLUV, ITULAB and ICCLAB that have 3 color channels

2018-04-12  Even Rouault  <<EMAIL>>

	Fix MSVC warning.

2018-04-12  Even Rouault  <<EMAIL>>

	Merge branch 'master' into 'master'
	Fix NULL pointer dereference in TIFFPrintDirectory (bugzilla 2778/CVE-2018-7456)

	See merge request libtiff/libtiff!27

2018-04-11  Hugo Lefeuvre  <<EMAIL>>

	Fix NULL pointer dereference in TIFFPrintDirectory.
	The TIFFPrintDirectory function relies on the following assumptions,
	supposed to be guaranteed by the specification:

	(a) A Transfer Function field is only present if the TIFF file has
	    photometric type < 3.

	(b) If SamplesPerPixel > Color Channels, then the ExtraSamples field
	    has count SamplesPerPixel - (Color Channels) and contains
	    information about supplementary channels.

	While respect of (a) and (b) are essential for the well functioning of
	TIFFPrintDirectory, no checks are realized neither by the callee nor
	by TIFFPrintDirectory itself. Hence, following scenarios might happen
	and trigger the NULL pointer dereference:

	(1) TIFF File of photometric type 4 or more has illegal Transfer
	    Function field.

	(2) TIFF File has photometric type 3 or less and defines a
	    SamplesPerPixel field such that SamplesPerPixel > Color Channels
	    without defining all extra samples in the ExtraSamples fields.

	In this patch, we address both issues with respect of the following
	principles:

	(A) In the case of (1), the defined transfer table should be printed
	    safely even if it isn't 'legal'. This allows us to avoid expensive
	    checks in TIFFPrintDirectory. Also, it is quite possible that
	    an alternative photometric type would be developed (not part of the
	    standard) and would allow definition of Transfer Table. We want
	    libtiff to be able to handle this scenario out of the box.

	(B) In the case of (2), the transfer table should be printed at its
	    right size, that is if TIFF file has photometric type Palette
	    then the transfer table should have one row and not three, even
	    if two extra samples are declared.

	In order to fulfill (A) we simply add a new 'i < 3' end condition to
	the broken TIFFPrintDirectory loop. This makes sure that in any case
	where (b) would be respected but not (a), everything stays fine.

	(B) is fulfilled by the loop condition
	'i < td->td_samplesperpixel - td->td_extrasamples'. This is enough as
	long as (b) is respected.

	Naturally, we also make sure (b) is respected. This is done in the
	TIFFReadDirectory function by making sure any non-color channel is
	counted in ExtraSamples.

	This commit addresses CVE-2018-7456.

2018-03-27  Even Rouault  <<EMAIL>>

	Merge branch 'tiffset-long8' into 'master'
	tiffset: Add support for LONG8, SLONG8 and IFD8 field types

	See merge request libtiff/libtiff!25

2018-03-26  Roger Leigh  <<EMAIL>>

	port: Clean up NetBSD sources and headers to build standalone.

2018-03-23  Roger Leigh  <<EMAIL>>

	port: Add strtol, strtoll and strtoull.
	Also update strtoul.  All use the same implementation from NetBSD libc.

	tiffset: Add support for LONG8, SLONG8 and IFD8 field types.

2018-03-17  Even Rouault  <<EMAIL>>

	ChopUpSingleUncompressedStrip: avoid memory exhaustion (CVE-2017-11613)
	Rework fix done in 3719385a3fac5cfb20b487619a5f08abbf967cf8 to work in more
	cases like https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=6979.
	Credit to OSS Fuzz

	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2724

2018-03-13  Even Rouault  <<EMAIL>>

	libtiff/tif_luv.c: rewrite loops in a more readable way (to avoid false positive reports like http://bugzilla.maptools.org/show_bug.cgi?id=2779)

2018-03-13  Even Rouault  <<EMAIL>>

	Merge branch 'avoid_memory_exhaustion_in_ChopUpSingleUncompressedStrip' into 'master'
	ChopUpSingleUncompressedStrip: avoid memory exhaustion (CVE-2017-11613)

	See merge request libtiff/libtiff!26

2018-03-11  Even Rouault  <<EMAIL>>

	ChopUpSingleUncompressedStrip: avoid memory exhaustion (CVE-2017-11613)
	In ChopUpSingleUncompressedStrip(), if the computed number of strips is big
	enough and we are in read only mode, validate that the file size is consistent
	with that number of strips to avoid useless attempts at allocating a lot of
	memory for the td_stripbytecount and td_stripoffset arrays.

	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2724

2018-03-10  Even Rouault  <<EMAIL>>

	Typo fix in comment.

2018-03-03  Even Rouault  <<EMAIL>>

	Avoid warning with gcc 8 (partially revert 647b0e8c11ee11896f319b92cf110775f538d75c)

2018-02-25  Even Rouault  <<EMAIL>>

	Merge branch 'typos' into 'master'
	Fix some typos

	See merge request libtiff/libtiff!23

2018-02-24  Stefan Weil  <<EMAIL>>

	Fix some typos.
	Most of them were found by codespell.

2018-02-14  Even Rouault  <<EMAIL>>

	Typo fix in comment.

	Merge branch 'zstd'

	Add warning about COMPRESSION_ZSTD not being officialy registered.

2018-02-14  Even Rouault  <<EMAIL>>

	Merge branch 'bug2772' into 'master'
	Fix for bug 2772

	See merge request libtiff/libtiff!20

2018-02-12  Nathan Baker  <<EMAIL>>

	Fix for bug 2772.
	It is possible to craft a TIFF document where the IFD list is circular,
	leading to an infinite loop while traversing the chain. The libtiff
	directory reader has a failsafe that will break out of this loop after
	reading 65535 directory entries, but it will continue processing,
	consuming time and resources to process what is essentially a bogus TIFF
	document.

	This change fixes the above behavior by breaking out of processing when
	a TIFF document has >= 65535 directories and terminating with an error.

2018-02-09  Even Rouault  <<EMAIL>>

	Merge branch 'libtiff-as-subdirectory-fixes' into 'master'
	Prefer target_include_directories

	See merge request libtiff/libtiff!12

2018-02-06  Even Rouault  <<EMAIL>>

	Merge branch 'cmake-cleanups' into 'master'
	Cmake cleanups

	See merge request libtiff/libtiff!11

2018-02-06  Even Rouault  <<EMAIL>>

	Merge branch 'check-right-cxx-variable' into 'master'
	Check right cxx variable

	See merge request libtiff/libtiff!19

2018-02-06  Even Rouault  <<EMAIL>>

	Merge branch 'dont-leak-stream-open' into 'master'
	Fix a memory leak in TIFFStreamOpen

	See merge request libtiff/libtiff!17

2018-02-06  Ben Boeckel  <<EMAIL>>

	cmake: check CXX_SUPPORT.
	This variable is set in response to the `cxx` cache variable; use it
	instead.

2018-02-04  Olivier Paquet  <<EMAIL>>

	Merge branch 'warnings' into 'master'
	Fix all compiler warnings for default build

	See merge request libtiff/libtiff!16

2018-02-04  Nathan Baker  <<EMAIL>>

	Fix all compiler warnings for default build.

2018-01-30  Paul Kehrer  <<EMAIL>>

	tabs are hard.

2018-01-29  Paul Kehrer  <<EMAIL>>

	use hard tabs like the rest of the project.

	Fix a memory leak in TIFFStreamOpen.
	TIFFStreamOpen allocates a new tiff{o,i}s_data, but if TIFFClientOpen
	fails then that struct is leaked. Delete it if the returned TIFF * is
	null.

2018-01-29  Kevin Funk  <<EMAIL>>

	Bump minimum required CMake version to v2.8.11.
	Because we use the BUILD_INTERFACE generator expression

2018-01-27  Even Rouault  <<EMAIL>>

	Merge branch 'patch-1' into 'master'
	Update CMakeLists.txt for build fix on Windows

	See merge request libtiff/libtiff!14

2018-01-27  Even Rouault  <<EMAIL>>

	Merge branch 'patch-2' into 'master'
	Update tiffgt.c for build fix on Windows

	See merge request libtiff/libtiff!13

2018-01-25  Olivier Paquet  <<EMAIL>>

	Merge branch 'bug2750' into 'master'
	Add workaround to pal2rgb buffer overflow.

	See merge request libtiff/libtiff!15

2018-01-25  Nathan Baker  <<EMAIL>>

	Add workaround to pal2rgb buffer overflow.

2018-01-23  Andrea  <<EMAIL>>

	Update tiffgt.c for build fix on Windows.

	Update CMakeLists.txt for build fix on Windows.

2018-01-15  Even Rouault  <<EMAIL>>

	Merge branch 'has-attribute-check' into 'master'
	tiffiop: use __has_attribute to detect the no_sanitize attribute

	See merge request libtiff/libtiff!10

2018-01-15  Ben Boeckel  <<EMAIL>>

	cmake: avoid setting hard-coded variables in the cache.

	cmake: avoid an unnecessary intermediate variable.

	cmake: avoid an unnecessary intermediate variable.

	cmake: avoid tautological logic.

	cmake: use check_symbol_exists.
	This accounts for symbols being provided by macros.

	cmake: remove unused configure checks.

2018-01-12  Kevin Funk  <<EMAIL>>

	Prefer target_include_directories.
	When libtiff is included in a super project via a simple
	`add_subdirectory(libtiff)`, this way the `tiff` library target has all
	the necessary information to build against it.

	Note: The BUILD_INTERFACE generator expression feature requires at least
	CMake v2.8.11 if I'm correct.

2018-01-09  Ben Boeckel  <<EMAIL>>

	tiffiop: use __has_attribute to detect the no_sanitize attribute.

2017-12-31  Even Rouault  <<EMAIL>>

	man/TIFFquery.3tiff: remove reference to non-existing TIFFReadStrip() function in TIFFIsByteSwapped() documentation. Patch by Eric Piel. Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2763

	libtiff/tif_dir.c: _TIFFVGetField(): fix heap out-of-bounds access when requesting TIFFTAG_NUMBEROFINKS on a EXIF directory. Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2765. Reported by Google Autofuzz project

	libtiff/tif_print.c: TIFFPrintDirectory(): fix null pointer dereference on corrupted file. Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2770

2017-12-21  Even Rouault  <<EMAIL>>

	Add libzstd to gitlab-ci.

2017-12-21  Even Rouault  <<EMAIL>>

	Add ZSTD compression codec.
	From https://github.com/facebook/zstd
	"Zstandard, or zstd as short version, is a fast lossless compression
	algorithm, targeting real-time compression scenarios at zlib-level
	and better compression ratios. It's backed by a very fast entropy stage,
	provided by Huff0 and FSE library."

	We require libzstd >= 1.0.0 so as to be able to use streaming compression
	and decompression methods.

	The default compression level we have selected is 9 (range goes from 1 to 22),
	which experimentally offers equivalent or better compression ratio than
	the default deflate/ZIP level of 6, and much faster compression.

	For example on a 6600x4400 16bit image, tiffcp -c zip runs in 10.7 seconds,
	while tiffcp -c zstd runs in 5.3 seconds. Decompression time for zip is
	840 ms, and for zstd 650 ms. File size is 42735936 for zip, and
	42586822 for zstd. Similar findings on other images.

	On a 25894x16701 16bit image,

	                Compression time     Decompression time     File size

	ZSTD                 35 s                   3.2 s          399 700 498
	ZIP/Deflate       1m 20 s                   4.9 s          419 622 336

2017-12-10  Even Rouault  <<EMAIL>>

	Merge branch 'fix_cve-2017-9935' into 'master'
	Fix CVE-2017-9935

	See merge request libtiff/libtiff!7

2017-12-10  Brian May  <<EMAIL>>

	tiff2pdf: Fix apparent incorrect type for transfer table.
	The standard says the transfer table contains unsigned 16 bit values,
	I have no idea why we refer to them as floats.

2017-12-10  Brian May  <<EMAIL>>

	tiff2pdf: Fix CVE-2017-9935.
	Fix for http://bugzilla.maptools.org/show_bug.cgi?id=2704

	This vulnerability - at least for the supplied test case - is because we
	assume that a tiff will only have one transfer function that is the same
	for all pages. This is not required by the TIFF standards.

	We than read the transfer function for every page.  Depending on the
	transfer function, we allocate either 2 or 4 bytes to the XREF buffer.
	We allocate this memory after we read in the transfer function for the
	page.

	For the first exploit - POC1, this file has 3 pages. For the first page
	we allocate 2 extra extra XREF entries. Then for the next page 2 more
	entries. Then for the last page the transfer function changes and we
	allocate 4 more entries.

	When we read the file into memory, we assume we have 4 bytes extra for
	each and every page (as per the last transfer function we read). Which
	is not correct, we only have 2 bytes extra for the first 2 pages. As a
	result, we end up writing past the end of the buffer.

	There are also some related issues that this also fixes. For example,
	TIFFGetField can return uninitalized pointer values, and the logic to
	detect a N=3 vs N=1 transfer function seemed rather strange.

	It is also strange that we declare the transfer functions to be of type
	float, when the standard says they are unsigned 16 bit values. This is
	fixed in another patch.

	This patch will check to ensure that the N value for every transfer
	function is the same for every page. If this changes, we abort with an
	error. In theory, we should perhaps check that the transfer function
	itself is identical for every page, however we don't do that due to the
	confusion of the type of the data in the transfer function.

2017-12-10  Even Rouault  <<EMAIL>>

	Merge branch 'undef-warn-fixes' into 'master'
	Fix a couple of harmless but annoying -Wundef warnings

	See merge request libtiff/libtiff!8

2017-12-07  Vadim Zeitlin  <<EMAIL>>

	Remove tests for undefined SIZEOF_VOIDP.
	As configure never uses AC_CHECK_SIZEOF(void*), this symbol is never
	defined and so it doesn't make sense to test it in the code, this just
	results in -Wundef warnings if they're enabled.

	Avoid harmless -Wundef warnings for __clang_major__
	Check that we're using Clang before checking its version.

2017-12-02  Even Rouault  <<EMAIL>>

	Merge branch 'remove_autogenerated_files' into 'master'
	Remove autogenerated files

	See merge request libtiff/libtiff!5

2017-12-02  Bob Friesenhahn  <<EMAIL>>

	Merge branch 'tif_config_h_includes' into 'master'
	'tif_config.h' or 'tiffiop.h' must be included before any system header.

	See merge request libtiff/libtiff!6

2017-12-02  Bob Friesenhahn  <<EMAIL>>

	'tif_config.h' or 'tiffio.h' must be included before any system header.

2017-12-01  Even Rouault  <<EMAIL>>

	.gitignore: add patterns for build from root.

	Remove remaining .cvsignore files.

	Remove autoconf/automake generated files, and add them to .gitignore.

2017-12-01  Olivier Paquet  <<EMAIL>>

	Merge branch 'makedistcheck' into 'master'
	build/gitlab-ci and build/travis-ci: add a 'make dist' step in autoconf_build()…

	See merge request libtiff/libtiff!4

2017-12-01  Even Rouault  <<EMAIL>>

	build/gitlab-ci and build/travis-ci: add a 'make dist' step in autoconf_build() target, to check we are release-ready

2017-12-01  Even Rouault  <<EMAIL>>

	Merge branch 'git_updates' into 'master'
	CVS to Git updates

	See merge request libtiff/libtiff!2

2017-12-01  Even Rouault  <<EMAIL>>

	HOWTO-RELEASE: update to use signed tags.

	README.md: use markdown syntax for hyperlinks.

2017-11-30  Even Rouault  <<EMAIL>>

	Add .gitignore.

	Regenerate autoconf files.

	Makefile.am: update to reflect removal of README.vms and README -> README.md

	Remove all $Id and $Headers comments with CVS versions.

	HOWTO-RELEASE: update for git.

	Remove outdated .cvsignore.

	Remove outdated commit script.

	Remove README.vms.

	Rename README as README.md, and update content.

	html/index.html: reflect change from CVS to gitlab.

2017-11-30  Olivier Paquet  <<EMAIL>>

	Merge branch 'test-ci' into 'master'
	Update CI configuration

	See merge request libtiff/libtiff!1

2017-11-23  Roger Leigh  <<EMAIL>>

	appveyor: Correct path for git clone and skip artefact archival.

2017-11-22  Roger Leigh  <<EMAIL>>

	travis-ci: Remove unused matrix exclusion.

	Add gitlab-ci build support.

2017-11-18  Bob Friesenhahn  <<EMAIL>>

	* configure.ac: libtiff 4.0.9 released.

	* html/v4.0.9.html: Add HTML file to document changes in libtiff
	v4.0.9.

2017-11-17  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_aux.c, tif_getimage.c, tif_read.c: typo fixes in
	comments.

2017-11-02  Bob Friesenhahn  <<EMAIL>>

	* test/Makefile.am: Add some tests for tiff2bw.

2017-11-01  Bob Friesenhahn  <<EMAIL>>

	* tools/tiff2bw.c (main): Free memory allocated in the tiff2bw
	program.  This is in response to the report associated with
	CVE-2017-16232 but does not solve the extremely high memory usage
	with the associated POC file.

2017-10-29  Bob Friesenhahn  <<EMAIL>>

	* tools/tiff2pdf.c (t2p_sample_realize_palette): Fix possible
	arithmetic overflow in bounds checking code and eliminate
	comparison between signed and unsigned type.

	* tools/fax2tiff.c (_FAX_Client_Data): Pass FAX_Client_Data as the
	client data.  This client data is not used at all at the moment,
	but it makes the most sense.  Issue that the value of
	client_data.fd was passed where a pointer is expected was reported
	via email by Gerald Schade on Sun, 29 Oct 2017.

2017-10-23  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_getimage.c: avoid floating point division by zero in
	initCIELabConversion()
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=3733
	Credit to OSS Fuzz

2017-10-17  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_jpeg.c: add compatibility with libjpeg-turbo 1.5.2 that
	honours max_memory_to_use > 0.
	Cf https://github.com/libjpeg-turbo/libjpeg-turbo/issues/162

2017-10-10  Even Rouault <even.rouault at spatialys.com>

	* nmake.opt: support a DEBUG=1 option, so as to adjust OPTFLAGS and use
	/MDd runtime in debug mode.

2017-10-01  Even Rouault <even.rouault at spatialys.com>

	* tools/tiffset.c: fix setting a single value for the ExtraSamples tag
	(and other tags with variable number of values).
	So 'tiffset -s ExtraSamples 1 X'. This only worked
	when setting 2 or more values, but not just one.

2017-09-29  Even Rouault <even.rouault at spatialys.com>

	* libtiff/libtiff.def: add TIFFReadRGBAStripExt and TIFFReadRGBATileExt
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2735

2017-09-09  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: add NULL check to avoid likely false positive
	null-pointer dereference warning by CLang Static Analyzer. 

2017-09-07  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tiffiop.h, tif_aux.c: redirect SeekOK() macro to a _TIFFSeekoK()
	function that checks if the offset is not bigger than INT64_MAX, so as
	to avoid a -1 error return code of TIFFSeekFile() to match a required
	seek to UINT64_MAX/-1.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2726
	Adapted from proposal by Nicolas Ruff.

2017-08-29  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_jpeg.c: accept reading the last strip of a JPEG compressed
	file if the codestream height is larger than the truncated height of the
	strip. Emit a warning in this situation since this is non compliant.

2017-08-28  Even Rouault <even.rouault at spatialys.com>

	* test/Makefile.am: add missing reference to images/quad-lzw-compat.tiff
	to fix "make distcheck". Patch by Roger Leigh

2017-08-23  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirwrite.c: replace assertion to tag value not fitting
	on uint32 when selecting the value of SubIFD tag by runtime check
	(in TIFFWriteDirectoryTagSubifd()).
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2728
	Reported by team OWL337

2017-08-23  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirwrite.c: replace assertion related to not finding the
	SubIFD tag by runtime check (in TIFFWriteDirectorySec())
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2727
	Reported by team OWL337

2017-07-24  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_luv.c: further reduce memory requirements for temporary
	buffer when RowsPerStrip >= image_length in LogLuvInitState() and
	LogL16InitState().
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=2700
	Credit to OSS Fuzz

2017-07-24  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_getimage.c: fix fromskew computation when to-be-skipped
	pixel number is not a multiple of the horizontal subsampling, and
	also in some other cases. Impact putcontig8bitYCbCr44tile,
	putcontig8bitYCbCr42tile, putcontig8bitYCbCr41tile,
	putcontig8bitYCbCr21tile and putcontig8bitYCbCr12tile
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2637 (discovered
	by Agostino Sarubbo)
	and https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=2691 (credit
	to OSS Fuzz)

2017-07-24  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_getimage.c: gtTileContig() and gtTileSeparate():
	properly break from loops on error when stoponerr is set, instead
	of going on iterating on row based loop.

2017-07-18  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_luv.c: LogLuvInitState(): avoid excessive memory
	allocation when RowsPerStrip tag is missing.
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=2683
	Credit to OSS-Fuzz

2017-07-15  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: add protection against excessive memory
	allocation attempts in TIFFReadDirEntryArray() on short files.
	Effective for mmap'ed case. And non-mmap'ed case, but restricted
	to 64bit builds.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2675

2017-07-15  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: in TIFFFetchStripThing(), only grow the
	arrays that hold StripOffsets/StripByteCounts, when they are smaller
	than the expected number of striles, up to 1 million striles, and
	error out beyond. Can be tweaked by setting the environment variable
	LIBTIFF_STRILE_ARRAY_MAX_RESIZE_COUNT.
	This partially goes against a change added on 2002-12-17 to accept
	those arrays of wrong sizes, but is needed to avoid denial of services.
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=2350
	Credit to OSS Fuzz

2017-07-15  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: TIFFFillStrip() / TIFFFillTile().
	Complementary fix for http://bugzilla.maptools.org/show_bug.cgi?id=2708
	in the isMapped() case, so as to avoid excessive memory allocation
	when we need a temporary buffer but the file is truncated.

2017-07-15  Even Rouault <even.rouault at spatialys.com>

	* tools/tiff2pdf.c: prevent heap buffer overflow write in "Raw"
	mode on PlanarConfig=Contig input images.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2715
	Reported by team OWL337

2017-07-11  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dir.c: avoid potential null pointer dereference in
	_TIFFVGetField() on corrupted TIFFTAG_NUMBEROFINKS tag instance.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2713

2017-07-11  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_lzw.c: fix potential out-of-buffer read on 1-byte LZW
	strips. Crashing issue only on memory mapped files, where the strip
	offset is the last byte of the file, and the file size is a multiple
	of one page size on the CPU architecture (typically 4096). Credit
	to myself :-)

2017-07-11  Even Rouault <even.rouault at spatialys.com>

	* test/tiffcp-lzw-compat.sh, test/images/quad-lzw-compat.tiff: new files
	to test old-style LZW decompression
	* test/common.sh, Makefile.am, CMakeList.txt: updated with above

2017-07-11  Even Rouault <even.rouault at spatialys.com>

	* refresh autoconf/make stuff with what is on Ubuntu 16.04 (minor changes)

2017-07-11  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_lzw.c: fix 4.0.8 regression in the decoding of old-style LZW
	compressed files.

2017-07-10  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_pixarlog.c: avoid excessive memory allocation on decoding
	when RowsPerStrip tag is not defined (and thus td_rowsperstrip == UINT_MAX)
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=2554
	Credit to OSS Fuzz

2017-07-04  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c, tiffiop.h: add a _TIFFReadEncodedTileAndAllocBuffer()
	and _TIFFReadTileAndAllocBuffer() variants of TIFFReadEncodedTile() and
	TIFFReadTile() that allocates the decoded buffer only after a first
	successful TIFFFillTile(). This avoids excessive memory allocation
	on corrupted files.
	* libtiff/tif_getimage.c: use _TIFFReadTileAndAllocBuffer().
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=2470
	Credit to OSS Fuzz.

2017-07-04  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_error.c, tif_warning.c: correctly use va_list when both
	an old-style and new-style warning/error handlers are installed.
	Patch by Paavo Helde (sent on the mailing list)

2017-07-02  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: TIFFStartTile(): set tif_rawcc to
	tif_rawdataloaded when it is set. Similarly to TIFFStartStrip().
	This issue was revealed by the change of 2017-06-30 in TIFFFileTile(),
	limiting the number of bytes read. But it could probably have been hit
	too in CHUNKY_STRIP_READ_SUPPORT mode previously ?
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=2454
	Credit to OSS Fuzz

2017-06-30  Even Rouault <even.rouault at spatialys.com>

	* man: update documentation regarding SubIFD tag and
	TIFFSetSubDirectory() data type.
	Patch by Eric Piel
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2671

2017-06-30  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirwrite.c: in TIFFWriteDirectoryTagCheckedXXXX()
	functions associated with LONG8/SLONG8 data type, replace assertion that
	the file is BigTIFF, by a non-fatal error.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2712
	Reported by team OWL337

2017-06-30  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c, tiffiop.h: add a _TIFFReadEncodedStripAndAllocBuffer()
	function, variant of TIFFReadEncodedStrip() that allocates the
	decoded buffer only after a first successful TIFFFillStrip(). This avoids
	excessive memory allocation on corrupted files.
	* libtiff/tif_getimage.c: use _TIFFReadEncodedStripAndAllocBuffer(). 
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2708 and
	https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=2433 .
	Credit to OSS Fuzz

2017-06-30  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: TIFFFillTile(): add limitation to the number
	of bytes read in case td_stripbytecount[strip] is bigger than
	reasonable, so as to avoid excessive memory allocation (similarly to
	what was done for TIFFFileStrip() on 2017-05-10)

2017-06-29  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tiffiop.h, libtiff/tif_jpeg.c, libtiff/tif_jpeg_12.c,
	libtiff/tif_read.c: make TIFFReadScanline() works in
	CHUNKY_STRIP_READ_SUPPORT mode with JPEG stream with multiple scans.
	Also make configurable through a LIBTIFF_JPEG_MAX_ALLOWED_SCAN_NUMBER
	environment variable the maximum number of scans allowed. Defaults to
	100.

2017-06-27  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: in TIFFReadDirEntryFloat(), check that a
	double value can fit in a float before casting. Patch by Nicolas RUFF

2017-06-26  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_jbig.c: fix memory leak in error code path of JBIGDecode()
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2706
	Reported by team OWL337

2017-06-24  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_jpeg.c: error out at decoding time if anticipated libjpeg
	memory allocation is above 100 MB. libjpeg in case of multiple scans,
	which is allowed even in baseline JPEG, if components are spread over several
	scans and not interleavedin a single one, needs to allocate memory (or
	backing store) for the whole strip/tile.
	See http://www.libjpeg-turbo.org/pmwiki/uploads/About/TwoIssueswiththeJPEGStandard.pdf
	This limitation may be overriden by setting the 
	LIBTIFF_ALLOW_LARGE_LIBJPEG_MEM_ALLOC environment variable, or recompiling
	libtiff with a custom value of TIFF_LIBJPEG_LARGEST_MEM_ALLOC macro.

2017-06-24  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_jpeg.c: add anti-denial of service measure to avoid excessive
	CPU consumption on progressive JPEGs with a huge number of scans.
	See http://www.libjpeg-turbo.org/pmwiki/uploads/About/TwoIssueswiththeJPEGStandard.pdf
	Note: only affects libtiff since 2014-12-29 where support of non-baseline JPEG
	was added.

2017-06-18  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tiffiop.h: add TIFF_NOSANITIZE_UNSIGNED_INT_OVERFLOW macro to
	disable CLang warnings raised by -fsanitize=undefined,unsigned-integer-overflow
	* libtiff/tif_predict.c: decorate legitimate functions where unsigned int
	overflow occur with TIFF_NOSANITIZE_UNSIGNED_INT_OVERFLOW
	* libtiff/tif_dirread.c: avoid unsigned int overflow in EstimateStripByteCounts()
	and BYTECOUNTLOOKSBAD when file is too short.
	* libtiff/tif_jpeg.c: avoid (harmless) unsigned int overflow on tiled images.
	* libtiff/tif_fax3.c: avoid unsigned int overflow in Fax3Encode2DRow(). Could
	potentially be a bug with huge rows.
	* libtiff/tif_getimage.c: avoid many (harmless) unsigned int overflows.

2017-06-12  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: TIFFFetchStripThing(): limit the number of items
	read in StripOffsets/StripByteCounts tags to the number of strips to avoid
	excessive memory allocation.
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=2215
	Credit to OSS Fuzz

2017-06-12  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: fix regression of libtiff 4.0.8 in
	ChopUpSingleUncompressedStrip() regarding update of newly single-strip
	uncompressed files whose bytecount is 0. Before the change of 2016-12-03,
	the condition bytecount==0 used to trigger an early exit/disabling of
	strip chop. Re-introduce that in update mode. Otherwise this cause
	later incorrect setting for the value of StripByCounts/StripOffsets.
	( https://trac.osgeo.org/gdal/ticket/6924 )

2017-06-10  Even Rouault <even.rouault at spatialys.com>

	* .appveyor.yml, .travis.yml, build/travis-ci: apply patches
	0001-ci-Travis-script-improvements.patch and
	0002-ci-Invoke-helper-script-via-shell.patch by Roger Leigh
	(sent to mailing list)

2017-06-08  Even Rouault <even.rouault at spatialys.com>

	* .travis.yml, build/travis-ci: new files from
	0001-ci-Add-Travis-support-for-Linux-builds-with-Autoconf.patch by
	Roger Leigh (sent to mailing list on 2017-06-08)
	This patch adds support for the Travis-CI service.

	* .appveyor.yml: new file from
	0002-ci-Add-AppVeyor-support.patch by Roger Leigh (sent to mailing
	list on 2017-06-08)
	This patch adds a .appveyor.yml file to the top-level.  This allows
	one to opt in to having a branch built on Windows with Cygwin,
	MinGW and MSVC automatically when a branch is pushed to GitHub,
	GitLab, BitBucket or any other supported git hosting service.

	* CMakeLists.txt, test/CMakeLists.txt, test/TiffTestCommon.cmake: apply
	patch 0001-cmake-Improve-Cygwin-and-MingGW-test-support.patch from Roger
	Leigh (sent to mailing list on 2017-06-08)
	This patch makes the CMake build system support running the tests
	with MinGW or Cygwin.

2017-06-08  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_swab.c: if DISABLE_CHECK_TIFFSWABMACROS is defined, do not do
	the #ifdef TIFFSwabXXX checks. Make it easier for GDAL to rename the symbols
	of its internal libtiff copy.

2017-06-01  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirinfo.c, tif_dirread.c: add _TIFFCheckFieldIsValidForCodec(),
	and use it in TIFFReadDirectory() so as to ignore fields whose tag is a
	codec-specified tag but this codec is not enabled. This avoids TIFFGetField()
	to behave differently depending on whether the codec is enabled or not, and
	thus can avoid stack based buffer overflows in a number of TIFF utilities
	such as tiffsplit, tiffcmp, thumbnail, etc.
	Patch derived from 0063-Handle-properly-CODEC-specific-tags.patch
	(http://bugzilla.maptools.org/show_bug.cgi?id=2580) by Raphaël Hertzog.
	Fixes:
	http://bugzilla.maptools.org/show_bug.cgi?id=2580
	http://bugzilla.maptools.org/show_bug.cgi?id=2693
	http://bugzilla.maptools.org/show_bug.cgi?id=2625 (CVE-2016-10095)
	http://bugzilla.maptools.org/show_bug.cgi?id=2564 (CVE-2015-7554)
	http://bugzilla.maptools.org/show_bug.cgi?id=2561 (CVE-2016-5318)
	http://bugzilla.maptools.org/show_bug.cgi?id=2499 (CVE-2014-8128)
	http://bugzilla.maptools.org/show_bug.cgi?id=2441
	http://bugzilla.maptools.org/show_bug.cgi?id=2433

2017-05-29  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_getimage.c: initYCbCrConversion(): stricter validation for
	refBlackWhite coefficients values. To avoid invalid float->int32 conversion
	(when refBlackWhite[0] == 2147483648.f)
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=1907
	Credit to OSS Fuzz

2017-05-29  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_color.c: TIFFYCbCrToRGBInit(): stricter clamping to avoid
	int32 overflow in TIFFYCbCrtoRGB().
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=1844
	Credit to OSS Fuzz

2017-05-21  Bob Friesenhahn  <<EMAIL>>

	* configure.ac: libtiff 4.0.8 released.

	* html/v4.0.8.html: Add description of changes targeting the 4.0.8
	release.

2017-05-20 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_getimage.c: initYCbCrConversion(): stricter validation for
	refBlackWhite coefficients values. To avoid invalid float->int32 conversion.
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=1718
	Credit to OSS Fuzz

2017-05-18 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_getimage.c: initYCbCrConversion(): check luma[1] is not zero
	to avoid division by zero.
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=1665
	Credit to OSS Fuzz

2017-05-17 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: _TIFFVSetField(): fix outside range cast of double to
	float.
	Credit to Google Autofuzz project

2017-05-17 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_getimage.c: initYCbCrConversion(): add basic validation of
	luma and refBlackWhite coefficients (just check they are not NaN for now),
	to avoid potential float to int overflows.
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=1663
	Credit to OSS Fuzz

2017-05-17 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_pixarlog.c: PixarLogDecode(): resync tif_rawcp with
	next_in and tif_rawcc with avail_in at beginning and end of function,
	similarly to what is done in LZWDecode(). Likely needed so that it
	works properly with latest chnges in tif_read.c in CHUNKY_STRIP_READ_SUPPORT
	mode. But untested...

2017-05-17 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_lzw.c: update dec_bitsleft at beginning of LZWDecode(),
	and update tif_rawcc at end of LZWDecode(). This is needed to properly
	work with the latest chnges in tif_read.c in CHUNKY_STRIP_READ_SUPPORT
	mode.

2017-05-14 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_luv.c: LogL16InitState(): avoid excessive memory
	allocation when RowsPerStrip tag is missing.
	Credit to OSS-Fuzz (locally run, on GDAL)

2017-05-14 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_packbits.c: fix out-of-buffer read in PackBitsDecode()
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=1563
	Credit to OSS-Fuzz

2017-05-13 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_pixarlog.c, tif_luv.c: avoid potential int32
	overflows in multiply_ms() and add_ms().
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=1558
	Credit to OSS-Fuzz

2017-05-13 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_color.c: avoid potential int32 overflow in
	TIFFYCbCrToRGBInit()
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=1533
	Credit to OSS-Fuzz

2017-05-13 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: update tif_rawcc in CHUNKY_STRIP_READ_SUPPORT
	mode with tif_rawdataloaded when calling TIFFStartStrip() or
	TIFFFillStripPartial(). This avoids reading beyond tif_rawdata
	when bytecount > tif_rawdatasize.
	Fixes https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=1545.
	Credit to OSS-Fuzz

2017-05-12 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: TIFFFillStripPartial():
	avoid excessive memory allocation in case of shorten files.
	Only effective on 64 bit builds.
	Credit to OSS-Fuzz (locally run, on GDAL)

2017-05-12 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: TIFFFillStripPartial() / TIFFSeek(),
	avoid potential integer overflows with read_ahead in
	CHUNKY_STRIP_READ_SUPPORT mode. Should
	especially occur on 32 bit platforms.

2017-05-10 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: TIFFFillStrip() and TIFFFillTile():
	avoid excessive memory allocation in case of shorten files.
	Only effective on 64 bit builds and non-mapped cases.
	Credit to OSS-Fuzz (locally run, on GDAL)

2017-05-10 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_zip.c, tif_pixarlog.c, tif_predict.c: fix memory
	leak when the underlying codec (ZIP, PixarLog) succeeds its
	setupdecode() method, but PredictorSetup fails.
	Credit to OSS-Fuzz (locally run, on GDAL)

2017-05-10 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: TIFFFillStrip(): add limitation to the number
	of bytes read in case td_stripbytecount[strip] is bigger than
	reasonable, so as to avoid excessive memory allocation.

2017-04-28 Even Rouault <even.rouault at spatialys.com>

	* tools/tiff2bw.c: close TIFF handle in error code path.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2677

2017-04-27 Even Rouault <even.rouault at spatialys.com>

	* litiff/tif_fax3.c: avoid crash in Fax3Close() on empty file.
	Patch by Alan Coopersmith  + complement by myself.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2673
	* tools/fax2tiff.c: emit appropriate message if the input file is
	empty. Patch by Alan Coopersmith.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2672

2017-04-27 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_ojpeg.c: fix potential memory leak in
	OJPEGReadHeaderInfoSecTablesQTable, OJPEGReadHeaderInfoSecTablesDcTable
	and OJPEGReadHeaderInfoSecTablesAcTable
	Patch by Nicolás Peña.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2670

2017-04-27 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: fix memory leak in non DEFER_STRILE_LOAD
	mode (ie default) when there is both a StripOffsets and
	TileOffsets tag, or a StripByteCounts and TileByteCounts
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2689
	* tools/tiff2ps.c: call TIFFClose() in error code paths.

2017-02-25 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_fax3.c, tif_predict.c, tif_getimage.c: fix GCC 7
	-Wimplicit-fallthrough warnings.

2017-02-18 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_pixarlog.c: fix memory leak in error code path of
	PixarLogSetupDecode(). Patch by Nicolás Peña.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2665

2017-02-18 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_lzw.c: in LZWPostEncode(), increase, if necessary, the
	code bit-width after flushing the remaining code and before emitting
	the EOI code.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=1982

2017-01-31 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_jpeg.c: only run JPEGFixupTagsSubsampling() if the
	YCbCrSubsampling tag is not explicitly present. This helps a bit to reduce
	the I/O amount when te tag is present (especially on cloud hosted files).

2017-01-14 Even Rouault <even.rouault at spatialys.com>

	* tools/raw2tiff.c: avoid integer division by zero.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2631

2017-01-12 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_ojpeg.c: fix leak in OJPEGReadHeaderInfoSecTablesQTable,
	OJPEGReadHeaderInfoSecTablesDcTable and OJPEGReadHeaderInfoSecTablesAcTable
	when read fails.
	Patch by Nicolás Peña.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2659

2017-01-11 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_luv.c, tif_lzw.c, tif_packbits.c: return 0 in Encode
	functions instead of -1 when TIFFFlushData1() fails.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2130

2017-01-11 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcp.c: error out cleanly in cpContig2SeparateByRow and
	cpSeparate2ContigByRow if BitsPerSample != 8 to avoid heap based overflow.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2656 and
	http://bugzilla.maptools.org/show_bug.cgi?id=2657

2017-01-11 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tiffio.h, tif_unix.c, tif_win32.c, tif_vms.c: add _TIFFcalloc()

	* libtiff/tif_read.c: TIFFReadBufferSetup(): use _TIFFcalloc() to zero
	initialize tif_rawdata.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2651

2017-01-11 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_getimage.c: add explicit uint32 cast in putagreytile to
	avoid UndefinedBehaviorSanitizer warning.
	Patch by Nicolás Peña.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2658

2017-01-11 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: avoid potential undefined behaviour on signed integer
	addition in TIFFReadRawStrip1() in isMapped() case.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2650

2017-01-11 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_jpeg.c: validate BitsPerSample in JPEGSetupEncode() to avoid
	undefined behaviour caused by invalid shift exponent.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2648

2017-01-11 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dir.c, tif_dirread.c, tif_dirwrite.c: implement various clampings
	of double to other data types to avoid undefined behaviour if the output range
	isn't big enough to hold the input value.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2643
	http://bugzilla.maptools.org/show_bug.cgi?id=2642
	http://bugzilla.maptools.org/show_bug.cgi?id=2646
	http://bugzilla.maptools.org/show_bug.cgi?id=2647

2017-01-11 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: avoid division by floating point 0 in
	TIFFReadDirEntryCheckedRational() and TIFFReadDirEntryCheckedSrational(),
	and return 0 in that case (instead of infinity as before presumably)
	Apparently some sanitizers do not like those divisions by zero.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2644

2017-01-11 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirwrite.c: in TIFFWriteDirectoryTagCheckedRational, replace
	assertion by runtime check to error out if passed value is strictly
	negative.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2535

	* tools/tiffcrop.c: remove extraneous TIFFClose() in error code path, that
	caused double free.
	Related to http://bugzilla.maptools.org/show_bug.cgi?id=2535

2017-01-11 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_jpeg.c: avoid integer division by zero in
	JPEGSetupEncode() when horizontal or vertical sampling is set to 0.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2653

2017-01-03 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_jpeg.c: increase libjpeg max memory usable to
	10 MB instead of libjpeg 1MB default. This helps when creating files
	with "big" tile, without using libjpeg temporary files.
	Related to https://trac.osgeo.org/gdal/ticket/6757

2016-12-20 Even Rouault <even.rouault at spatialys.com>

	* tools/tiff2pdf.c: avoid potential heap-based overflow in
	t2p_readwrite_pdf_image_tile().
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2640

2016-12-20 Even Rouault <even.rouault at spatialys.com>

	* tools/tiff2pdf.c: avoid potential invalid memory read in
	t2p_writeproc.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2639

2016-12-20 Even Rouault <even.rouault at spatialys.com>

	* tools/tiff2pdf.c: fix wrong usage of memcpy() that can trigger
	unspecified behaviour.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2638

2016-12-18 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_getimage.c: fix potential memory leaks in error code
	path of TIFFRGBAImageBegin().
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2627

2016-12-18 Even Rouault <even.rouault at spatialys.com>

	* tools/tiff2pdf.c: prevent heap-based buffer overflow in -j mode
	on a paletted image. Note: this fix errors out before the overflow
	happens. There could probably be a better fix.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2635

2016-12-17 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tiffio.h, libtiff/tif_getimage.c: add TIFFReadRGBAStripExt()
	and TIFFReadRGBATileExt() variants of the functions without ext, with
	an extra argument to control the stop_on_error behaviour.

2016-12-17 Even Rouault <even.rouault at spatialys.com>

	* tools/tiff2ps.c: fix 2 heap-based buffer overflows (in PSDataBW
	and PSDataColorContig). Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2633 and
	http://bugzilla.maptools.org/show_bug.cgi?id=2634.

2016-12-13 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_fax3.h: revert change done on 2016-01-09 that made
	Param member of TIFFFaxTabEnt structure a uint16 to reduce size of
	the binary. It happens that the Hylafax software uses the tables that
	follow this typedef (TIFFFaxMainTable, TIFFFaxWhiteTable,
	TIFFFaxBlackTable), although they are not in a public libtiff header.
	Raised by Lee Howard.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2636

2016-12-04 Even Rouault <even.rouault at spatialys.com>

	* html/man/Makefile.am: remove thumbnail.1.html and rgb2ycbcr.1.html
	from installed pages since the corresponding utilities are no longer
	installed. Reported by Havard Eidnes
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2606

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_write.c: fix misleading indentation as warned by GCC.

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcp.c: replace assert( (bps % 8) == 0 ) by a non assert check.
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2605

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcp.c: fix uint32 underflow/overflow that can cause heap-based
	buffer overflow.
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2610

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcp.c: avoid potential division by zero is BitsPerSamples tag is
	missing.
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2607

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* man/Makefile.am: remove thumbnail.1 and rgb2ycbcr.1 from installed man
	pages since the corresponding utilities are no longer installed.
	Reported by Havard Eidnes 
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2606

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* tools/tif_dir.c: when TIFFGetField(, TIFFTAG_NUMBEROFINKS, ) is called,
	limit the return number of inks to SamplesPerPixel, so that code that parses
	ink names doesn't go past the end of the buffer.
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2599

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcp.c: avoid potential division by zero is BitsPerSamples tag is
	missing.
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2597

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffinfo.c: fix null pointer dereference in -r mode when the image has
	no StripByteCount tag.
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2594

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcrop.c: fix integer division by zero when BitsPerSample is missing.
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2619

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcrop.c: add 3 extra bytes at end of strip buffer in
	readSeparateStripsIntoBuffer() to avoid read outside of heap allocated buffer.
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2621

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcrop.c: fix readContigStripsIntoBuffer() in -i (ignore) mode so
	that the output buffer is correctly incremented to avoid write outside bounds.
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2620

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_ojpeg.c: make OJPEGDecode() early exit in case of failure in
	OJPEGPreDecode(). This will avoid a divide by zero, and potential other issues.
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2611

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: modify ChopUpSingleUncompressedStrip() to
	instanciate compute ntrips as TIFFhowmany_32(td->td_imagelength, rowsperstrip),
	instead of a logic based on the total size of data. Which is faulty is
	the total size of data is not sufficient to fill the whole image, and thus
	results in reading outside of the StripByCounts/StripOffsets arrays when
	using TIFFReadScanline().
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2608.

	* libtiff/tif_strip.c: revert the change in TIFFNumberOfStrips() done
	for http://bugzilla.maptools.org/show_bug.cgi?id=2587 / CVE-2016-9273 since
	the above change is a better fix that makes it unnecessary.

2016-12-03 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_pixarlog.c, libtiff/tif_luv.c: fix heap-based buffer
	overflow on generation of PixarLog / LUV compressed files, with
	ColorMap, TransferFunction attached and nasty plays with bitspersample.
	The fix for LUV has not been tested, but suffers from the same kind
	of issue of PixarLog.
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2604

2016-12-02 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcp.c: avoid uint32 underflow in cpDecodedStrips that 
	can cause various issues, such as buffer overflows in the library.
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2598

2016-12-02 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c, libtiff/tiffiop.h: fix uint32 overflow in
	TIFFReadEncodedStrip() that caused an integer division by zero.
	Reported by Agostino Sarubbo.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2596

2016-11-20 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_getimage.c, libtiff/tif_open.c: add parenthesis to
	fix cppcheck clarifyCalculation warnings
	* libtiff/tif_predict.c, libtiff/tif_print.c: fix printf unsigned
	vs signed formatting (cppcheck invalidPrintfArgType_uint warnings)

2016-11-20  Bob Friesenhahn  <<EMAIL>>

	* tools/fax2tiff.c (main): Applied patch by Jörg Ahrens to fix
	passing client data for Win32 builds using tif_win32.c
	(USE_WIN32_FILEIO defined) for file I/O.  Patch was provided via
	email on November 20, 2016.

2016-11-19  Bob Friesenhahn  <<EMAIL>>

	* libtiff 4.0.7 released.

	* configure.ac: Update for 4.0.7 release.

	* tools/tiffdump.c (ReadDirectory): Remove uint32 cast to
	_TIFFmalloc() argument which resulted in Coverity report.  Added
	more mutiplication overflow checks.

2016-11-18 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcrop.c: Fix memory leak in (recent) error code path.
	Fixes Coverity 1394415.

2016-11-17  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_getimage.c: Fix some benign warnings which appear in
	64-bit compilation under Microsoft Visual Studio of the form
	"Arithmetic overflow: 32-bit value is shifted, then cast to 64-bit
	value.  Results might not be an expected value.".  Problem was
	reported on November 16, 2016 on the tiff mailing list.

2016-11-16 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: in TIFFFetchNormalTag(), do not dereference
	NULL pointer when values of tags with TIFF_SETGET_C16_ASCII / TIFF_SETGET_C32_ASCII
	access are 0-byte arrays.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2593 (regression introduced
	by previous fix done on 2016-11-11 for CVE-2016-9297).
	Reported by Henri Salo. Assigned as CVE-2016-9448

2016-11-12  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffinfo.c (TIFFReadContigTileData): Fix signed/unsigned
	comparison warning.
	(TIFFReadSeparateTileData): Fix signed/unsigned comparison
	warning.

	* tools/tiffcrop.c (readContigTilesIntoBuffer): Fix
	signed/unsigned comparison warning.

	* html/v4.0.7.html: Add a file to document the pending 4.0.7
	release.

2016-11-11 Even Rouault <even.rouault at spatialys.com>

	* tools/tiff2pdf.c: avoid undefined behaviour related to overlapping
	of source and destination buffer in memcpy() call in
	t2p_sample_rgbaa_to_rgb()
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2577

2016-11-11 Even Rouault <even.rouault at spatialys.com>

	* tools/tiff2pdf.c: fix potential integer overflows on 32 bit builds
	in t2p_read_tiff_size()
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2576

2016-11-11 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_aux.c: fix crash in TIFFVGetFieldDefaulted()
	when requesting Predictor tag and that the zip/lzw codec is not
	configured.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2591

2016-11-11 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: in TIFFFetchNormalTag(), make sure that
	values of tags with TIFF_SETGET_C16_ASCII / TIFF_SETGET_C32_ASCII
	access are null terminated, to avoid potential read outside buffer
	in _TIFFPrintField().
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2590 (CVE-2016-9297)

2016-11-11 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: reject images with OJPEG compression that
	have no TileOffsets/StripOffsets tag, when OJPEG compression is
	disabled. Prevent null pointer dereference in TIFFReadRawStrip1()
	and other functions that expect td_stripbytecount to be non NULL.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2585

2016-11-11 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcrop.c: fix multiple uint32 overflows in
	writeBufferToSeparateStrips(), writeBufferToContigTiles() and
	writeBufferToSeparateTiles() that could cause heap buffer overflows.
	Reported by Henri Salo from Nixu Corporation.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2592 (CVE-2016-9532)

2016-11-10 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_strip.c: make TIFFNumberOfStrips() return the td->td_nstrips
	value when it is non-zero, instead of recomputing it. This is needed in
	TIFF_STRIPCHOP mode where td_nstrips is modified. Fixes a read outsize of
	array in tiffsplit (or other utilities using TIFFNumberOfStrips()).
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2587 (CVE-2016-9273)

2016-11-04 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_predic.c: fix memory leaks in error code paths added in
	previous commit (fix for MSVR 35105)

2016-10-31 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_predict.h, libtiff/tif_predict.c:
	Replace assertions by runtime checks to avoid assertions in debug mode,
	or buffer overflows in release mode. Can happen when dealing with
	unusual tile size like YCbCr with subsampling. Reported as MSVR 35105
	by Axel Souchet	& Vishal Chauhan from the MSRC Vulnerabilities & Mitigations
	team.

2016-10-26 Even Rouault <even.rouault at spatialys.com>

	* tools/fax2tiff.c: fix segfault when specifying -r without
	argument. Patch by Yuriy M. Kaminskiy.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2572

2016-10-25 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dir.c: discard values of SMinSampleValue and
	SMaxSampleValue when they have been read and the value of
	SamplesPerPixel is changed afterwards (like when reading a
	OJPEG compressed image with a missing SamplesPerPixel tag,
	and whose photometric is RGB or YCbCr, forcing SamplesPerPixel
	being 3). Otherwise when rewriting the directory (for example
	with tiffset, we will expect 3 values whereas the array had been
	allocated with just one), thus causing a out of bound read access.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2500
	(CVE-2014-8127, duplicate: CVE-2016-3658)
	
	* libtiff/tif_dirwrite.c: avoid null pointer dereference on td_stripoffset
	when writing directory, if FIELD_STRIPOFFSETS was artificially set
	for a hack case	in OJPEG case.
	Fixes http://bugzilla.maptools.org/show_bug.cgi?id=2500
	(CVE-2014-8127, duplicate: CVE-2016-3658)

2016-10-25 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffinfo.c: fix out-of-bound read on some tiled images.
	(http://bugzilla.maptools.org/show_bug.cgi?id=2517)

	* libtiff/tif_compress.c: make TIFFNoDecode() return 0 to indicate an
	error and make upper level read routines treat it accordingly.
	(linked to the test case of http://bugzilla.maptools.org/show_bug.cgi?id=2517)

2016-10-14 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcrop.c: fix out-of-bound read of up to 3 bytes in
	readContigTilesIntoBuffer(). Reported as MSVR 35092 by Axel Souchet
	& Vishal Chauhan from the MSRC Vulnerabilities & Mitigations team.

2016-10-09 Even Rouault <even.rouault at spatialys.com>

	* tools/tiff2pdf.c: fix write buffer overflow of 2 bytes on JPEG
	compressed images. Reported by Tyler Bohan of Cisco Talos as
	TALOS-CAN-0187 / CVE-2016-5652.
	Also prevents writing 2 extra uninitialized bytes to the file stream.

2016-10-08 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcp.c: fix out-of-bounds write on tiled images with odd
	tile width vs image width. Reported as MSVR 35103
	by Axel Souchet and Vishal Chauhan from the MSRC Vulnerabilities &
	Mitigations team.

2016-10-08 Even Rouault <even.rouault at spatialys.com>

	* tools/tiff2pdf.c: fix read -largely- outsize of buffer in
	t2p_readwrite_pdf_image_tile(), causing crash, when reading a
	JPEG compressed image with TIFFTAG_JPEGTABLES length being one.
	Reported as MSVR 35101 by Axel Souchet and Vishal Chauhan from
	the MSRC Vulnerabilities & Mitigations team. CVE-2016-9453

2016-10-08 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcp.c: fix read of undefined variable in case of missing
	required tags. Found on test case of MSVR 35100.
	* tools/tiffcrop.c: fix read of undefined buffer in
	readContigStripsIntoBuffer() due to uint16 overflow. Probably not a
	security issue but I can be wrong. Reported as MSVR 35100 by Axel
	Souchet from the MSRC Vulnerabilities & Mitigations team.

2016-09-25  Bob Friesenhahn  <<EMAIL>>

	* html: Change as many remotesensing.org broken links to a working
	URL as possible.

2016-09-24  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_getimage.c (TIFFRGBAImageOK): Reject attempts to
	read floating point images.

	* libtiff/tif_predict.c (PredictorSetup): Enforce bits-per-sample
	requirements of floating point predictor (3).  Fixes CVE-2016-3622
	"Divide By Zero in the tiff2rgba tool."

2016-09-23 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcrop.c: fix various out-of-bounds write vulnerabilities
	in heap or stack allocated buffers. Reported as MSVR 35093,
	MSVR 35096 and MSVR 35097. Discovered by Axel Souchet and Vishal
	Chauhan from the MSRC Vulnerabilities & Mitigations team.
	* tools/tiff2pdf.c: fix out-of-bounds write vulnerabilities in
	heap allocate buffer in t2p_process_jpeg_strip(). Reported as MSVR
	35098. Discovered by Axel Souchet and Vishal Chauhan from the MSRC
	Vulnerabilities & Mitigations team.
	* libtiff/tif_pixarlog.c: fix out-of-bounds write vulnerabilities
	in heap allocated buffers. Reported as MSVR 35094. Discovered by
	Axel Souchet and Vishal Chauhan from the MSRC Vulnerabilities &
	Mitigations team.
	* libtiff/tif_write.c: fix issue in error code path of TIFFFlushData1()
	that didn't reset the tif_rawcc and tif_rawcp members. I'm not
	completely sure if that could happen in practice outside of the odd
	behaviour of t2p_seekproc() of tiff2pdf). The report points that a
	better fix could be to check the return value of TIFFFlushData1() in
	places where it isn't done currently, but it seems this patch is enough.
	Reported as MSVR 35095. Discovered by Axel Souchet & Vishal Chauhan &
	Suha Can from the MSRC Vulnerabilities & Mitigations team.

2016-09-20  Bob Friesenhahn  <<EMAIL>>

	* html/man/index.html: Comment out links to documentation for
	abandoned utilities.

2016-09-17 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_lzma.c: typo fix in comment

2016-09-04 Even Rouault <even.rouault at spatialys.com>

	* libtiff/*.c: fix warnings raised by clang 3.9 -Wcomma

2016-09-03 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirwrite.c, libtiff/tif_color.c: fix warnings raised
	by GCC 5 / clang -Wfloat-conversion

2016-08-16 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcrop.c: fix C99'ism.

2016-08-15 Even Rouault <even.rouault at spatialys.com>

	* tools/tiff2bw.c: fix weight computation that could result of color
	value overflow (no security implication). Fix bugzilla #2550.
	Patch by Frank Freudenberg.

2016-08-15 Even Rouault <even.rouault at spatialys.com>

	* tools/rgb2ycbcr.c: validate values of -v and -h parameters to
	avoid potential divide by zero. Fixes CVE-2016-3623 (bugzilla #2569)

2016-08-15 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcrop.c: Fix out-of-bounds write in loadImage().
	From patch libtiff-CVE-2016-3991.patch from
	libtiff-4.0.3-25.el7_2.src.rpm by Nikola Forro (bugzilla #2543)

2016-08-15 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_pixarlog.c: Fix write buffer overflow in PixarLogEncode
	if more input samples are provided than expected by PixarLogSetupEncode.
	Idea based on libtiff-CVE-2016-3990.patch from
	libtiff-4.0.3-25.el7_2.src.rpm by Nikola Forro, but with different and
	simpler check. (bugzilla #2544)

2016-08-15 Even Rouault <even.rouault at spatialys.com>

	* tools/tiff2rgba.c: Fix integer overflow in size of allocated
	buffer, when -b mode is enabled, that could result in out-of-bounds
	write. Based initially on patch tiff-CVE-2016-3945.patch from
	libtiff-4.0.3-25.el7_2.src.rpm by Nikola Forro, with correction for
	invalid tests that rejected valid files. (bugzilla #2545)

2016-07-11 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffcrop.c: Avoid access outside of stack allocated array
	on a tiled separate TIFF with more than 8 samples per pixel.
	Reported by Kaixiang Zhang of the Cloud Security Team, Qihoo 360
	(CVE-2016-5321 / CVE-2016-5323 , bugzilla #2558 / #2559)

2016-07-10 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: Fix out-of-bounds read on
	memory-mapped files in TIFFReadRawStrip1() and TIFFReadRawTile1()
	when stripoffset is beyond tmsize_t max value (reported by
	Mathias Svensson)

2016-07-10 Even Rouault <even.rouault at spatialys.com>

	* tools/tiffdump.c: fix a few misaligned 64-bit reads warned
	by -fsanitize

2016-07-03 Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c: make TIFFReadEncodedStrip() and
	TIFFReadEncodedTile() directly use user provided buffer when
	no compression (and other conditions) to save a memcpy().

	* libtiff/tif_write.c: make TIFFWriteEncodedStrip() and
	TIFFWriteEncodedTile() directly use user provided buffer when
	no compression to save a memcpy().

2016-07-01  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_luv.c: validate that for COMPRESSION_SGILOG and
	PHOTOMETRIC_LOGL, there is only one sample per pixel. Avoid
	potential invalid memory write on corrupted/unexpected images when
	using the TIFFRGBAImageBegin() interface (reported by
	Clay Wood)

2016-06-28  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_pixarlog.c: fix potential buffer write overrun in
	PixarLogDecode() on corrupted/unexpected images (reported by Mathias Svensson)
	(CVE-2016-5875)

2016-06-15  Bob Friesenhahn  <<EMAIL>>

	* libtiff/libtiff.def: Added _TIFFMultiply32 and _TIFFMultiply64
	to libtiff.def

2016-06-05  Bob Friesenhahn  <<EMAIL>>

	* tools/Makefile.am: The libtiff tools bmp2tiff, gif2tiff,
	ras2tiff, sgi2tiff, sgisv, and ycbcr are completely removed from
	the distribution.  The libtiff tools rgb2ycbcr and thumbnail are
	only built in the build tree for testing.  Old files are put in
	new 'archive' subdirectory of the source repository, but not in
	distribution archives.  These changes are made in order to lessen
	the maintenance burden.

2016-05-10  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_config.vc.h (HAVE_SNPRINTF): Add a '1' to the
	HAVE_SNPRINTF definition.'

2016-05-09  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_config.vc.h (HAVE_SNPRINTF): Applied patch by Edward
	Lam to define HAVE_SNPRINTF for Visual Studio 2015.

2016-04-27  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: when compiled with DEFER_STRILE_LOAD,
	fix regression, introduced on 2014-12-23, when reading a one-strip
	file without a StripByteCounts tag. GDAL #6490

2016-04-07  Bob Friesenhahn  <<EMAIL>>

	* html/bugs.html: Replace Andrey Kiselev with Bob Friesenhahn for
	purposes of security issue reporting.

2016-01-23  Even Rouault <even.rouault at spatialys.com>

	* libtiff/*: upstream typo fixes (mostly contributed by Kurt Schwehr)
	coming from GDAL internal libtiff

2016-01-09  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_fax3.h: make Param member of TIFFFaxTabEnt structure
	a uint16 to reduce size of the binary.

2016-01-03  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_read.c, tif_dirread.c: fix indentation issues raised
	by GCC 6 -Wmisleading-indentation

2015-12-27  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_pixarlog.c: avoid zlib error messages to pass a NULL
	string to %s formatter, which is undefined behaviour in sprintf().

2015-12-27  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_next.c: fix potential out-of-bound write in NeXTDecode()
	triggered by http://lcamtuf.coredump.cx/afl/vulns/libtiff5.tif
	(bugzilla #2508)

2015-12-27  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_luv.c: fix potential out-of-bound writes in decode
	functions in non debug builds by replacing assert()s by regular if
	checks (bugzilla #2522).
	Fix potential out-of-bound reads in case of short input data.

2015-12-26  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_getimage.c: fix out-of-bound reads in TIFFRGBAImage
	interface in case of unsupported values of SamplesPerPixel/ExtraSamples
	for LogLUV / CIELab. Add explicit call to TIFFRGBAImageOK() in
	TIFFRGBAImageBegin(). Fix CVE-2015-8665 reported by limingxing and
	CVE-2015-8683 reported by zzf of Alibaba.

2015-12-21  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: workaround false positive warning of Clang Static
	Analyzer about null pointer dereference in TIFFCheckDirOffset().

2015-12-19  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_fax3.c: remove dead assignment in Fax3PutEOLgdal(). Found
	by Clang Static Analyzer

2015-12-18  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirwrite.c: fix truncation to 32 bit of file offsets in
	TIFFLinkDirectory() and TIFFWriteDirectorySec() when aligning directory
	offsets on a even offset (affects BigTIFF). This was a regression of the
	changeset of 2015-10-19.

2015-12-12  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_write.c: TIFFWriteEncodedStrip() and TIFFWriteEncodedTile()
	should return -1 in case of failure of tif_encodestrip() as documented
	* libtiff/tif_dumpmode.c: DumpModeEncode() should return 0 in case of
	failure so that the above mentionned functions detect the error.

2015-12-06  Even Rouault <even.rouault at spatialys.com>

	* libtiff/uvcode.h: const'ify uv_code array

2015-12-06  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirinfo.c: const'ify tiffFields, exifFields,
	tiffFieldArray and exifFieldArray arrays

2015-12-06  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_print.c: constify photoNames and orientNames arrays

2015-12-06  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_close.c, libtiff/tif_extension.c : rename link
	variable to avoid -Wshadow warnings

2015-11-22  Even Rouault <even.rouault at spatialys.com>

	* libtiff/*.c: fix typos in comments (patch by Kurt Schwehr)
 
2015-11-22  Even Rouault <even.rouault at spatialys.com>

	* libtiff/*.c: fix MSVC warnings related to cast shortening and
	assignment within conditional expression

2015-11-18  Even Rouault <even.rouault at spatialys.com>

	* libtiff/*.c: fix clang -Wshorten-64-to-32 warnings

2015-11-18  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: initialize double* data at line 3693 to NULL
	to please MSVC 2013

2015-11-17  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: prevent reading ColorMap or TransferFunction
	if BitsPerPixel > 24, so as to avoid huge memory allocation and file
	read attempts

2015-11-02  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dirread.c: remove duplicated assignment (reported by
	Clang static analyzer)

2015-10-28  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dir.c, libtiff/tif_dirinfo.c, libtiff/tif_compress.c,
	libtiff/tif_jpeg_12.c: suppress warnings about 'no previous
	declaration/prototype'

2015-10-19  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tiffiop.h, libtiff/tif_dirwrite.c: suffix constants by U to fix 
	'warning: negative integer implicitly converted to unsigned type' warning
	(part of -Wconversion)

2015-10-17  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_dir.c, libtiff/tif_dirread.c, libtiff/tif_getimage.c,
	  libtiff/tif_print.c: fix -Wshadow warnings (only in libtiff/)

2015-09-12  Bob Friesenhahn  <<EMAIL>>

	* libtiff 4.0.6 released.

	* html/v4.0.6.html: Added release notes for 4.0.6.

2015-09-06  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffgt.c: Silence glut API deprecation warnings on MacOS
	X.  Patch by Roger Leigh.

	* Makefile.am: Added a 'coverity' rule to assist with Coverity
	submissions.

	* tools/tiff2pdf.c: Fix compiler warning about unused function
	when JPEG is not available.

	* tools/fax2ps.c (main): Detect failure to write to temporary
	file.

2015-09-05  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_dirread.c (TIFFReadDirEntryCheckRangeSlongSlong8):
	Change implementation so that it does not sometimes overflow the
	range of a 32-bit int and to avoid a signed vs unsigned compare
	compiler warning.
	(TIFF_INT64_MAX): Avoid use of platform-specific large constants.
	(TIFF_UINT32_MAX): Avoid use of platform-specific large constants.

2015-09-01  Bob Friesenhahn  <<EMAIL>>

	* Makefile.am (distcheck-hook), configure.ac: Applied patches by
	Roger Leigh (via tiff mailing list on 2015-09-01) to fix issue
	with BSD make and to make use of cmake in 'distcheck' target
	conditional on if cmake is available.

	* CMakeLists.txt, Makefile.am, configure.ac: Applied patches by
	Roger Leigh (via tiff mailing list on 2015-09-01).

	CMake build is now included in 'distcheck' target.

	Builds with CMake 2.8.9 and newer.

	Tar is now resquested to use POSIX PAX format.

2015-08-31  Bob Friesenhahn  <<EMAIL>>

	* CMakeLists.txt, libtiff/test/Makefile.am: Applied patches by
	Roger Leigh (via tiff mailing list on 2015-08-31.

	CMake reads all version information directly from configure.ac to
	avoid duplication of values.  This basically greps over the file
	for the LIBTIFF_* variables, then translates them to the form
	needed for cmake. This includes the release version and libtool
	shared library version information.

	Make shared/static library building configurable.  Currently it
	always builds shared libraries, with static libs having a _static
	suffix (copying zlib, but it means it's got a non-standard name).
	CMake has a -DBUILD_SHARED_LIBS=ON|OFF option to select one or the
	other, which is now used instead.  There's now a single "tiff"
	target to build either shared or static as required, and all the
	tests and tools are linked with this. Note: the Windows tests fail
	when linked with a static libtiff (says: libtiff.dll not found).
	Not really a regression since this was not tested up to this
	point, and it's likely the unit tests haven't (ever?) been run on
	Windows with a static libtiff, so there's some additional
	portability issue here to address.  Works fine on UNIX systems,
	and fine on Windows with the default to build a DLL.

	Add a missing file which wasn't being distributed, causing unit
	tests to fail.  Note that "find . -name '*.cmake'" lists all the
	CMake files which need distributing in addition to all the
	CMakeLists.txt files (which now are distributed).

2015-08-31  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_predict.c: pedantic change to add explicit masking
	with 0xff before casting to uchar in floating-point horizontal
	differencing and accumulation routines.

2015-08-31  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_predict.c: fix generation of output with 16 bit
	or 32 bit integer, when byte swapping is needed, in
	horizontal predictor (#2521). Also fixes decoding when there is
	a single pixel to code (unlikely case...) and byte swapping is
	involved.

2015-08-30  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_lzw.c: make nextdata a unsigned type to avoid
	undefined behaviour with shifts (gcc -fsanitize=shift)

2015-08-30  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_fax3.c, libtiff/tif_lzw.c, libtiff/tif_predict.c:
	add explicit masking with 0xff before casting
	to unsigned char (make icc -check=conversions happy)

	* libtiff/tif_predict.c: operate on unsigned datatypes when
	computing/applying differences to avoid undefined behaviour of
	signed types (C standard compliance)

2015-08-30  Bob Friesenhahn  <<EMAIL>>

	* configure.ac: libtiff 4.0.5 released.

2015-08-29  Bob Friesenhahn  <<EMAIL>>

	* CMakeLists.txt: Applied patch by Roger Leigh (via tiff mailing
	list on 2015-08-29) to add ld-version-script option to cmake build
	to match autoconf.  Note: defaults to 'on' to be ABI-compatible by
	default with common Linux distribution builds.  Note that the
	autoconf configure script defaults to 'off'.

	* html/build.html: Applied patch by Roger Leigh (via tiff mailing
	list on 2015-08-29) to describe how to use CMake to build libtiff.

2015-08-28  Bob Friesenhahn  <<EMAIL>>

	* html/v4.0.5.html: Added HTML file describing the changes which
	will appear in the 4.0.5 release.

2015-08-23  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tiffiop.h: For MinGW comiles, make sure that build
	supports necessary __MSVCRT_VERSION__ (at least at least 0x800).
	Otherwise large files can not be supported for POSIX-style I/O.

	* tools/fax2tiff.c (main): Eliminate a compiler warning in 64-bit
	builds about cast to thandle_t.

	* test/rewrite_tag.c (main): Does not require any arguments.

2015-08-20  Bob Friesenhahn  <<EMAIL>>

	* tools/CMakeLists.txt, port/snprintf.c: Patch by Roger Leigh to
	fix build issues when using Cmake due to Windows large file
	changes.

2015-08-18  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tiffiop.h: First cut at supporting large files under
	Microsoft Windows using tif_unix.c and the libtiff tools.  This
	only works if the Windows CDK is new enough to support the APIs
	used (Visual C++ 2005 or later).  Support for large files is not
	actually tested yet.

2015-08-15  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_jpeg.c: Applied patch by Räisä Olli to assure that
	client_data is initialized to a known value, and to report an
	error on two memory allocation failures.

2015-08-13  Bob Friesenhahn  <<EMAIL>>

	* CMakeLists.txt: Applied patch by Roger Leigh to fix libtiffxx
	symbol versioning.  Patch was mailed to libtiff list on Thu, 13
	Aug 2015.

2015-07-04  Bob Friesenhahn  <<EMAIL>>

	* cmake: Add d suffix to debug libraries with MSVC.  Patch #3 of 3
	by Roger Leigh posted to tiff list on Wed, 1 Jul 2015 15:58:20
	+0100.

	* cmake: Add extra warning flags.  Patch #2 of 3 by Roger Leigh
	posted to tiff list on Wed, 1 Jul 2015 15:58:20 +0100.

	* cmake: Correct snprintf fallback for VS2015.  Patch #1 of 3 by
	Roger Leigh posted to tiff list on Wed, 1 Jul 2015 15:58:20 +0100.

2015-06-24  Bob Friesenhahn  <<EMAIL>>

	* CMakeLists.txt: Add CMake patchset by Roger Leigh as posted to
	libtiff mailing list on Mon, 22 Jun 2015 21:21:01 +0100. Several
	corrections to ensure that the autotools build still works were
	added by me.  I have not yet tested the build using 'cmake' or
	MSVC with 'nmake'.

2015-06-21  Bob Friesenhahn  <<EMAIL>>

	* test/Makefile.am: tiff2rgba-quad-tile.jpg.sh depends on the JPEG
	library so only execute if JPEG is available.

	* libtiff 4.0.4 released.

	* configure.ac: Add a HAVE_FOO Automake conditional for each
	add-on library.

	* test/Makefile.am (JPEG_DEPENDENT_CHECK_PROG): raw_decode
	requires JPEG support to compile.  Use Automake conditional to
	only include it when JPEG support is available.

	* html/build.html: Try to improve the nmake-based VC++ build
	description.

	* libtiff/tiffconf.vc.h: Build fixes based on testing.

	* libtiff/tif_config.vc.h: Build fixes based on testing.

	* libtiff/libtiff.def: TIFFRasterScanline does not exist so remove
	export for it.

2015-06-20  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_config.vc.h: Make adjustments to match the new
	definitions that configure produces, including for WIN64.  Still
	needs to be tested.

	* configure.ac: For 64-bit MinGW, fix SSIZE_FORMAT formatting
	specifier.  64-bit MinGW supports 'long long' but support for
	'lld' is not assured by the run-time DLLs and so GCC warns.
	Add TIFF_SIZE_T and TIFF_SIZE_FORMAT to provide a type definition
	and printf format specifier to deal with printing values of
	'size_t' type.  In particular, this was necessary for WIN64.
	Added a configure test for if the system headers provide 'optarg'
	(normal case) and block out the many explicit 'extern' statements
	in the utilities.  This was found to be necessary under Windows
	when getopt is in a DLL and the symbols are already imported with
	dllimport via standard header files.

	* test/raw_decode.c (XMD_H): Avoid conflicting typedefs for INT32
	and boolean in MinGW build due to including jpeglib.h.

	* test/rewrite_tag.c (main): Fix problem with location of variable
	declaration.

	* libtiff/libtiff.def: Added exports for TIFFGetConfiguredCODECs,
	TIFFReadRGBAImageOriented, TIFFSetCompressionScheme,
	TIFFSwabArrayOfTriples, TIFFVGetFieldDefaulted, _TIFFCheckRealloc,
	TIFFRasterScanline, TIFFSetErrorHandlerExt,
	TIFFSetWarningHandlerExt, TIFFNumberOfDirectories,
	TIFFCreateCustomDirectory, TIFFCreateEXIFDirectory,
	TIFFWriteCustomDirectory, _TIFFRewriteField as recommended by
	Roger Leigh and justified by use in libtiff tests, documentation,
	and changelog notes.  Also sorted symbol list and removed
	duplicate entries.

2015-06-16  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_getimage.c: Fix four Coverity issues related to
	unintended sign extension.

2015-06-16  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_unix.c: fix compilation with MSVC (fix by Jeff McKenna)

2015-06-14  Lee Howard  <<EMAIL>>

	* libtiff/tif_unix.c: contribution from Vadim Zeitlin on
	Bugzilla Bug #2510 fixes several harmless but still annoying
	warnings

	* configure: contribution from Ludolf Holzheid on Bugzilla
	Bug #2498.  Adds an option to select the file I/O style on
	Windows hosts.

	* libtiff/tif_getimage.c: contribution from Gary Cramblitt
	on Bugzilla Bug #2409.  Correct reading of certain tiled TIFFs.

	* configure, configure.ac: contribution from Marcos H. Woehrmann
	on Bugzilla Bug #2405.  Correct shell equality operator.

	* tools/tiffgt.c (raster_draw): contribution from Jay Berkenbilt
	on Bugzilla Bug #2401.  Appropriately call glFlush().

	* tools/tiff2pdf.c: change ColorTransform from "0" to "1"
	following Bugzilla Bug #2150.

2015-06-13  Lee Howard  <<EMAIL>>

	* libtiff/tif_lzw.c: contribution from Andy Cave - decode
	files that contain consecutive CODE_CLEAR codes.

	* tools/tiff2pdf.c: contribution from Antti S. Lankila on
	Bugzilla Bug #2078. Suppress initial output of the header.

	* tools/tiff2pdf.c: contribution from Yuriy M. Kaminskiy -
	Take care in using the return value from snprintf().

	* tools/tiffcrop.c: contribution from Eduardo Robles Elvira -
	correctly copy the compression tag from the source TIFF.

	* tools/tiff2ps.c: contribution from Eduardo Robles Elvira -
	correct sizing and scaling problems with output document.

2015-06-10  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_jpeg.c (JPEGDecode): Split JPEGDecode() into two
	clean implementations in order to avoid pre-processor hell.  Only
	one of the implementations is used in a given build.

2015-06-08  Even Rouault <even.rouault at spatialys.com>

	* libtiff/tif_jpeg.c: Fix compilation in BITS_IN_JSAMPLE == 12
	case

2015-06-07  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_write.c (TIFFWriteEncodedStrip): Fix Coverity 715975
	"Division or modulo by zero".
	(TIFFWriteEncodedTile): Fix Coverity 715976 and 715977 "Division
	or modulo by zero".
	(TIFFWriteRawStrip): Fix Coverity 715978 "Division or modulo by
	zero".
	(TIFFWriteScanline): Fix Coverity 715979 "Division or modulo by
	zero".

	* libtiff/tif_read.c (TIFFStartTile): Fix Coverity 715973 and
	715974 "Division or modulo by zero".

2015-05-31  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_dir.c (TIFFNumberOfDirectories): Quiet Coverity
	1134470 "Logically dead code" by making the roll-over check
	explicit.

	* libtiff/tif_luv.c (LogLuvDecodeTile): Fix Coverity 991227
	"Division or modulo by zero".
	(LogLuvDecodeStrip): Fix Coverity 991239 "Division or modulo by
	zero".
	(LogLuvEncodeStrip): Fix Coverity 991240 "Division or modulo by
	zero".
	(LogLuvEncodeTile): Fix Coverity 991241 "Division or modulo by
	zero".

	* libtiff/tif_dirread.c (TIFFReadDirEntryDoubleArray): Fix
	Coverity 298626 "Logically dead code".
	(TIFFReadDirEntryFloatArray): Fix Coverity 298627 "Logically dead
	code".
	(TIFFReadDirEntryIfd8Array): Fix Coverity 298628 "Logically dead
	code".
	(TIFFReadDirEntrySlong8Array): Fix Coverity 298629 "Logically dead
	code"

	* libtiff/tif_dir.c (TIFFNumberOfDirectories): Don't depend on ++
	operator precedenc in evaluation.  Might quench Coverity 1134470
	"Logically dead code".

	* libtiff/tif_jpeg.c (JPEGDecode): Fix Coverity 602597 "Operands
	don't affect result".  This change uses ifdefs to include
	applicable code based on properties of libjpeg.  Still needs to be
	re-tested with 12-bit "6b" and "MK1".

2015-05-30  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_dirwrite.c (_TIFFRewriteField): Fix Coverity 1024310
	"Resource leak".

	* libtiff/tif_ojpeg.c (OJPEGReadHeaderInfoSecStreamDht): Fix
	Coverity 601720 "Resource leak".

	* libtiff/tif_jpeg.c (JPEGCleanup): Fix Coverity 298624
	"Dereference before null check".

	* libtiff/tif_ojpeg.c (OJPEGReadBufferFill): Fix Coverity 603400
	"Missing break in switch".

	* contrib/addtiffo/tif_overview.c (TIFF_DownSample): Check buffer
	size calculation for overflow.

	* contrib/addtiffo/addtiffo.c (main): Possibly address Coverity
	1024226 "Untrusted value as argument".

	* tools/gif2tiff.c (readgifimage): Fix Coverity 1024222 "Untrusted
	value as argument".
	(checksignature): Fix Coverity 1024894 "Ignoring number of bytes
	read".
	(readextension): Fix Coverity 1024893 "Ignoring number of bytes
	read".
	(readgifimage): Fix Coverity 1024890 "Ignoring number of bytes
	read".
	(readraster): Fix Coverity 1024891 "Ignoring number of bytes
	read".
	(readgifimage): Fix Coverity 1024892 "Ignoring number of bytes
	read".

	* tools/tiff2pdf.c (t2p_readwrite_pdf_image): Fix Coverity 1024181
	"Structurally dead code".

	* tools/raw2tiff.c (main): Fix Coverity 1024887 "Unchecked return
	value from library".
	(guessSize): Fix Coverity 1024888 "Unchecked return value from
	library".
	(guessSize): Fix Coverity 1214162 "Ignoring number of bytes read".
	(guessSize): Fix Coverity 1024889 "Unchecked return value from
	library".

	* tools/tiff2pdf.c (t2p_readwrite_pdf_image): Fix Coverity 298621
	"Resource leak".
	(t2p_readwrite_pdf_image): Fix Coverity 1024181 "Structurally dead
	code".
	(t2p_write_pdf): Fix Coverity 1227690 "Unused value".

2015-05-29  Bob Friesenhahn  <<EMAIL>>

	* contrib/iptcutil/iptcutil.c (formatIPTC): Fix Coverity 1024468
	"Infinite loop".
	(formatIPTC): Fix Coverity 1024727 "Truncated stdio return value".
	(formatIPTC): Fix Coverity 1214240 "Untrusted loop bound".

2015-05-28  Bob Friesenhahn  <<EMAIL>>

	* contrib/addtiffo/tif_ovrcache.c (TIFFCreateOvrCache): Fix
	Coverity 298615 "Resource leak".
	(TIFFGetOvrBlock): Fix Coverity 1024649 "Unintended sign
	extension".

	* tools/bmp2tiff.c (main): Fix Coverity 1024225 "Untrusted value
	as argument".
	(main): Fix Coverity 1024678 "Unchecked return value from
	library".
	(main): Fix Coverity 1024679 "Unchecked return value from
	library".
	(main): Fix Coverity 1214160 "Ignoring number of bytes read".

	* contrib/addtiffo/tif_ovrcache.c (TIFFCreateOvrCache): Fix
	Coverity 298615 "Resource leak".

	* tools/tiffcp.c: Fix Coverity 1024306, 1024307, 1024308, 1024309
	"Resource leak".

	* tools/tiffsplit.c (cpTiles): Fix Coverity 1024304 "Resource
	leak".
	(cpStrips): Fix Coverity 1024305 "Resource leak".

2015-05-27  Bob Friesenhahn  <<EMAIL>>

	* tools/ras2tiff.c: Fix Sun Raster header definition to be safe
	for 64-bit systems.  Add some header validations.  Should fix many
	Coverity issues.
	(main): Fix Coverity 1301206: "Integer handling issues  (BAD_SHIFT)".
	(main): Quiet Coverity 1024223 "Untrusted value as argument".

	* tools/tiffmedian.c (GetInputLine): Fix Coverity 1024795 "Nesting
	level does not match indentation".
	(get_histogram): Quiet Coverity 1024386 "Out-of-bounds read".
	This was a benign mis-diagnosis but added code to enforce against
	buffer overflow.

	* tools/tiffcrop.c (ROTATE_ANY): Fix Coverity 1294542 "Logical
	vs. bitwise operator".
	(readContigStripsIntoBuffer): Fix Coverity 1024545 "Division or
	modulo by zero".
	(readContigTilesIntoBuffer): Fix Coverity 1024586 "Logically dead
	code".
	(writeSingleSection): Fix Coverity 1024796 "Nesting level does not
	match indentation".
	(writeCroppedImage): Fix Coverity 1024797 "Nesting level does not
	match indentation".
	(loadImage): Fix Coverity 1299741 "Dereference before null check".
	(loadImage): Fix Coverity 1299740 "Out-of-bounds write".

2015-03-02  Even Rouault  <<EMAIL>>

	* tools/tiffdither.c: check memory allocations to avoid writing to
	NULL pointer. Also check multiplication overflow. Fixes #2501,
	CVE-2014-8128. Derived from patch by Petr Gajdos.

2015-01-26  Even Rouault  <<EMAIL>>

	* add html/v4.0.4beta.html under version control
	* HOWTO-RELEASE: write that cvs add html/vX.X.html must be used

2015-01-26  Even Rouault  <<EMAIL>>

	* libtiff 4.0.4beta released

2015-01-26  Even Rouault  <<EMAIL>>

	* automake: updated to 1.15
	* libtool: updated to 2.4.5

2015-01-22  Even Rouault  <<EMAIL>>

	* tools/tiff2pdf.c: Fix two crashes (oCERT-2014-013)

2015-01-05  Frank Warmerdam  <<EMAIL>>

	* html/bugs.html: remove note about needing to email the tiff mailing
	list administrator about being approved for membership, this appears
	not to be true.

2015-01-05  Olivier Paquet  <<EMAIL>>

	* tools/tiff2pdf.c: Fixed unsigned integer addition overflow detection.

2015-01-03  Even Rouault  <<EMAIL>>

	* libtiff/tif_dirread.c: in TIFFCheckDirOffset(), avoid uint16 overflow
	when reading more than 65535 directories, and effectively error out when
	reaching that limit.

2014-12-29  Even Rouault  <<EMAIL>>

	* libtiff/tif_jpeg.c: in JPEGFixupTags(), recognize SOF2, SOF9 and SOF10
	markers to avoid emitting a warning (even if, according to the TechNote,
	there are admittedly unusual/not recommended or even forbidden variants, but
	they do work well with libjpeg for SOF2, and with libjpeg-turbo for SOF2,
	SOF9 and SOF10).
	Define in_color_space and input_components to the right values in
	JPEGSetupEncode(), before calling jpeg_set_defaults(), as specified by
	libjpeg API documentation, so as to be compatible with mozjpeg library.
	Note: the default settings of mozjpeg will produce progressive scans, which
	is forbidden by the TechNote.

2014-12-29  Even Rouault  <<EMAIL>>

	* libtiff/tif_getimage.c: move test on vertical value of YCbCr subsampling.
	to avoid buffer leak (fix previous fix, found by Coverity scan)

2014-12-29  Even Rouault  <<EMAIL>>

	* libtiff/tif_next.c: add new tests to check that we don't read outside of
	the compressed input stream buffer.

	* libtiff/tif_getimage.c: in OJPEG case, fix checks on strile width/height
    in the putcontig8bitYCbCr42tile, putcontig8bitYCbCr41tile and
    putcontig8bitYCbCr21tile cases.

2014-12-27  Even Rouault  <<EMAIL>>

	* libtiff/tif_dir.c: in TIFFDefaultDirectory(), reset any already existing
	extented tags installed by user code through the extender mechaninm before
	calling the extender callback (GDAL #5054)

2014-12-26  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffcrop.c: Fix warnings about variables set but not used.

	* contrib/iptcutil/iptcutil.c: Fix warnings about variables set
	but not used.

	* tools/tiffgt.c: Fix warnings about unused parameters.

	* libtiff/tif_stream.cxx: Fix warnings about unused parameters.

2014-12-25  Even Rouault  <<EMAIL>>

	* libtiff/tif_getimage.c, libtiff/tif_ojpeg.c, libtiff/tif_zip.c: fix
	various typos found by Debian lintian tool (GDAL #5756)

2014-12-24  Even Rouault  <<EMAIL>>

	* libtiff/tif_getimage.c: avoid divide by zero on invalid YCbCr subsampling.
	http://bugzilla.maptools.org/show_bug.cgi?id=2235

2014-12-24  Even Rouault  <<EMAIL>>

	* tools/tiff2pdf.c: fix buffer overflow on some YCbCr JPEG compressed images.
	http://bugzilla.maptools.org/show_bug.cgi?id=2445

2014-12-24  Even Rouault  <<EMAIL>>

	* tools/tiff2pdf.c: fix buffer overflow on YCbCr JPEG compressed image.
	Derived from patch by Petr Gajdos,
	http://bugzilla.maptools.org/show_bug.cgi?id=2443

2014-12-23  Even Rouault  <<EMAIL>>

	* libtiff/tif_dirread.c: In EstimateStripByteCounts(), check return code
	of _TIFFFillStriles(). This solves crashing bug on corrupted
	images generated by afl.

2014-12-23  Even Rouault  <<EMAIL>>

	* libtiff/tif_read.c: fix several invalid comparisons of a uint64 value with
	<= 0 by casting it to int64 first. This solves crashing bug on corrupted
	images generated by afl.

2014-12-21  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffdump.c: Guard against arithmetic overflow when
	calculating allocation buffer sizes.

2014-12-21  Even Rouault  <<EMAIL>>

	* tools/tiff2bw.c: when Photometric=RGB, the utility only works if
	SamplesPerPixel = 3. Enforce that
	http://bugzilla.maptools.org/show_bug.cgi?id=2485 (CVE-2014-8127)

2014-12-21  Even Rouault  <<EMAIL>>

	* tools/pal2rgb.c, tools/thumbnail.c: fix crash by disabling TIFFTAG_INKNAMES
	copying. The right fix would be to properly copy it, but not worth the burden
	for those esoteric utilities.
	http://bugzilla.maptools.org/show_bug.cgi?id=2484 (CVE-2014-8127)

2014-12-21  Even Rouault  <<EMAIL>>

	* tools/thumbnail.c: fix out-of-buffer write
	http://bugzilla.maptools.org/show_bug.cgi?id=2489 (CVE-2014-8128)

2014-12-21  Even Rouault  <<EMAIL>>

	* tools/thumbnail.c, tools/tiffcmp.c: only read/write TIFFTAG_GROUP3OPTIONS
	or TIFFTAG_GROUP4OPTIONS if compression is COMPRESSION_CCITTFAX3 or
	COMPRESSION_CCITTFAX4
	http://bugzilla.maptools.org/show_bug.cgi?id=2493 (CVE-2014-8128)

2014-12-21  Even Rouault  <<EMAIL>>

	* libtiff/tif_next.c: check that BitsPerSample = 2. Fixes
	http://bugzilla.maptools.org/show_bug.cgi?id=2487 (CVE-2014-8129)

2014-12-21  Even Rouault  <<EMAIL>>

	* tools/tiff2pdf.c: check return code of TIFFGetField() when reading
	TIFFTAG_SAMPLESPERPIXEL

2014-12-21  Even Rouault  <<EMAIL>>

	* tools/tiffcp.c: fix crash when converting YCbCr JPEG-compressed to none.
	Based on patch by Tomasz Buchert (http://bugzilla.maptools.org/show_bug.cgi?id=2480)
	Description: fix for Debian bug #741451
	tiffcp crashes when converting JPEG-encoded TIFF to a different
	encoding (like none or lzw). For example this will probably fail:
	tiffcp -c none jpeg_encoded_file.tif output.tif
	The reason is that when the input file contains JPEG data,
	the tiffcp code forces conversion to RGB space. However,
	the output normally inherits YCbCr subsampling parameters
	from the input, which leads to a smaller working buffer
	than necessary. The buffer is subsequently overrun inside
	cpStripToTile() (called from writeBufferToContigTiles).
	Note that the resulting TIFF file would be scrambled even
	if tiffcp wouldn't crash, since the output file would contain
	RGB data intepreted as subsampled YCbCr values.
	This patch fixes the problem by forcing RGB space on the output
	TIF if the input is JPEG-encoded and output is *not* JPEG-encoded.
	Author: Tomasz Buchert <<EMAIL>>

2014-12-21  Even Rouault  <<EMAIL>>

	Fix various crasher bugs on fuzzed images.
	* libtiff/tif_dir.c: TIFFSetField(): refuse to set negative values for
	TIFFTAG_XRESOLUTION and TIFFTAG_YRESOLUTION that cause asserts when writing
	the directory
	* libtiff/tif_dirread.c: TIFFReadDirectory(): refuse to read ColorMap or
	TransferFunction if BitsPerSample has not yet been read, otherwise reading
	it later will cause user code to crash if BitsPerSample > 1
	* libtiff/tif_getimage.c: TIFFRGBAImageOK(): return FALSE if LOGLUV with
	SamplesPerPixel != 3, or if CIELAB with SamplesPerPixel != 3 or BitsPerSample != 8
	* libtiff/tif_next.c: in the "run mode", use tilewidth for tiled images
	instead of imagewidth to avoid crash
	* tools/bmp2tiff.c: fix crash due to int overflow related to input BMP dimensions
	* tools/tiff2pdf.c: fix crash due to invalid tile count (should likely be checked by
	libtiff too). Detect invalid settings of BitsPerSample/SamplesPerPixel for CIELAB / ITULAB
	* tools/tiffcrop.c: fix crash due to invalid TileWidth/TileHeight
	* tools/tiffdump.c: fix crash due to overflow of entry count.

2014-12-15  Even Rouault  <<EMAIL>>

	* libtiff/tif_jpeg.c: Fix regression introduced on 2010-05-07 that caused
	all tiles/strips to include quantization tables even when the jpegtablesmode
	had the JPEGTABLESMODE_QUANT bit set.
	Also add explicit removal of Huffman tables when jpegtablesmode has the
	JPEGTABLESMODE_HUFF bit set, which avoids Huffman tables to be emitted in the
	first tile/strip (only useful in update scenarios. create-only was
	fine)

2014-12-09  Bob Friesenhahn  <<EMAIL>>

	* tools/tiff2pdf.c: Assure that memory size calculations for
	_TIFFmalloc() do not overflow the range of tmsize_t.

2014-12-07  Even Rouault  <<EMAIL>>

	* tools/thumbnail.c, tools/tiffcrop.c: "fix" heap read over-run found with
	Valgrind and Address Sanitizer on test suite

2014-12-07  Bob Friesenhahn  <<EMAIL>>

	* tools/tiff2pdf.c (t2p_read_tiff_init): TIFFTAG_TRANSFERFUNCTION
	tag can return one channel, with the other two channels set to
	NULL.  The tiff2pdf code was expecting that other two channels
	were duplicate pointers in the case where there is only one
	channel.  Detect this condition in order to avoid a crash, and
	presumably perform correctly with just one channel.

2014-12-06  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffdump.c: Fix double-free bug.

2014-11-27  Even Rouault  <<EMAIL>>

	* libtiff/tif_config.vc.h: no longer use "#define snprintf _snprintf" with
	Visual Studio 2015 aka VC 14 aka MSVC 1900

2014-11-20  Even Rouault  <<EMAIL>>

	* libtiff/tif_lzw.c: prevent potential null dereference of
	sp->dec_codetab in LZWPreDecode (bug #2459)

	* libtiff/tif_read.c: in TIFFReadBufferSetup(), avoid passing -1 size
	to TIFFmalloc() if passed user buffer size is 0 (bug #2459)

	* libtiff/tif_ojpeg.c: make Coverity happier (not a bug, #2459)

	* libtiff/tif_dir.c: in _TIFFVGetField() and _TIFFVSetField(), make
	Coverity happier (not a bug, #2459)

	* libtiff/tif_dirread.c: in TIFFFetchNormalTag(), make Coverity happier
	(not a bug, #2459)

	* tools/tiff2pdf.c: close PDF file (bug #2479)

	* tools/fax2ps.c: check malloc()/realloc() result (bug #2470)

	* tools/tiffdump.c: detect cycle in TIFF directory chaining (bug #2463)
	and avoid passing a NULL pointer to read() if seek() failed before (bug #2459)

	* tools/tiffcrop.c: fix segfault if bad value passed to -Z option
	(bug #2459) and add missing va_end in dump_info (#2459)

	* tools/gif2tif.c: apply patch for CVE-2013-4243 (#2451)

2014-11-20  Even Rouault  <<EMAIL>>
	* libtiff/tif_jpeg.c: fix segfault in JPEGFixupTagsSubsampling() on
	corrupted image where tif->tif_dir.td_stripoffset == NULL (bug #2471)

2014-11-20  Even Rouault  <<EMAIL>>
	* automake: updated to 1.14.1
	* libtool: updated to 2.4.3
	* HOWTO-RELEASE: small update about autotools building order

2014-10-20  Olivier Paquet  <<EMAIL>>
	* tools/tiff2pdf.c: Preserve input file directory order when pages
	are tagged with the same page number.

2014-08-31  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_dirread.c (TIFFReadDirEntryOutputErr): Incorrect
	count for tag should be a warning rather than an error since
	errors terminate processing.

2014-06-07  Bob Friesenhahn  <<EMAIL>>

	* tools/tiff2rgba.c (]): Fixed tiff2rgba usage message in that zip
	was wrongly described.  Fix suggested by Miguel Medalha.

2014-05-06  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_dirinfo.c (TIFFField) : Fix data type for
	TIFFTAG_GLOBALPARAMETERSIFD tag.  Patch by Steve Underwood.
	Reviewed and forwarded by Lee Howard.

2013-11-30  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dir.c: fix last fix for TIFFNumberOfDirectories()

2013-10-21  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dir.c: generate error in case of directory count
	overflow.

2013-10-01  Frank Warmerdam  <<EMAIL>>

	* libtiff/tiff.h, libtiff/tif_dirinfo.c: add definitions for
	TIFF/EP CFARepeatPatternDim and CFAPattern tags (bug #2457)

2013-09-12  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_dir.c (TIFFAdvanceDirectory): If nextdir is found to
	be defective, then set it to zero before returning error in order
	to terminate processing of truncated TIFF.  Issue found and fix
	suggested by Richard Nolde.

2013-08-14  Frank Warmerdam  <<EMAIL>>

	* tools/gif2tiff.c: fix possible OOB write (#2452, CVE-2013-4244)

2013-08-13  Frank Warmerdam  <<EMAIL>>

	* tools/gif2tiff.c: Be more careful about corrupt or
	hostile input files (#2450, CVE-2013-4231)

	* tools/tiff2pdf.c: terminate after failure of allocating
	ycbcr buffer (bug #2449, CVE-2013-4232)

2013-07-09  Frank Warmerdam  <<EMAIL>>

	* tools/tiffinfo.c: Default various values fetched with
	TIFFGetField() to avoid being uninitialized.

2013-05-02  Tom Lane  <<EMAIL>>

	* tools/tiff2pdf.c: Rewrite JPEG marker parsing in
	t2p_process_jpeg_strip to be at least marginally competent.  The
	approach is still fundamentally flawed, but at least now it won't
	stomp all over memory when given bogus input.  Fixes CVE-2013-1960.

2013-05-02  Tom Lane  <<EMAIL>>

	* contrib/dbs/xtiff/xtiff.c, libtiff/tif_codec.c,
 	libtiff/tif_dirinfo.c, tools/rgb2ycbcr.c, tools/tiff2bw.c,
 	tools/tiff2pdf.c, tools/tiff2ps.c, tools/tiffcrop.c,
 	tools/tiffdither.c: Enlarge some fixed-size buffers that weren't
 	large enough, and eliminate substantially all uses of sprintf(buf,
 	...)  in favor of using snprintf(buf, sizeof(buf), ...), so as to
 	protect against overflow of fixed-size buffers.  This responds in
 	particular to CVE-2013-1961 concerning overflow in tiff2pdf.c's
 	t2p_write_pdf_page(), but in general it seems like a good idea to
 	deprecate use of sprintf().

2013-03-29  Bob Friesenhahn  <<EMAIL>>

	* configure.ac: Applied patch by Brad Smith to improve pkg-config
	static linking by adding -lm to Libs.private when needed.

2013-03-05  Tom Lane  <<EMAIL>>

	* html/man/tiff2ps.1.html, html/man/tiffcp.1.html,
 	html/man/tiffdither.1.html, man/tiff2ps.1, man/tiffcp.1,
 	man/tiffdither.1, tools/tiff2ps.c, tools/tiffcp.c,
 	tools/tiffdither.c: Sync tool usage printouts and man pages with
 	reality (quite a few options had escaped being documented in one
 	or both places).  Per an old report from Miroslav Vadkerti.

2013-01-25  Bob Friesenhahn  <<EMAIL>>

	* tools/tiff2ps.c:Fix bug in auto rotate option code. Once a
	rotation angle was set by the auto rotate check, it was retained
	for all pages that followed instead ofa being retested for each
	page.  Patch by Richard Nolde.

2013-01-18  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_write.c: tmsize_t related casting warning fixed for
	64bit linux.

	* libtiff/tif_read.c: uint64/tmsize_t change for MSVC warnings.
	http://bugzilla.maptools.org/show_bug.cgi?id=2427

2012-12-20  Tom Lane  <<EMAIL>>

	* test/raw_decode.c: Relax raw_decode's pixel-value checks so that
	it will pass with more versions of libjpeg.  (There are at least
	three in active use now, and JPEG_LIB_VERSION doesn't tell us
	enough to uniquely identify expected results.)

2012-12-12  Tom Lane  <<EMAIL>>

	* libtiff/tif_print.c: Fix TIFFPrintDirectory's handling of
	field_passcount fields: it had the TIFF_VARIABLE and
	TIFF_VARIABLE2 cases backwards.

2012-12-10  Tom Lane  <<EMAIL>>

	* tools/ppm2tiff.c: Improve previous patch for CVE-2012-4564:
 	check the linebytes calculation too, get the max() calculation
 	straight, avoid redundant error messages, check for malloc
 	failure.

2012-12-10  Tom Lane  <<EMAIL>>

	* libtiff/tif_pixarlog.c: Improve previous patch for CVE-2012-4447
 	(to enlarge tbuf for possible partial stride at end) so that
 	overflow in the integer addition is detected.  Per gripe from
 	Huzaifa Sidhpurwala.

2012-12-03  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffset.c: tiffset now supports a -u option to unset a
	tag.  Patch by Zach Baker. See
	http://bugzilla.maptools.org/show_bug.cgi?id=2419

2012-11-18  Bob Friesenhahn  <<EMAIL>>

	* automake: Update Automake to 1.12.5 release.

	* libtiff/tif_{unix,vms,win32}.c (_TIFFmalloc): ANSI C does not
	require malloc() to return NULL pointer if requested allocation
	size is zero.  Assure that _TIFFmalloc does.

2012-11-01  Frank Warmerdam  <<EMAIL>>

	* tools/ppm2tiff.c: avoid zero size buffer vulnerability.
	CVE-2012-4564 - Thanks to Huzaifa Sidhpurwala of the
	Red Hat Security Response team for the fix.

2012-10-18  Frank Warmerdam  <<EMAIL>>

	* tif_zip.c: Avoid crash on NULL error messages.

2012-09-22  Bob Friesenhahn  <<EMAIL>>

	* libtiff 4.0.3 released.

2012-09-20  Bob Friesenhahn  <<EMAIL>>

	* Makefile.am: Update to Automake 1.12.4

2012-08-19  Bob Friesenhahn  <<EMAIL>>

	* Makefile.in: Update to Automake 1.12.3

	* libtiff{tiff.h, tif_print.c, tif_dirinfo.c, tif_dirread.c}: Add
	some TIFF/FX support in libtiff.  Add the tag definitions to
	tiff.h.  Add the related TIFF field definitions to tif_dirinfo.c,
	and also fixes an error in a comment.  Adds the photometric values
	to tif_print.c, and fixes a bug.  These changes are by Steve
	Underwood.

2012-08-13  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_write.c: Fix bug rewriting image tiles in a
	compressed file: http://trac.osgeo.org/gdal/ticket/4771

2012-08-02  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirread.c: report error in case of mismatch value
	counts for tags (ie. DotRange).

2012-07-26  Tom Lane  <<EMAIL>>

	* libtiff/{tiffio.h, tif_dirinfo.c, libtiff.def}: Add six new
 	functions TIFFFieldTag(), TIFFFieldName(), TIFFFieldDataType(),
	TIFFFieldPassCount(), TIFFFieldReadCount(), TIFFFieldWriteCount()
	as external accessors for the opaque type TIFFField.

	* tools/tiffset.c: Make tiffset use the above functions instead of
	relying on library private headers.

2012-07-19  Tom Lane  <<EMAIL>>

	* tools/tiff2pdf.c: Fix two places where t2p_error didn't get set
	after a malloc failure.  No crash risk AFAICS, but the program
	might not report exit code 1 as desired.  h/t <EMAIL>

2012-07-18  Tom Lane  <<EMAIL>>

	* tools/tiff2pdf.c: Fail when TIFFSetDirectory() fails.  This
	prevents core dumps or perhaps even arbitrary code execution when
	processing a corrupt input file (CVE-2012-3401).

2012-07-06  Bob Friesenhahn  <<EMAIL>>

	* test/raw_decode.c (main): Test fixes to work with IJG JPEG 7+.
	IJG JPEG 7+ uses a different upsampling algorithm which produces
	different numeric results.

	* libtiff/tif_jpeg.c (JPEGPreDecode): Patch from Even Rouault to
	work with IJG JPEG 7+.

2012-07-04  Bob Friesenhahn  <<EMAIL>>

	* test/raw_decode.c: Add changes so that test can run with build
	directory outside of source directory.

2012-07-02  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: Fix handling when writing RGBA jpeg compressed
	imagery (http://trac.osgeo.org/gdal/ticket/4732)

2012-06-20  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_fax3.c: fix memory initialization of runs, only
	partly done.

	* libtiff/tif_pixarlog.c: Make sure tbuf is large enough for one
	full "stride" past the end.

2012-06-19  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_packbits.c: fix read past end of data buffer.

2012-06-15  Frank Warmerdam  <<EMAIL>>

	*  libtiff 4.0.2 released.

	* tools/tif2pdf.c, tools/tifdump.c: avoid unitialized variable
	warnings with clang.

2012-06-15  Tom Lane  <<EMAIL>>

	* tools/tiff2pdf.c: Defend against integer overflows while
	calculating required buffer sizes (CVE-2012-2113).

2012-06-12  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_print.c: Be careful about printing corrupt inknames.

	* libtiff/tif_fax3.c: Ensure runs array is initialized to zeros.

2012-06-07  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_print.c: avoid pretty printing other fields when
	we don't have the proper amount and type of data or if the field
	is actually autodefined.

2012-06-05  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_tile.c, libtiff/tif_strip.c: Ensure that illegal
	ycbcrsubsampling values result in a runtime error, not just an
	assertion.

	* tests/custom_dir.c: Add testing of EXIF and custom directory
	reading and writing.

	* libtiff/tif_dir.c, libtiff/tiffio.h: Add TIFFCreateCustomDirectory()
	and TIFFCreateEXIFDirectory() functions.

	* libtiff/tif_dir.c, tif_print.c : Remove FIELD_CUSTOM handling for
	PAGENUMBER, HALFTONEHINTS, and YCBCRSUBSAMPLING.  Implement DOTRANGE
	differently.  This is to avoid using special TIFFGetField/TIFFSetField
	rules for these fields in non-image directories (like EXIF).

2012-06-04  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: Remove code for fixing up h_sampling and v_sampling
	in JPEGPreDecode().  If a fixup will be done it needs to be done sooner
	in JPEGFixupTagsSubsampling() or else buffer sized may be wrong.

2012-06-01  Frank Warmerdam  <<EMAIL>>

	* tools/tiffinfo.c: Do not try to read image data in EXIF directories.

	* libtiff/tif_getimage.c: added support for _SEPARATED CMYK images.
	http://bugzilla.maptools.org/show_bug.cgi?id=2379

	* libtiff/tif_unix.c: use strerror() to return a more specific error message
	on failed open.
	http://bugzilla.maptools.org/show_bug.cgi?id=2341

	* libtiff/tif_jpeg.c: Fix JPEGDecodeRaw() bugs.
	http://bugzilla.maptools.org/show_bug.cgi?id=2386

	* tests/decode_raw.c, tests/images/quad-tile.jpg.tiff: add limited support
	for testing jpeg in tiff image decoding including the "raw" decode interface.

2012-05-31  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: avoid overrunning the end of the output buffer in
	JPEGDecodeRaw() - mostly likely to occur when there is confusion about
	sampling values.

	* libtiff/tif_read.c: Make sure tif_rawdatasize is cleared when tif_rawdata is freed.

	* libtiff/tif_getimage.c: Add support for greyscale+alpha c/o Jérémie Laval.
	http://bugzilla.maptools.org/show_bug.cgi?id=2398

2012-05-29  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dir.c: avoid using specific set/get logic to process fields in custom directories,
	like EXIF directories.  This fixes problems like a tag "320" existing in a custom directory getting
	processed as if it were a colormap when it isn't really.  Damn the wide variety of argument formulations
	to get/set functions for different tags!

	* libtiff/tif_dir.c: Ensure that we keep track of when tif_rawdata
	is a pointer into an mmap()ed file via TIFF_BUFFERMMAP flag.

2012-05-24  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_pixarlog.c: Allocate working buffer one word larger since we "forward
	accumulate" and overwrite the end by one word in at least some cases.

2012-05-23  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_pixarlog.c: avoid accessing out of the lookup arrays for out of range inputs.

	* tools/tiffinfo.c: initialize h=0 to avoid undefined variable for degenerate files.

	* libtiff/tif_ojpeg.c: if OJPEGWriteHeader() fails once do not bother trying again on
	the same image.

	* libtiff/tif_ojpeg.c: make things more resilient in the face of files without
	stripbytecounts or stripoffsets or where loading these fails.

	* libtiff/tif_print.c: be careful about whether min/max values are singular
	or one per sample.

	* libtiff/tif_print.c: Avoid confusion about count size when printing custom fields.
	May affect things like ISOSpeedRatings.

	* libtiff/tif_dir.c: avoid one byte past end of ink names reading
	in some cases.

2012-05-19  Bob Friesenhahn  <<EMAIL>>

	* man/TIFFGetField.3tiff: Correct the 'count' field type in the
	example for how to retrieve the value of unsupported tags.

2012-03-30  Frank Warmerdam  <<EMAIL>>

	* tif_getimage.c: Fix size overflow (zdi-can-1221,CVE-2012-1173)
	care of Tom Lane @ Red Hat.

2012-02-18  Bob Friesenhahn  <<EMAIL>>

	* libtiff 4.0.1 released.

	* Update automake used to 1.11.3.

	* libtiff/tiffio.h: Use double-underbar syntax in GCC printf
	attribute specification to lessen the risk of accidental macro
	substitution.  Patch from Vincent Torri.

2012-01-31  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dir.c, libtiff/tif_dirread.c: Extra caution around
	assumption tag fetching is always successful.

	* libtiff/tif_jpeg.c: Extra caution for case where sp is NULL.

2012-01-22  Bob Friesenhahn  <<EMAIL>>

	* configure.ac: Add support for using library symbol versioning on
	ELF systems with the GNU linker.  Support is enabled via
	--enable-ld-version-script.  Disabled by default for now until
	there is a decision for how to deploy a libtiff with versioned
	symbols after libtiff 4.0.0 was already released.

2011-12-22  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_win32.c: Eliminate some minor 64-bit warnings in

	tif_win32.c.  Patch by Edward Lam.

	* configure.ac: Add libtiff private dependency on -llzma for
	pkg-config.  Patch by Mark Brand.
	Updated Automake to 1.11.2.

2011-12-21  Bob Friesenhahn  <<EMAIL>>

	* libtiff 4.0.0 released.

2011-12-08  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirread.c, libtiff/tif_read.c: more cautious checking
	of _TIFFFillStriles() results (#gdal 4372)

2011-12-07  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirread.c: fixes to deal with invalid files where
	_TIFFFillStriles() fails, and we try to chop up strips (gdal #4372)

	* libtiff/tif_dirread.c: fix error reporting when there is no
	tag information struct and name (gdal #4373)

2011-10-22  Bob Friesenhahn  <<EMAIL>>

	* Update GNU libtool to 2.4.2.

	* tools/tiffsplit.c (tiffcp): TIFFGetField count field should be
	uint32 type for TIFFTAG_JPEGTABLES.  Patch by Christophe
	Deroulers.

2011-06-21  Frank Warmerdam  <<EMAIL>>

	* libtiff/libtiff.def: Restore TIFFMergeFieldInfo.

2011-05-31  Jim Meyering  <<EMAIL>>

	* libtiff/tif_dirread.c (TIFFFetchStripThing): Free "data" also
	upon failure to allocate "resizeddata".
	* tools/tiff2ps.c (PSDataBW): Zero buffer *after* checking for
	allocation failure, not before.
	* libtiff/tif_ojpeg.c: plug leaks on OJPEG read failure path
	* tools/rgb2ycbcr.c (cvtRaster): unchecked malloc
	* libtiff/tif_jpeg.c, tools/tiff2pdf.c, tools/tiff2ps.c: mark
	NULL-deref and possible overflow
	* tools/tiff2pdf.c: remove decl+set of set-but-not-used local, "written"
	* libtiff/tif_jpeg.c (JPEGInitializeLibJPEG): Remove declaration
	and set of otherwise unused local, data_is_empty.
	* libtiff/tif_jpeg.c (JPEGDecodeRaw) [JPEG_LIB_MK1_OR_12BIT]:
	Diagnose out-of-memory failure and return 0 rather than
	dereferencing NULL.

2011-05-24  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirread.c: produce special error message for zero tag
	directories instead of error out on the malloc(0) failure.

2011-05-16  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirinfo.c: Restore TIFFMergeFieldInfo() and
	related declarations as they are in active use by libraries
	such as libgeotiff, and work just fine.  (#2315)

2011-04-20  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirinfo.c,tiffio.h: Remove the obsolete
	TIFFMergeFieldInfo/TIFFFindFieldInfo/TIFFFindFieldInfoByName API.
	http://bugzilla.maptools.org/show_bug.cgi?id=2315

	* libtiff/libtiff.def: add some missing (64bit) APIs.
	http://bugzilla.maptools.org/show_bug.cgi?id=2316

2011-04-09  Bob Friesenhahn  <<EMAIL>>

	* libtiff 4.0.0beta7 released.

2011-04-09  Bob Friesenhahn  <<EMAIL>>

	* configure.ac: Should use AC_CANONICAL_HOST since host specifies
	the run-time target whereas target is used to specify the final
	output target if the package is a build tool (like a compiler),
	which libtiff is not.  Resolves libtiff bug 2307 "Use
	AC_CANONICAL_HOST macro".

2011-04-02  Bob Friesenhahn  <<EMAIL>>

	* configure.ac: Support configuring TIFF_INT64_FORMAT and
	TIFF_UINT64_FORMAT appropriately for MinGW32.

	* tools/tiffdump.c (ReadDirectory): MinGW32 needs to use WIN32
	printf conventions for 64-bit types because it uses the WIN32 CRT.

	* libtiff/{tif_dumpmode.c,tif_luv.c,tif_lzw.c,tif_print.c,
	tif_read.c,tif_strip.c,tif_thunder.c}: MinGW32 needs to use WIN32
	printf conventions for 64-bit types because it uses the WIN32 CRT.

	* tools/tiff2pdf.c (t2p_write_pdf_string): Fix printf syntax not
	understood by WIN32 CRT.

	* libtiff/tif_ojpeg.c: Fixes to compile with MinGW32 GCC.

	* tools/fax2ps.c (main): Use tmpfile() rather than mkstemp() since
	it is much more portable.  Tmpfile is included in ISO/IEC
	9899:1990 and the WIN32 CRT.

2011-03-26  Frank Warmerdam  <<EMAIL>>

	* tools/tiffset.c: add -d and -sd switches to allow operation on
	a particular directory, not just the first (jef).

2011-03-21  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_thunder.c: Correct potential buffer overflow with
	thunder encoded files with wrong bitspersample set.  The libtiff
	development team would like to thank Marin Barbella and TippingPoint's
	Zero Day Initiative for reporting this vulnerability (ZDI-CAN-1004,
	CVE-2011-1167).
	http://bugzilla.maptools.org/show_bug.cgi?id=2300

2011-03-10  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_fax3.h: Fix to last change allowing zero length
	runs at the start of a scanline - needed for legal cases.

2011-03-02  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_fax3.h: Protect against a fax VL(n) codeword commanding
	a move left.  Without this, a malicious input file can generate an
	indefinitely large series of runs without a0 ever reaching the right
	margin, thus overrunning our buffer of run lengths.  Per CVE-2011-0192.
	This is a modified version of a patch proposed by Drew Yao of Apple
	Product Security.  It adds an unexpected() report, and disallows the
	equality case, since emitting a run without increasing a0 still allows
	buffer overrun.

2011-02-23  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: avoid divide by zero in degenerate case (#2296)

	* tools/tiff2rgba.c: close source file on error to make leak
	detection easier.

	* libtiff/tif_getimage.c: avoid leaks if TIFFRGBAImageBegin() fails.

	http://bugzilla.maptools.org/show_bug.cgi?id=2295

2011-02-22  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_lzma.c: Maintain tif_rawcc/tif_rawcp (CHUNKY_STRING_READ
	_SUPPORT)

2011-02-18  Frank Warmerdam  <<EMAIL>>

	* configure.ac, configure: Added support for --enable-chunky-strip-read
	configure option to enable the experimental feature from a couple
	months ago for reading big strips in chunks.

	* configure.ac, tif_read.c, tif_readdir.c, tif_dir.h, tiffiop.h,
	tif_write.c, tif_print.c, tif_jpeg.c, tif_dirwrite.c, tif_write.c:
	Implement optional support for deferring the load of strip/tile
	offset and size tags for optimized scanning of directories.  Enabled
	with the --enable-defer-strile-load configure option (DEFER_STRILE_LOAD
	#define in tif_config.h).

2011-02-11  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_print.c: remove unused variable.

2011-02-09  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_win32.c: avoid error/warning buffer overrun problem
	with non-console (popup message) builds on win32.

	http://bugzilla.maptools.org/show_bug.cgi?id=2293

2011-01-24  Olivier Paquet  <<EMAIL>>

	* libtiff/{tif_dir.{h,c}, tif_dirinfo.c, tif_dirread.c, tif_dirwrite.c,
	tif_print.c, tiff.h, tiffiop.h} : Added support for
	TIFFTAG_SMINSAMPLEVALUE and TIFFTAG_SMAXSAMPLEVALUE to have different
	values for each sample. Presents the min/max of all samples by default for
	compatibility. TIFFSetField/TIFFGetField can be made to handle those tags
	as arrays by changing the new TIFFTAG_PERSAMPLE pseudo tag.
	http://www.asmail.be/msg0055458208.html

2011-01-06  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_pixarlog.c: Note that tif_rawcc/tif_rawcp are not
	maintained.

	* libtiff/tif_zip.c: Maintain tif_rawcc/tif_rawcp when decoding
	for CHUNKY_STRIP_READ_SUPPORT.

	* libtiff/tif_jpeg.c: ensure that rawcc and rawcp are maintained
	during JPEGPreDecode and JPEGDecode calls.
	* libtiff/tif_read.c: larger read ahead for CHUNKY_STRIP_READ_SUPPORT,
	as compression formats like JPEG keep 16 lines interleaved in a sense
	and might need to touch	quite a bit of data.

	http://trac.osgeo.org/gdal/ticket/3894

2011-01-03  Lee Howard <<EMAIL>>

	* libtiff/tif_jpeg.c: Fix regressions with 2 and 3 band images
	caused by commit on 2010-12-14.  Submitted by e-mail from
	Even Rouault <<EMAIL>>

2010-12-31  Olivier Paquet  <<EMAIL>>

	* libtiff/tif_dirwrite.c: Fixed writing of TIFFTAG_REFERENCEBLACKWHITE.
	http://bugzilla.maptools.org/show_bug.cgi?id=2266

2010-12-23  Andrey Kiselev  <<EMAIL>>

	* tools/tiffcp.c, man/tiffcp.1: Added support for specifying the
	compression level parameter (preset) for Deflate and LZMA encoders,
	e.g "-c lzma:p1" or "-c zip:p9".

	* libtiff/tif_lzma.c: Properly set the LZMA2 compression level
	(preset) in LZMAVSetField().

2010-12-18  Bob Friesenhahn  <<EMAIL>>

	* libtiff/Makefile.am (libtiff_la_SOURCES): Added tif_lzma.c to
	Makefile.

2010-12-14  Andrey Kiselev  <<EMAIL>>

	* configure.ac, libtiff/{tif_codec.c, tif_config.h.in, tiff.h,
	tiffiop.h, tif_lzma.c}, tools/tiffcp.c, man/tiffcp.1: Implement a new
	TIFF compression scheme LZMA reserving a new value 34925 for
	Compression tag. As per
	bug http://bugzilla.maptools.org/show_bug.cgi?id=2221

2010-12-14  Lee Howard <<EMAIL>>

	* libtiff/tif_dirread.c: tolerate some cases where
	FIELD_COLORMAP is missing
	http://bugzilla.maptools.org/show_bug.cgi?id=2189

2010-12-14  Lee Howard <<EMAIL>>

	* libtiff/tif_read.c: change read_ahead to tmsize_t
	http://bugzilla.maptools.org/show_bug.cgi?id=2222

2010-12-14  Lee Howard <<EMAIL>>

	* configure.ac, libtiff/Makefile.am: Build tif_win32.c on
	Windows except on Cygwin
	http://bugzilla.maptools.org/show_bug.cgi?id=2224

2010-12-14  Lee Howard <<EMAIL>>

	* tools/gif2tiff.c: fix buffer overrun
	http://bugzilla.maptools.org/show_bug.cgi?id=2270

2010-12-14  Lee Howard <<EMAIL>>

	* libtiff/tif_jpeg.c: reduce usage of JCS_UNKNOWN in order
	to improve compatibility with various viewers
	submitted by e-mail from Dwight Kelly <<EMAIL>>

2010-12-13  Lee Howard <<EMAIL>>

	* tools/fax2ps.c: be consistent with page-numbering
	http://bugzilla.maptools.org/show_bug.cgi?id=2225

2010-12-13  Lee Howard <<EMAIL>>

	* libtiff/tif_color.c: prevent crash in handling bad TIFFs
	resolves CVE-2010-2595
	http://bugzilla.maptools.org/show_bug.cgi?id=2208

2010-12-13  Lee Howard <<EMAIL>>

	* tools/tiffcrop.c: new release by Richard Nolde
	http://bugzilla.maptools.org/show_bug.cgi?id=2004

2010-12-12  Lee Howard <<EMAIL>>

	* tools/tiff2pdf.c: fix colors for images with RGBA
	interleaved data
	http://bugzilla.maptools.org/show_bug.cgi?id=2250

2010-12-12  Lee Howard <<EMAIL>>

	* libtiff/tif_dirread.c: fix for Zeiss LSM and Canon CR2 files
	http://bugzilla.maptools.org/show_bug.cgi?id=2164

2010-12-11  Lee Howard <<EMAIL>>

	* tools/tiff2pdf.c: remove invalid duplication for Lab
	http://bugzilla.maptools.org/show_bug.cgi?id=2162

2010-12-11  Lee Howard <<EMAIL>>

	* libtiff/tif_jpeg.c: fix use of clumplines calculation
	http://bugzilla.maptools.org/show_bug.cgi?id=2149

2010-12-11  Lee Howard <<EMAIL>>

	* tools/fax2ps.c: replace unsafe tmpfile() with mkstemp()
	http://bugzilla.maptools.org/show_bug.cgi?id=2118

2010-12-11  Lee Howard <<EMAIL>>

	* libtiff/tif_ojpeg.c, libtiff/tif_pixarlog.c,
	  libtiff/tif_zip.c: fix build errors for VC6
	http://bugzilla.maptools.org/show_bug.cgi?id=2105

2010-12-11  Lee Howard <<EMAIL>>

	* libtiff/tif_stream.cxx: warnings cleanup
	http://bugzilla.maptools.org/show_bug.cgi?id=2091
	* libtiff/tif_dirread.c: warnings cleanup
	http://bugzilla.maptools.org/show_bug.cgi?id=2092

2010-12-11  Lee Howard <<EMAIL>>

	* tools/tiff2pdf.c: add fill-page option
	http://bugzilla.maptools.org/show_bug.cgi?id=2051

2010-12-11  Lee Howard <<EMAIL>>

	* libtiff/tif_dirread.c: modify warnings
	http://bugzilla.maptools.org/show_bug.cgi?id=2016

2010-12-11  Lee Howard <<EMAIL>>

	* libtiff/tif_ojpeg.c: fix buffer overflow on problem data
        http://bugzilla.maptools.org/show_bug.cgi?id=1999

2010-12-11  Lee Howard <<EMAIL>>

	* tools/tiffinfoce.c: strip byte counts are uint64* now

2010-12-11  Lee Howard <<EMAIL>>

        * libtiff/tif_ojpeg.c: fix crash when reading a TIFF with a zero
        or missing byte-count tag
        * tools/tiffsplit.c: abort when reading a TIFF without a byte-count
        per http://bugzilla.maptools.org/show_bug.cgi?id=1996

2010-12-08  Lee Howard <<EMAIL>>

        * libtiff/tif_dirread.c: fix crash when reading a badly-constructed
        TIFF per http://bugzilla.maptools.org/show_bug.cgi?id=1994

2010-12-06  Lee Howard <<EMAIL>>

        * libtiff/tif_open.c: Fix mode check before opening a file.
        http://bugzilla.maptools.org/show_bug.cgi?id=1906

2010-11-27  Bob Friesenhahn  <<EMAIL>>

	* libtiff-4.pc.in: Added libtiff pkg-config .pc file support.
	Patch by Vincent Torri.

2010-10-21  Frank Warmerdam  <<EMAIL>>

	* tools/tiffinfo.c: avoid direct reference to _TIFFerrorHandler.

	* libtiff/tif_config.vc.h: define snprintf to _snprintf for tiff2pdf.

	* libtiff/libtiff.def: export _TIFFCheckMalloc for tools.

2010-09-25  Lee Howard <<EMAIL>>

	* tools/tiff2ps.c: improvements and enhancements from Richard Nolde
	with additional command line options for Document Title,
	Document Creator, and Page Orientation

2010-07-13  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffcrop.c: Patch from Richard Nolde to avoid a
	potentially unterminated buffer due to using an exceptionally long
	file name.

2010-07-08  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2pdf.c: Fixed ID buffer filling in
	t2p_write_pdf_trailer(), thanks to Dmitry V. Levin.

2010-07-07  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: Really reset the tag count in CheckDirCount()
	to expected value as the warning message suggests. As per bug
	http://bugzilla.maptools.org/show_bug.cgi?id=1963

2010-07-06  Andrey Kiselev  <<EMAIL>>

	* tools/tiffset.c: Properly handle TIFFTAG_PAGENUMBER,
	TIFFTAG_HALFTONEHINTS, TIFFTAG_YCBCRSUBSAMPLING, TIFFTAG_DOTRANGE
	which should be set by value.

	* libtiff/tif_dirinfo.c: Don't use assertions in _TIFFFieldWithTag()
	and _TIFFFieldWithName() if the tag is not found in the tag table.
	This should be normal situation and returned NULL value should be
	properly handled by the caller.

2010-07-02  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_getimage.c: Avoid wrong math du to the signed/unsigned
	integer type conversions. As per bug
	http://bugzilla.maptools.org/show_bug.cgi?id=2207

	* tools/{tiff2bw.c, thumbnail.c, pal2rgb.c}: Fix the count for
	WhitePoint tag as per bug
	http://bugzilla.maptools.org/show_bug.cgi?id=2042

	* libtiff/tif_getimage.c: Check the number of samples per pixel when
	working with YCbCr image in PickContigCase(). As per bug
	http://bugzilla.maptools.org/show_bug.cgi?id=2216

	* libtiff/tif_dir.c: Set the bogus post-decoding hook when processing
	TIFFTAG_BITSPERSAMPLE in _TIFFVSetField() for the case of 8 bit when
	we don't need any post-processing. That helps to reset the hook if we
	previously set this field to some other value and the hook was
	initialized accordingly. As per bug
	http://bugzilla.maptools.org/show_bug.cgi?id=2035

2010-07-01  Andrey Kiselev  <<EMAIL>>

	* tools/tiffgt.c: Properly check the raster buffer allocations for
	integer overflows. As per bug
	http://bugzilla.maptools.org/show_bug.cgi?id=2108

	* m4/acinclude.m4: Update GL/GLU/GLUt/Pthread macros from the
	upstream.

	* libtiff/{tif_aux.c, tif_strip.c, tif_tile.c, tiffiop.h}: Move
	multiply_32() and multiply_64() functions into tif_aux.c file and
	rename them into _TIFFMultiply32() and _TIFFMultiply64() respectively.

2010-06-30  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2pdf.c: Better generation of ID field in
	t2p_write_pdf_trailer(). Get rid of GCC aliasing warnings.

	* tools/tiff2pdf.c: Fixed computation of the tile buffer size when
	converting JPEG encoded tiles.

	* tools/tiff2pdf.c: Better handling of string fields, use static
	string buffers instead of dynamically allocated, use strncpy() instead
	of strcpy(), control the string lengths.

2010-06-25  Andrey Kiselev  <<EMAIL>>

	* tools/tiffcp.c: Initialize buffer arrays with zero to avoid
	referencing to uninitialized memory in some cases (e.g. when tile size
	set bigger than the image size).

2010-06-15  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffcrop.c: Patch from Richard Nolde. Reject YCbCr
	subsampled data since tiffcrop currently doesn't support it.  Fix
	JPEG support.

2010-06-13  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirinfo.c: Fix invocation of tag compare function (#2201)

	* tools/tiff2pdf.c: Fix assorted bugs in tiff2pdf: missing "return"
	in t2p_read_tiff_size() causes t2p->tiff_datasize to be set entirely
	wrong for COMPRESSION_JPEG case, resulting in memory stomp if actual
	size is larger.  Also, there are a bunch of places that try to
	memset() a malloc'd buffer before checking for malloc failure, which
	would result in core dump if there actually were a failure. (#2211)

2010-06-11  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tiffiop.h (TIFFSafeMultiply): Need more castings to
	avoid compiler warnings if parameter types are not sign
	consistent.

	* libtiff 4.0.0alpha6 released.

	* tools/tiffcrop.c: Applied patch from Richard Nolde: Corrected
	European page size dimensions.  Added an option to allow the user
	to specify a custom page size on the command line.  Fix the case
	where a page size specified with a fractional part was being
	coerced to an integer by retyping the variables that define the
	paper size.

	* html/index.html: Update for the 3.9.3 release.

	* tools/tiffcp.c (tiffcp): Applied Tom Lane's patch to reject
	YCbCr subsampled data since tiffcp currently doesn't support it.
	http://bugzilla.maptools.org/show_bug.cgi?id=2097

	* Update libtool to version 2.2.10.

2010-06-10  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tiffiop.h (TIFFSafeMultiply): Work properly if
	multiplier is zero.

2010-06-09  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_fax3.c (Fax3SetupState): Yesterday's fix for
	CVE-2010-1411 was not complete.

	* libtiff/tiffiop.h (TIFFSafeMultiply): New macro to safely
	multiply two integers.  Returns zero if there is an integer
	overflow.

	* tools/tiffcp.c (main): tiffcp should not leak memory if an error
	is reported when reading the input file.

2010-06-08  Bob Friesenhahn  <<EMAIL>>

	* Update libtool to version 2.2.8.

	* libtiff/tif_fax3.c (Fax3SetupState): Avoid under-allocation of
	buffer due to integer overflow in TIFFroundup() and several other
	potential overflows.  In conjunction with the fix to TIFFhowmany(),
	fixes CVE-2010-1411.

	* libtiff/tiffiop.h (TIFFhowmany): Return zero if parameters would
	result in an integer overflow. This causes TIFFroundup() to also
	return zero if there would be an integer overflow.

	* contrib: Add an emacs formatting mode footer to all source files
	so that emacs can be effectively used.

2010-06-03  Oliver Chen Feng <<EMAIL>>

	* libtiff/tools/tiffcp.c: add a new option -x to force merged tiff
	file PAGENUMBER value in sequence for users who care the page
	sequence, this will also prevent tiff2pdf from creating pdf file from
	the merged tiff file with wrong page sequence.

2010-05-08  Olivier Paquet  <<EMAIL>>

	* libtiff/tif_dirread.c: Restored TIFFReadDirEntryFloat function in order
	to add missing TIFF_SETGET_FLOAT case to TIFFFetchNormalTag.
	* libtiff/tif_dirinfo.c: Use correct set_field_type for
	TIFFTAG_PIXAR_FOVCOT so it is readable again (regression from 3.9.2).
	http://bugzilla.maptools.org/show_bug.cgi?id=2192

2010-05-07  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: Ensure that quality is always set in
	JPEGPreEncode(), not just when we want to output local tables.
	Otherwise the quality used during compression may not be right and
	might not match the tables in the tables tag.   This bug only occurs
	when seeking between directories in the midst of writing blocks.
	http://trac.osgeo.org/gdal/ticket/3539

2010-05-06  Andrey Kiselev  <<EMAIL>>

	* html/man/TIFFGetField.3tiff.html, html/man/TIFFSetField.3tiff.html:
	Regenerated from the source.

2010-05-05  Olivier Paquet  <<EMAIL>>

	* libtiff/tif_print.c: Fixed printing of TIFFTAG_REFERENCEBLACKWHITE which
	had stopped working. Also made it always print 6 floats instead of
	2*SamplesPerPixel.
	http://bugzilla.maptools.org/show_bug.cgi?id=2191
	http://bugzilla.maptools.org/show_bug.cgi?id=2186
	* man/TIFFGetField.3tiff, man/TIFFSetField.3tiff: Fixed doc to reflect the
	fact that libtiff considers TIFFTAG_REFERENCEBLACKWHITE to be 6 floats.

2010-05-05  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: Fix to use memcmp(), not memcpy() when checking
	if the jpeg table was written.  This is a fix for the last fix on 04-21.

2010-04-21  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: avoid preparing jpeg tables every time
	JPEGSetupEncode() is called if the tables already seem to be
	established.  This prevents spurious updates and rewriting of
	directories with jpegtables when doing updates to existing images.
	http://trac.osgeo.org/gdal/ticket/3539

2010-04-20  Olivier Paquet  <<EMAIL>>

	* libtiff/tif_dirinfo.c: Use correct set_field_type for
	TIFFTAG_PIXAR_IMAGEFULLWIDTH, TIFFTAG_PIXAR_IMAGEFULLLENGTH,
	TIFFTAG_PIXAR_MATRIX_WORLDTOSCREEN and TIFFTAG_PIXAR_MATRIX_WORLDTOCAMERA.
	They were unreadable with TIFF_SETGET_UNDEFINED, a regression from 3.9.2.
	http://bugzilla.maptools.org/show_bug.cgi?id=2139

2010-04-10  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_dir.c (_TIFFVSetField): Add a special error case for
	when the tag count value is zero.  Error handling is still a
	regression since in 3.9.2, empty tags are skipped (with a warning)
	rather than returning a hard error and refusing to read the file.

	* tools/ppm2tiff.c (main): While case for parsing comment line
	requires extra parenthesis to work as expected.  Reported by
	Thomas Sinclair.

2010-04-02  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_read.c (primarily): Add support for
	CHUNKY_STRIP_READ_SUPPORT where large strips are
	read in chunks for applications using TIFFReadScanline().
	This is intended to make it more practical work with very
	large compressed one-strip files.   Feature is off by default.
	Enable by defining CHUNK_STRIP_READ_SUPPORT as a macro.
	http://trac.osgeo.org/gdal/ticket/3514

2010-03-31  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_flush.c: Use TIFFRewriteDirectory() when flushing
	directories so previously placed directories will be migrated to
	the end of file if needed.

2010-03-30  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_lzw.c: change type of dec_bitsleft field to uint64
	to support operating on strips/tiles of more than 256MB.
	http://trac.osgeo.org/gdal/ticket/3512

2010-03-10  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_aux.c (_TIFFCheckRealloc): Improve error message so
	that it is clearly a memory allocation error message, and also
	includes the size of the allocation request.

2010-02-22  Lee Howard  <<EMAIL>>

	* libtiff/tif_jpeg.c: Do not generate a JPEGTables tag when creating
	the JPEG TIFF as is is not required in order to prevent it from
	being unused and filled with invalid data.  (Leave it to be
	generated by later activity.)
	http://bugzilla.maptools.org/show_bug.cgi?id=2135
	* tools/tiff2pdf.c: Write the JPEG SOI headers into the TIFF strip
	data rather than skipping them.  This fixes the ability to view in
	Acrobat Reader, Evince, and Ghostscript.
	http://bugzilla.maptools.org/show_bug.cgi?id=2135
	* libtiff/tif_fax3.c: Don't return error on badly-terminated MMR
	strips.
	http://bugzilla.maptools.org/show_bug.cgi?id=2029

2009-12-03  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: Made JPEGDecodeRaw() check for buffer overruns.
	Made so that when working with downsampled images a stub function
	reporting an error is used for tif_decoderow.  We cannot meaningfully
	support reading scanlines in this situation.  (#1936)

	* libtiff/tif_jpeg.c: Ensure that tif_scanlinesize is computed after
	resetting of the upsampling values (gdal:#3259).
	http://bugzilla.maptools.org/show_bug.cgi?id=1936

2009-11-30  Frank Warmerdam  <<EMAIL>>

	* contrib/dbs/tiff-grayscale.c, contrib/tif-palette.c,
	tools/ras2tiff.c: Fix resource leaks on error.
	http://bugzilla.maptools.org/show_bug.cgi?id=2121

	* libtiff/tif_{aux.c,dir.c,dir.h,dirinfo.c}: Return to handling
	TIFFTAG_REFERENCEBLACKWHITE as a field in the TIFF directory instead
	of as a custom(generic) field to avoid a potential reentrancy problem.
	http://bugzilla.maptools.org/show_bug.cgi?id=2125

	* libtiff/tif_color.c, libtiff/tif_getimage.c, libtiff/tiffio.h,
	man/TIFFcolor.3tiff: Make TIFFDisplay argument in TIFFCIELabToRGBInit
	const, and display_sRGB static and const.
	http://bugzilla.maptools.org/show_bug.cgi?id=2124

2009-11-04  Bob Friesenhahn  <<EMAIL>>

	* libtiff 4.0.0alpha5 released.

2009-11-03  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffcrop.c: Updated tiffcrop from Richard Nolde.  This
	version has undergone substantial testing with arbitrary sample
	bit depths.  Also eliminates GCC compilation warnings.

2009-11-02  Bob Friesenhahn  <<EMAIL>>

	* port/libport.h: Add extern declarations for getopt standard
	globals.

2009-10-31  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_lzw.c (LZWDecode, LZWDecodeCompat): Fix warnings
	noticed in 64-bit build of libtiff with Visual Studio 2005.
	Resolves "Bug 2067 - Visual Studio 2005 64-bit warnings in
	tif_lzw.c", http://bugzilla.maptools.org/show_bug.cgi?id=2067

	* libtiff/tif_pixarlog.c (PixarLogEncode): Fix non-important
	warning noticed in Visual Studio 2005 build. Resolves "Bug 2068 -
	Visual Studio 2005 64-bit warning in tif_pixarlog.c",
	http://bugzilla.maptools.org/show_bug.cgi?id=2068

2009-10-29  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_dirread.c: Eliminate GCC "dereferencing type-punned
	pointer" warnings.

2009-10-28  Bob Friesenhahn  <<EMAIL>>

	* html/tools.html: Add manual page links, and a summary
	description of tiffcrop.

2009-10-07  Bob Friesenhahn  <<EMAIL>>

	* configure.ac: x86_64 should use the same fill order as i386.

2009-09-24  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffcrop.c, man/tiffcrop.1: New tiffcrop from Richard
	Nolde.  Major updates to add significant functionality for reading
	and writing tile based images with bit depths not a multiple of 8
	which cannot be handled by tiffcp.

2009-09-03  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_ojpeg.c (OJPEGWriteHeaderInfo): IJG JPEG 7 needs
	do_fancy_upsampling=FALSE in order to read raw data.  Resolves
	"Bug 2090 - OJPEG crash with libjpeg v7".
	http://bugzilla.maptools.org/show_bug.cgi?id=2090

2009-09-03  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_getimage.c: Fixed error recognition handling in RGBA
	interface when stoponerror is set.
	http://bugzilla.maptools.org/show_bug.cgi?id=2071

2009-08-30  Bob Friesenhahn  <<EMAIL>>

	* tools/{tiffcrop.c,tiffgt.c}: Applied patch from Oden Eriksson to
	fix build with gcc when using the "-Wformat
	-Werror=format-security" flags.

2009-08-29  Bob Friesenhahn  <<EMAIL>>

	* test/{bmp2tiff_palette.sh, bmp2tiff_rgb.sh, gif2tiff.sh,
	ppm2tiff_pbm.sh, ppm2tiff_pgm.sh, ppm2tiff_ppm.sh}: Additional
	utilities tests.

2009-08-28  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffinfo.c: tiffinfo should return error status to the
	caller.  Register a private error callback to accomplish that.

	* test/Makefile.am (TIFFIMAGES): Add test images in BMP, GIF, and
	PNM formats so that we will be able to test more of the tools.
	While adding these test images I notice that bmp2tiff and gif2tiff
	only support ancient versions of their respective formats.

2009-08-27  Bob Friesenhahn  <<EMAIL>>

	* libtiff 4.0.0alpha4 released.

	* HOWTO-RELEASE: Improved release instructions.

2009-08-24  Bob Friesenhahn  <<EMAIL>>

	* man/{TIFFClose.3tiff,raw2tiff.1,tiffcmp.1,tiffsplit.1}: Applied
	fixes for "Bug 2023 - nroff errors in manual pages".
	http://bugzilla.maptools.org/show_bug.cgi?id=2023

	* tools/{rgb2ycbcr.c, tiff2rgba.c}: Applied fixes for "Bug 2079 -
	CVE-2009-2347 libtiff: integer overflows in various inter-color
	space conversion tools".
	http://bugzilla.maptools.org/show_bug.cgi?id=2079

	* libtiff/tif_print.c (TIFFPrintDirectory): Apply fix from Jay
	Berkenbilt for "Bug 2024 - possible null pointer dereference with
	one-line fix".
	http://bugzilla.maptools.org/show_bug.cgi?id=2024

	* libtiff/tif_dirread.c (TIFFReadCustomDirectory): Apply patch
	from Jay Berkenbilt for "Bug 1895 - logic error in tif_dirread.c:
	segfault after setting tdir_tag = IGNORE".
	http://bugzilla.maptools.org/show_bug.cgi?id=1895

2009-08-23  Bob Friesenhahn  <<EMAIL>>

	* test/Makefile.am, test/tiffcrop*.sh: Split previously existing
	tiffcrop.sh into a collection of many specific tests.  Re-wrote
	all of the existing tests to be based on some simple shell
	functions.  Make distcheck works again.

	Export certain variables (MAKE, MAKEFLAGS, MEMCHECK) to tests and
	added 'memcheck' and 'ptrcheck' targets to make it easy to run the
	tests under valgrind.

2009-08-21  Bob Friesenhahn  <<EMAIL>>

	* test/tiffcp-logluv.sh: Fix test so that it works with a VPATH
	build.

	* test/Makefile.am (AUTOMAKE_OPTIONS): Colorized tests was not
	actually activated since it needed to be enabled in this
	Makefile.am.  Also activated parallel-tests mode since it offers
	useful features such as per-test .log files and a summary test
	report .log file.

2009-08-20  Bob Friesenhahn  <<EMAIL>>

	* configure.ac: Updated autotools.  Autoconf 2.64, Automake 1.11,
	libtool 2.2.6.  Enabled support for silent build rules
	(--enable-silent-rules or 'make V=0') and colorized tests.

	* html/{index.html, v3.9.0.html}: Update for 3.9.0 release.

2009-06-30  Frank Warmerdam  <<EMAIL>>

	* tests/tiffcp-logluv.sh: minimal testing of sgilog compression.

	* tools/tiffcp.c: add -c sgilog support.

	* libtiff/tif_luv.c: correct return codes from encoderow to be
	1 on success instead of zero.
	http://bugzilla.maptools.org/show_bug.cgi?id=2069

	* libtiff/tif_lzw.c: back out patch from #2065 and apply patch from
	#1085 for a better underflow fix that errors properly.
	http://bugzilla.maptools.org/show_bug.cgi?id=2065
	http://bugzilla.maptools.org/show_bug.cgi?id=1985

2009-06-26  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_strip.c: Remove an inappropriate assertion that often
	fails on oddly sized 12bit jpeg compressed ycbcr images.

2009-06-22  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_lzw.c: Fix buffer underflow bug.
	http://bugzilla.maptools.org/show_bug.cgi?id=2065

2009-06-21  Frank Warmerdam  <<EMAIL>>

	* configure.ac, libtiff/tif_jpeg.c, libtiff/tif_jpeg_12.c: add support
	for dual mode 8/12 bit jpeg support.

2009-06-03  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_write.c: do not override the planar configuration to be
	contig for one sample files if planar configuration is already set.
	http://bugzilla.maptools.org/show_bug.cgi?id=2057

2009-06-02  Frank Warmerdam  <<EMAIL>>

	* libtiff/libtiff.def: Add TIFFUnsetField.

2009-05-03  Frank Warmerdam  <<EMAIL>>

	* libtiff/{tif_jpeg.c,tif_ojpeg.c,tif_getimage.c}: Fixed various
	error reports to use "%s" as format string.
	http://trac.osgeo.org/gdal/ticket/2976

2009-03-12  Frank Warmerdam  <<EMAIL>>

	* libtiff/{tif_fax3.c,tif_jpeg.c,tif_ojpeg.c}: Fix printdir chaining
	for some codecs (#2020).

2009-02-12  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_luv.c: Fix handling of tiled logluv images.
	http://bugzilla.maptools.org/show_bug.cgi?id=2005

2009-02-09  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirread.c: Improve allocation safety when allocated
	buffer for large tags.  (#1998)  Related to (#1993)

2009-02-06  Frank Warmerdam  <<EMAIL>>

	* tools/tiffcrop.c: Don't default image->res_unit to INCH.  Now the
	test suite should pass.

2009-02-05  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirread.c: Re-incorporated a sanity check on tag size,
	but at the 2GB boundary to avoid overflow on 32bit systems.
	http://bugzilla.maptools.org/show_bug.cgi?id=1993

	* libtiff/tif_dirread.c: Remove some assertions that blow due to
	corrupt files rather than in response to library internal
	inconsistencies.
	http://bugzilla.maptools.org/show_bug.cgi?id=1995
	http://bugzilla.maptools.org/show_bug.cgi?id=1991

	* libtiff/tif_dirread.c: Fixed testing for failed result from
	TIFFReadDirectoryFindFieldInfo().
	http://bugzilla.maptools.org/show_bug.cgi?id=1992

2009-01-23  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_predict.c: Add support for 32bit integer horz. predictors.
	http://bugzilla.maptools.org/show_bug.cgi?id=1911

	* libtiff/tif_dirwrite.c: Fix byte swapping of next directory offset.

	http://bugzilla.maptools.org/show_bug.cgi?id=1924

	* tools/tiffcrop.c: initialize xres/yres values.

	* test/*.sh - default ${srcdir} to local directory.

	* test/common.sh - start verbose mode after common settings.

	* libtiff/tif_dirinfo.c: Replace lfind() with local equivalent to
	avoid type mismatches on different platforms.
	http://bugzilla.maptools.org/show_bug.cgi?id=1889

2009-01-22  Frank Warmerdam  <<EMAIL>>

	* tools/{fax2tiff.c,thumbnail.c,tiff2pdf.c,tiff2ps.c,tiffdump.c,
	tiffsplit.c}: avoid warnings, mostly 32bit/64bit casting issues.

	* port,tools: Introduce libport.h, and include in tools if NEED_LIBPORT
	defined, primarily to reduce prototype warnings on windows.

	* libtiff/tif_dirinfo.c,tif_dirread.c: Avoid warnings
	about unused parameters, and uninitialized variables.

2009-01-21  Bob Friesenhahn  <<EMAIL>>

	* test/common.sh: Execute tests like 'make VERBOSE=TRUE check' in
	order to trace full execution detail while executing the test suite.

2009-01-20  Frank Warmerdam  <<EMAIL>>

	* tools/tiffsplit.c: fix sampleformat to be shortv instead of longv.

2009-01-20  Bob Friesenhahn  <<EMAIL>>

	* test/Makefile.am (CLEANFILES): Make sure that test output files
	are removed by 'make clean'

	* Update autotools for 4.0.0 beta3

	* 4.0.0 beta3 produced.

2009-01-12  Bob Friesenhahn  <<EMAIL>>

	* test/tiffcrop.sh: New test script for tiffcrop from Richard
	Nolde.

	* tools/tiff2ps.c: Remove spurious message to stderr.

2009-01-11  Bob Friesenhahn  <<EMAIL>>

	* tools/tiff2ps.c: Incorporated significant functionality update
	from Richard Nolde.  In particular, support for rotating the image
	by 90, 180, 270, and 'auto' has been added.

	* man/tiffcrop.1: Incorporated documentation updates from Richard
	Nolde.

	* tools/tiffcrop.c: Incorporated significant functionality update
	from Richard Nolde.

2008-12-31  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tiffio.h: GCC will now validate format specifications
	for TIFFError(), TIFFErrorExt(), TIFFWarning(), and
	TIFFWarningExt() in order to reveal bugs.

	* Many fixes throughout to work better as a 64-bit build.

2008-12-30  Bob Friesenhahn  <<EMAIL>>

	* tools/{tiff2pdf.c, tiff2ps.c, tiffinfo.c}: Offset and length
	tags now require 64-bit parameter rather than 32-bit.

	* libtiff/tif_dirread.c: Fixed issues with unaligned access to
	64-bit values.

	* tools/thumbnail.c: Eliminate crash noticed while running test
	suite.

2008-12-29  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_ojpeg.c (OJPEGLibjpegJpegSourceMgrFillInputBuffer):
	Initialize stack variables to avoid compiler warning.

	* tools/tiffinfoce.c (main): Use toff_t for offset type when
	retrieving offset of EXIF IFD.

	* libtiff/tiffio.h: Undeprecate toff_t and restore its use in the
	TIFFClientOpen() callback and other external function definitions.

	* tools/tiffinfo.c (main): Offset to EXIF IFD requires a 64-bit
	type now.  Fixes crash when dumping files containing an EXIF IFD.

	* m4/libtool.m4: Update to libtool 2.2.6.

2008-12-21  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dir.c, tiffio.h: Introduce TIFFUnsetField() function.

	* libtiff/tif_jpeg.c: Avoid errors if the application writes a full
	strip for the last partial strip in a jpeg compressed file.
	http://bugzilla.maptools.org/show_bug.cgi?id=1981

2008-10-29  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_flush.c: Make sure that BEENWRITING is cleared when
	we take the shortcut to only update the strip/tile offsets in place.
	http://trac.osgeo.org/gdal/ticket/2621

2008-10-21  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_jbig.c: Support the JBIG-KIT 2.0 (compatibility with
	the older versions retained).

2008-10-09  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: Add #ifdefs for changes needed if using
	IPP enabled version of libjpeg from Intel.
	http://bugzilla.maptools.org/show_bug.cgi?id=1951

2008-09-05  Andrey Kiselev  <<EMAIL>>

	* tools/tiffsplit.c: Use byte counts of proper size (uint64).
	Required for libtiff 4.0.

	* tools/tiffsplit.c: Use dynamically allocated array instead of static
	when constructing output file names.

2008-09-03  Andrey Kiselev  <<EMAIL>>

	* tools/tiffsplit.c: Get rid of unsafe strcpy()/strcat() calls when
	doing the filename/path construction.

	* tools/tiff2pdf.c: More appropriate format string in
	t2p_write_pdf_string(); avoid signed/unsigned mismatch.

	* libtiff/tif_lzw.c: Properly zero out the codetable. As per bug

	http://bugzilla.maptools.org/show_bug.cgi?id=1929

	* libtiff/tif_lzw.c: Properly zero out the string table. Fixes
	CVE-2008-2327 security issue.

2008-09-01  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirread.c: Avoid unused TIFFReadDirEntryFloat() function.

	* libtiff/tif_dirwrite.c: modified to write IFDs as either IFD8 or IFD
	depending on whether the file is bigtiff or classic tiff.
	http://bugzilla.maptools.org/show_bug.cgi?id=1917

2008-08-12  Edward Lam  <<EMAIL>>

	* tools/tiffdump.c: When compiling for Microsoft Windows, apply
	consistent (__int64) casting when testing if _lseeki64 has
	successfully sought as requested.  This is necessary for large
	file support to work since off_t is only 32-bit.

2008-07-29  Frank Warmerdam  <<EMAIL>>

	* tif_strip.c: Replace assertions related to samplesperpixel != 3 or
	the subsampling values not being 1, 2 or 4 (for jpeg compressed images)
	with control logic to return runtime errors (c/o Even Rouault) (#1927).

2008-06-17  Frank Warmerdam  <<EMAIL>>

	* tools/tiffcrop.c: Fix some portability problems.

	* libtiff/tif_ojpeg.c: Use same jpeg/win32 boolean/FAR hacks as are
	used in tif_jpeg.c.

	* libtiff/tif_win32.c: Ensure TIFFOpenW() uses same FILE_SHARE flags
	as TIFFOpen().

2008-06-01  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirwrite.c: Fix alignment problems affecting architectures
	like Sparc/Solaris.
	http://bugzilla.maptools.org/show_bug.cgi?id=1892

2008-05-27  Frank Warmerdam  <<EMAIL>>

	* libtiff.def: Add TIFFFindField
	http://bugzilla.maptools.org/show_bug.cgi?id=1891

2008-05-26  Frank Warmerdam  <<EMAIL>>

	* tif_config.*.h, tiffconf.*.h: Remove SIZEOF_LONG definition, unused.

	* li2008-04-15  Andrey Kiselev  <<EMAIL>>

btiff/tif_win32.c: Replace custom Win32 memory api with generic
	POSIX one.  No apparent value to use of GlobalAlloc() in the modern
	age.  http://bugzilla.maptools.org/show_bug.cgi?id=1885

	* libtiff/tiffconf.vc.h: Added JBIG_SUPPORT and MDI_SUPPORT items
	in windows version (care of Edward Lam).

2008-05-24  Frank Warmerdam  <<EMAIL>>

	* tif_codec.c: Avoid NULL pointer dereferencing for exotic
	compression codec codes.

	* tif_dirwrite.c: fix potential memory leak.

	* tif_dirread.c: Fix unchecked malloc result.

2008-05-24  Bob Friesenhahn  <<EMAIL>>

	* test {tiff2pdf.sh tiff2ps-EPS1.sh tiff2ps-PS1.sh tiff2ps-PS2.sh
	tiff2ps-PS3.sh tiffcp-g3-1d-fill.sh tiffcp-g3-1d.sh
	tiffcp-g3-2d-fill.sh tiffcp-g3-2d.sh tiffcp-g3.sh tiffcp-g4.sh
	tiffcp-split-join.sh tiffcp-split.sh tiffcp-thumbnail.sh
	tiffdump.sh tiffinfo.sh}: Added more test scripts based on
	suggestions from Lee Howard posted to the tiff list on 13 Sep
	2007.

2008-05-23  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_fax3.c: Add an assert in an effort to detect a
	possible runtime problem reported by coverity.

	* contrib/iptcutil/iptcutil.c: Fixed memory leak of str.

	* tools/tiffcrop.c, man/tiffcrop.1: Major update from Richard Nolde.
	http://bugzilla.maptools.org/show_bug.cgi?id=1888

	* tools/tiffdither.c: remove dead onestrip code.  avoid memory leak.

	* tools/rgb2ycbcr.c: fix memory leak of raster buffer.

	* tools/tiffcp.c: Simplify inknames code to avoid pointless test.
	Cleanup scanline allocation to avoid coverity warning.

	* tools/thumbnail.c: Check for TIFFOpen() failure.

2008-05-18  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirinfo.c: Use TIFF_SETGET_ASCII for PIXAR_TEXTUREFORMAT
	and PIXAR_WRAPMODES instead of TIFF_SETGET_UNDEFINED.  Not exactly clear
	why this is needed.

2008-05-09  Bob Friesenhahn  <<EMAIL>>

	* Makefile.am (ACLOCAL_AMFLAGS): Libtool 2.2.4 does not like
	"ACLOCAL_AMFLAGS=-I ./m4".  It wants "ACLOCAL_AMFLAGS=-I m4".

2008-04-15  Andrey Kiselev  <<EMAIL>>

	* test/: Test suite updated. Everything is passed now.

	* libtiff/tif_dirinfo.c: Fixed description of the
	TIFFTAG_NUMBEROFINKS tag.

2008-04-14  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dirread.c, tif_dirwrite.c, tiffiop.h}:
	Get rid of some of "dereferencing type-punned" warnings by converting
	tdir_offset field of TIFFDirEntry structure into union.

2008-04-10  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_flush.c, tif_dirwrite.c, tiffio.h, tiffiop.h}:
	TIFFRewriteField() renamed into _TIFFRewriteField() and moved out
	from the public interface. Type of its 'count' parameter changed
	from uint32 to tmsize_t.

	* /libtiff/tiffiop.h: Make tif_nfields and tif_nfieldscompat fields
	of the tiff structure have the size_t type instead of uint32.

2008-04-09  Andrey Kiselev  <<EMAIL>>

	* tools/tiffdump.c: Added support for MSVS 6.0.

	* libtiff/tif_dirread.c: Use custom functions _TIFFUInt64ToFloat()
	and _TIFFUInt64ToDouble() to convert 64-bit integers into floating
	point values on MSVS 6.0 platform.

2008-03-14  Frank Warmerdam  <<EMAIL>>

	* tif_dirread.c: Removed sanity checks on tags larger than 4MB in
	TIFFReadDirEntryArray() since they are interfering with seemingly
	legitimate files.  http://trac.osgeo.org/gdal/ticket/2005

2008-02-09  Joris Van Damme  <<EMAIL>>

	* tif_dirread.c: Added handling for the case of number of values for
	PageNumber tag different from 2 (previously resulted in an assert
	indicating lack of handling and was forgotten about)

2008-02-01  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: Do not try to fixup subsampling tags based on
	the actual jpeg data stream if the first strip/tile has zero size.
	This is the case when GDAL creates a new file with zero sizes, closes
	and reopens it.

2008-01-07  Frank Warmerdam  <<EMAIL>>

	* tools/tiff2ps.c: fix up 64bit issues (from Edward Lam).

2008-01-01  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirwrite.c: #ifdef out lots of unused functions.

	* Makefile.vc, libtiff/Makefile.vc, tools/Makefile.vc: Improve clean
	targets.

	* tools/tiffinfo.c, tools/tiffcmp.c, tools/gif2tiff.c, tools/bmp2tiff.c
	tools/tiff2pdf.c: Fix 64-bit warnings when compiling under MSVC 2005
	(x64).

	* tools/tiffset.c: Changes to reflect the fact that TIFFFieldWithTag()
	and TIFFFieldWithName() now return TIFFField pointers instead of
	TIFFFieldInfo pointers.

	* tools/tiffdump.c: Added ssize_t typedef on Windows since it doesn't
	exist. This makes it compile again on Windows

	* tif_aux.c, tif_getimage.c, tif_next.c, tif_predict.c, tif_win32.c,
	tiffconf.vc.h: Various 64bit fixes from Edward Lam identified on win64.

	* test/rewrite_tag.c: New test for TIFFRewriteField().

2007-12-31  Frank Warmerdam  <<EMAIL>>

	* tif_dirwrite.c: Added TIFFRewriteField().  This new function
	rewrites one field "on disk" updating an existing directory
	entry.  Lots of limitations still...

	* tiffiop.h, tif_write.c, tif_dirread.c, tif_flush.c: Keep track of
	TIFF_DIRTYSTRIP separately from TIFF_DIRTYDIRECT to indicate that
	the strip offset/size values are dirty but nothing else about the
	directory is dirty.  In flush handle "just stripmaps dirty" as a
	special case that just rewrites these values without otherwise
	modifying the directory on disk using TIFFRewriteField().

	We also modify logic so that in update mode the directory is not
	marked dirty on read, but only when something is changed.  This
	means we need to keep track of updates to the stripmap stuff in
	TIFFAppendToStrip().

2007-12-10  Frank Warmerdam  <<EMAIL>>

	* tif_jpeg.c: Improve ability to switch between encoding and decoding
	in the jpeg code (gdal bug #2033).

2007-11-23  Frank Warmerdam  <<EMAIL>>

	* tif_dir.c, tif_dirread.c, tif_dirwrite.c, tif_read.c, tif_write.c,
	tiffiop.h: Added TIFF_BUF4WRITE flag to indicate if contents of the
	rawcp/rawcc buffer are for writing and thus may require flushing.
	Necessary to distinguish whether they need to be written to disk when
	in mixed read/write mode and doing a mixture of writing followed by
	reading.  http://trac.osgeo.org/gdal/ticket/1758

2007-11-23  Andrey Kiselev  <<EMAIL>>

	* configure.com, libtiff/tif_vms.c: Better OpenVMS support. Patches
	from Alexey Chupahin.

2007-11-02  Frank Warmerdam  <<EMAIL>>

	* tif_write.c: Rip out the fancy logic in TIFFAppendToStrip() for
	establishing if an existing tile can be rewritten to the same location
	by comparing the current size to all the other blocks in the same
	directory.  This is dangerous in many situations and can easily
	corrupt a file.  (observed in esoteric GDAL situation that's hard to
	document).  This change involves leaving the stripbytecount[] values
	unaltered till TIFFAppendToStrip().  Now we only write a block back
	to the same location it used to be at if the new data is the same
	size or smaller - otherwise we move it to the end of file.

	* tif_dirwrite.c: Try to avoid writing out a full readbuffer of tile
	data when writing the directory just because we have BEENWRITING at
	some point in the past.  This was causing odd junk to be written out
	in a tile of data when a single tile had an interleaving of reading
	and writing with reading last.  (highlighted by gdal
	autotest/gcore/tif_write.py test 7.

	* tif_predict.c: use working buffer in PredictorEncodeTile to avoid
	modifying callers buffer.
	http://trac.osgeo.org/gdal/ticket/1965

	* tif_predict.c/h: more fixes related to last item, keeping a
	distinct pfunc for encode and decode cases as these were getting
	mixed up sometimes.
	http://trac.osgeo.org/gdal/ticket/1948

2007-11-01  Frank Warmerdam  <<EMAIL>>

	* tif_predict.c/h, tif_lzw.c, tif_zip.c: Improvements so that
	predictor based encoding and decoding works in read-write update
	mode properly.
	http://trac.osgeo.org/gdal/ticket/1948

2007-10-24  Joris Van Damme  <<EMAIL>>

	* tif_dirread.c: Fixed problem with bogus file triggering
	assert(td->td_planarconfig == PLANARCONFIG_CONTIG) in
	ChopUpSingleUncompressedStrip

2007-10-22  Joris Van Damme  <<EMAIL>>

	* tif_jpeg.c: Resolved buffer incrementation bug that lead to faulty images
	at best, access violation at worst, when subsampled JPEG compressed imagery
	is decoded without the JPEG_COLORMODE feature

2007-10-11  Frank Warmerdam  <<EMAIL>>

	* html/index.html: Update "people responsible" section.

2007-10-05  Frank Warmerdam  <<EMAIL>>

	* tools/tiff2pdf.c: Fix problem with alpha setting in some cases
	as reported on the mailing list.

2007-10-01  Joris Van Damme  <<EMAIL>>

	* changed some more incorrect %lud printf flags to %lu

2007-09-29  Joris Van Damme  <<EMAIL>>

	* tif_dirread.c: Strip chopping interfered badly with uncompressed
	subsampled images because it tried to divide subsampled rowblocks,
	leading to all sorts of errors throughout the library for these
	images. Fixed by making strip chopping divide in row counts that
	are a multiple of vertical subsampling value.

2007-09-28  Joris Van Damme  <<EMAIL>>

	* tif_dirread.c: Logical cast working around compiler warning

	* tif_read.c: Correction of some error flags and parameter lists

2007-09-27  Joris Van Damme  <<EMAIL>>

	* tif_dirread.c: Made calculation of td_maxsamplevalue more robust
	when dealing with large bitspersample values, shutting up purification
	tools that warn about truncation, though it remains incorrect and
	indicates a conceptual problem there.

	* tif_open.c: Moved early exit in case of 'h' flag (to disable reading
	of first IFD) to proper place because it badly interfered with memory
	mapping, resulting in mapping flag even with dummy mapping functions
	that returned 0 whilst at the same time the mapping tif_size wasn't
	set, thus resulting in continuous incorrect beyond-eof errors.

2007-09-24  Joris Van Damme  <<EMAIL>>

	* tif_dirinfo.c: Fixed (MSVC) compiler reports about
	inconsistent use of const in tiffFields and exifFields definition

2007-09-20  Frank Warmerdam  <<EMAIL>>

	* tif_dirwrite.c: Always write tile/strip offsets and sizes
	using LONG8 type when output format is BigTIFF.  The
	TIFFWriteDirectoryTagLongLong8Array() function was restructured
	accordingly.

	* tif_dirread.c: Improvements to error reporting text in
	TIFFFetchDirectory().

2007-09-19  Bob Friesenhahn  <<EMAIL>>

	* test/images: Added a small collection of test images for use by
	test programs and scripts.
	* test/tiffinfo.sh: A trivial example test script.
	* test/common.sh: Added small script for setting the environment
	used by script-based tests.

2007-08-24  Frank Warmerdam  <<EMAIL>>

	* tif_dirwrite.c: Write the tif_nextdiroff value instead of a fixed
	zero when writing directory contents to preserve the ability to
	rewrite directories in place, even in the middle of a directory
	chain.

	* tif_dirinfo.c:  _TIFFMergeFields() now only merges in field
	definitions that are missing.  Existing definitions are silently
	ignored.

	* tif_dirread.c: Add runtime error for fields for which no definition
	is found (in addition to an assert for developers) in
	TIFFFetchNormalTag().  Not sure if this is needed, but it seems
	prudent.

2007-08-10  Joris Van Damme  <<EMAIL>>

	* libtiff/tif_getimage.c: removed SubsamplingHor and SubsamplingVer
	from _TIFFRGBAImage structure to revert unwanted ABI change.

2007-08-10  Joris Van Damme  <<EMAIL>>

	* libtiff/tif_win32.c: use SetFilePointer instead of
	SetFilePointerEx, as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1580

2007-07-19  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_stream.cxx: Put all callback functions declarations
	inside extern "C" block.

	* libtiff/{tif_lzw.c, tif_luv.c, tif_dumpmode.c, tif_print.c,
	tif_read.c, tif_strip.c, tif_thunder.c}: Use "%I64d" printf()
	formatter instead of "%lld" with MSVC compiler.

	* libtiff/{tiffiop.h, tif_aux.c}:  Added _TIFFUInt64ToFloat() and
	_TIFFUInt64ToDouble() functions.

2007-07-18  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: Handle the case of MSVC 6 when using 64-bit
	integer constants.

	* libtiff/{Makefile.am, Makefile.v}: Do not distribute tiffconf.h,
	remove tif_config.h/tiffconf.h during cleaning. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1573

	* libtiff/tif_unix.c: Do not use O_LARGEFILE. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1577

2007-07-13  Andrey Kiselev  <<EMAIL>>

	* libtiff 4.0.0alpha released.

2007-07-12  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2pdf.c: Added missed extern optind as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1567

	* libtiff/{tif_close.c, tif_dirinfo.c, tiffiop.c, tif_dirread.c,
	tif_dir.h, tif_dir.c, tiffio.h}: Transition to the new-style tag
	extending scheme completed.

2007-07-11  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_stream.cxx: Adapt to use toff_t again.  Update to
	use standard C++ library size types and attempt to detect overflow
	cases.

2007-07-08  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_jpeg.c, tif_dir.h, tif_dir.c, tif_dirinfo.c, tiffio.h,
	tif_ojpeg.c, tif_print.c, tif_fax3.c, tif_dirread.c}: More work on new
	tag extending scheme. Use the new scheme everywhere.

	* libtiff/{tif_zip.c, tif_predict.c, tif_pixarlog.c, tif_luv.c,
	tif_fax3.c, tif_dirread.c, tif_dirwrite.c, tif_close.c, tif_ojpeg.c,
	tif_jpeg.c, tif_dirinfo.c, tif_dir.h, tiffio.h, tiffiop.h}:
	TIFFFIeldInfo structure replaced with TIFFField structure.
	TIFFFieldInfo retained for the backward compatibility.

2007-07-05  Bob Friesenhahn  <<EMAIL>>

	* tools/tiff2pdf.c: Fix a compile problem when JPEG_SUPPORT is not
	defined.

2007-07-04  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dir.c, tiff.h, tiffio.h, libtiff.def}: Unused
	TIFFReassignTagToIgnore() function and TIFFIgnoreSense enumeration
	removed.

	* libtiff/{tif_dirinfo.c, tif_fax3.c, tif_jbig.c, tif_jpeg.c}: Move
	tags TIFFTAG_FAXRECVPARAMS, TIFFTAG_FAXSUBADDRESS,
	TIFFTAG_FAXRECVTIME and TIFFTAG_FAXDCS to the common tag directory.
	These tags are not codec-specific and relate to image content, so
	process them as other normal tags.

	* libtiff/{tiffio.h, tif_dir.h}: TIFFTagValue structure moved from the
	public tiffio.h to private tif_dir.h.

	* contrib/{acorn, mac-cw, mac-mpw}: Removed as unmaintained and
	outdated.

2007-07-03  Andrey Kiselev  <<EMAIL>>

	* libtiff{tif_acorn.c, tif_apple.c, tif_atari.c, tif_msdos.c,
	tif_win3.c}: Obsoleted portability stuff removed.

	* tools/tiff2ps.c:  Added support 16-bit images as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1566

	Patch from William Bader.

	* tools/tiff2pdf.c: Fix for TIFFTAG_JPEGTABLES tag fetching and
	significant upgrade of the whole utility as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1560

	Now we don't need tiffiop.h in tiff2pdf anymore and will open output
	PDF file using TIFFClientOpen() machinery as it is implemented
	by Leon Bottou.

2007-06-26  Bob Friesenhahn  <<EMAIL>>

	* configure.ac: Fix typo when substituting value for unsigned 8 bit type.
	Added support for a TIFF_PTRDIFF_T type to use when doing pointer arithmetic.
	Added support for a TIFF_SSIZE_T in order to return memory sizes but still
	allow returning -1 for errors.
	* libtiff/tiffconf.vc.h: Add porting type defintions for WIN32.

2007-06-25  Bob Friesenhahn  <<EMAIL>>

	* port/strtoull.c: New porting function in case strtoull() is not
	available on the target system.
	* configure.ac: Add configure support for determining sized types
	in a portable way and performing necessary substitutions in
	tif_config.h and tiffconf.h.  Updated tiff.h to use the new
	definitions.

2007-04-27  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2pdf.c: Check the tmpfile() return status as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=154

2007-04-07  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dir.h, tif_dirread.c, tif_dirinfo.c, tif_jpeg.c,
	tif_fax3.c, tif_jbig.c, tif_luv.c, tif_ojpeg.c, tif_pixarlog.c,
	tif_predict.c, tif_zip.c}: Finally fix bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1274

	by introducing _TIFFMergeFieldInfo() returning integer error status
	instead of void in case of problems with field merging (e.g., if the
	field with such a tag already registered). TIFFMergeFieldInfo() in
	public API remains void. Use _TIFFMergeFieldInfo() everywhere and
	check returned value.

2007-04-07  Frank Warmerdam  <<EMAIL>>

	* contrib/addtiffo/tif_overview.c: Fix problems with odd sized output
	blocks in TIFF_DownSample_Subsampled() (bug 1542).

2007-04-06  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: Changed JPEGInitializeLibJPEG() so that it
	will convert from decompressor to compressor or compress to decompress
	if required by the force arguments.  This works around a problem in
	where the JPEGFixupTestSubsampling() may cause a decompressor to
	be setup on a directory when later a compressor is required with the
	force flag set.  Occurs with the addtiffo program for instance.

2007-04-06  Andrey Kiselev  <<EMAIL>>

	* tools/tiffcrop.c, man/tiffcrop.1: Significant update in
	functionality from Richard Nolde. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1525

2007-03-28  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_fax3.c: "inline static" -> "static inline" for IRIC CC.

2007-03-17  Joris Van Damme  <<EMAIL>>

	* start of BigTIFF upgrade - CVS HEAD unstable until further notice

2007-03-07  Joris Van Damme  <<EMAIL>>

	* libtiff/tif_getimage.c: workaround for 'Fractional scanline' error reading
	OJPEG images with rowsperstrip that is not a multiple of vertical subsampling
	factor. This bug is mentioned in:
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1390
	http://www.asmail.be/msg0054766825.html

2007-03-07  Joris Van Damme  <<EMAIL>>

	* libtiff/tif_win32.c: made inclusion of windows.h unconditional

	* libtiff/tif_win32.c: replaced preprocessor indication for consiously
	unused arguments by standard C indication for the same

2007-02-27  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: Use uint32 type instead of tsize_t in byte
	counters in TIFFFetchData(). Should finally fix the issue

	http://bugzilla.remotesensing.org/show_bug.cgi?id=890

2007-02-24  Andrey Kiselev  <<EMAIL>>

	* tools/tiffset.c: Properly handle tags with TIFF_VARIABLE writecount.
	As per bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1350

	* libtiff/tif_dirread.c: Added special function to handle
	SubjectDistance EXIF tag as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1362

	* tools/tiff2pdf.c: Do not assume inches when the resolution units
	do not specified. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1366

	* tools/{tiffcp.c, tiffcrop.c}: Do not change RowsPerStrip value if
	it was set as infinite. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1368

	* tools/tiffcrop.c, man/tiffcrop.1: New tiffcrop utility contributed
	by Richard Nolde. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1383

2007-02-22  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dir.c: Workaround for incorrect TIFFs with
	ExtraSamples == 999 produced by Corel Draw. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1490

	* libtiff/{tif_dirread.c, tif_read.c}: Type of the byte counters
	changed from tsize_t to uint32 to be able to work with data arrays
	larger than 2GB. Fixes bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=890

	Idea submitted by Matt Hancher.

2007-01-31  Andrey Kiselev  <<EMAIL>>

	* tools/tif2rgba.c: This utility does not work properly on big-endian
	architectures. It was fixed including the bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1149

2007-01-15  Mateusz Loskot <<EMAIL>>

	* Submitted libtiff port for Windows CE platform
	* libtiff/tif_config.wince.h: Added configuration header for WinCE.
	* libtiff/tiffconf.wince.h: Ported old configuration header for WinCE.
	* libtiff/tif_wince.c: Added WinCE-specific implementation of some
	functons from tif_win32.c.
	* libtiff/tif_win32.c: Disabled some functions already reimplemented in tif_wince.c.
	* libtiff/tiffiop.h, port/lfind.c: Added conditional include of some
	standard header files for Windows CE build.
	* tools/tiffinfoce.c: Ported tiffinfo utility for Windows CE.

2006-11-19  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_write.c: TIFFAppendToStrip() - clear sorted flag if
	we move a strip.
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1359

2006-10-13  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dir.c: More fixes for vulnerabilities, reported
	in Gentoo bug ():

	http://bugs.gentoo.org/show_bug.cgi?id=142383

	* libtiff/contrib/dbs/xtiff/xtiff.c: Make xtiff utility compilable.
	Though it is still far from the state of being working and useful.

2006-10-12  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_fax3.c: Save the state of printdir codec dependent
	method.

	* libtiff/tif_jpeg.c: Save the state of printdir codec dependent method
	as per bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1273

	* libtiff/tif_win32.c: Fixed problem with offset value manipulation
	as per bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1322

	* libtiff/{tif_read.c, tif_jpeg.c, tif_dir.c}: More fixes for
	vulnerabilities, reported in Gentoo bug ():

	http://bugs.gentoo.org/show_bug.cgi?id=142383

2006-09-28  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_fax3.c, tif_next.c, tif_pixarlog.c}: Fixed multiple
	vulnerabilities, as per	Gentoo bug ():

	http://bugs.gentoo.org/show_bug.cgi?id=142383

2006-09-27  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_lzw.c, libtiff/tif_zip.c: Fixed problems with mixing
	encoding and decoding on the same read-write TIFF handle.  The LZW
	code can now maintain encode and decode state at the same time. The
	ZIP code will switch back and forth as needed.
	http://bugzilla.remotesensing.org/show_bug.cgi?id=757

2006-09-20  Frank Warmerdam  <<EMAIL>>

	* libtiff: Rename config.h.vc and tif_config.h.vc to config.vc.h and
	tif_config.vc.h for easier identification by folks using an IDE.

2006-07-25  Frank Warmerdam  <<EMAIL>>

	* tif_msdos.c: Avoid handle leak for failed opens.  c/o Thierry Pierron

2006-07-19  Frank Warmerdam  <<EMAIL>>

	* tif_dirwrite.c: take care not to flush out buffer of strip/tile
	data in _TIFFWriteDirectory if TIFF_BEENWRITING not set.  Relates
	to bug report by Peng Gao with black strip at bottom of images.

2006-07-12  Frank Warmerdam  <<EMAIL>>

	* tif_dirwrite.c: make sure to use uint32 for wordcount in
	TIFFWriteNormanTag if writecount is VARIABLE2 for ASCII fields.
	It already seems to have been done for other field types.  Needed
	for "tiffset" on files with geotiff ascii text.

2006-07-04  Bob Friesenhahn  <<EMAIL>>

	* {configure.ac, libtiff/tif_config.h.vc, libtiff/tif_jbig.c}
	(JBIGDecode): jbg_newlen is not available in older JBIG-KIT and
	its use does not appear to be required, so use it only when it is
	available.

2006-06-24  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirinfo.c: Added missed EXIF tag ColorSpace (40961).

	* libtiff/tif_dirread.c: Move IFD fetching code in the separate
	function TIFFFetchDirectory() avoiding code duplication in
	TIFFReadDirectory() and TIFFReadCustomDirectory().

2006-06-19  Frank Warmerdam  <<EMAIL>>

	* tools/tiff2pdf.c: Fix handling of -q values.
	http://bugzilla.remotesensing.org/show_bug.cgi?id=587

2006-06-17  Frank Warmerdam  <<EMAIL>>

	* tif_readdir.c: Added case in EstimateStripByteCounts() for tiled
	files.  Modified TIFFReadDirectory() to not invoke
	EstimateStripByteCounts() for case where entry 0 and 1 are unequal
	but one of them is zero.
	  http://bugzilla.remotesensing.org/show_bug.cgi?id=1204

2006-06-08  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_open.c, tif_dirread.c, tiffiop.h}: Move IFD looping
	checking code in the separate function TIFFCheckDirOffset().

	* libtiff/tif_aux.c: Added _TIFFCheckRealloc() function.

	* tools/tiffcmp.c: Fixed floating point comparison logic as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1191

	* libtiff/tif_fax3.c: Fixed problems in fax decoder as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1194

	* tools/tiff2pdf.c: Fixed buffer overflow condition in
	t2p_write_pdf_string() as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1196

2006-06-07  Andrey Kiselev  <<EMAIL>>

	* {configure, configure.ac, libtiff/tif_jbig.c, tools/tiffcp.c}: Added
	support for JBIG compression scheme (34661 code) contributed by Lee
	Howard. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=896

	* configure, configure.ac: OJPEG support enabled by default.

	* contrib/ojpeg/: Removed. New OJPEG support does not need this patch.

2006-06-03  Bob Friesenhahn  <<EMAIL>>

	* libtiff/{tif_dirinfo.c, tif_print.c} : Fix crash in
	TIFFPrintDirectory().  Joris Van Damme authored the fix.

2006-04-21  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2pdf.c: Unified line ending characters (always use '\n')
	as per bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1163

	* README.vms, Makefile.am, configure.com, libtiff/{Makefile.am,
	tif_config.h-vms, tif_stream.cxx, tif_vms.c, tiffconf.h-vms}:
	Added support for OpenVMS by Alexey Chupahin, <EMAIL>.

2006-04-20  Andrey Kiselev  <<EMAIL>>

	* tools/{fax2ps.c, fax2tiff.c, ppm2tiff.c, ras2tiff.c, tiff2pdf.c}:
	Properly set the binary mode for stdin stream as per bug
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1141

	* man/{bmp2tiff.1, fax2ps.1, fax2tiff.1, gif2tiff.1, ras2tiff.1,
	raw2tiff.1, rgb2ycbcr.1, sgi2tiff.1, tiff2bw.1, tiff2pdf.1, tiff2ps.1,
	tiff2rgba.1, tiffcmp.1, tiffcp.1, tiffdither.1,	tiffdump.1, tiffgt.1,
	tiffset.1}: Improvements in page formatting as per bug
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1140

	* html/tools.html, html/man/Makefile.am, tools/tiff2pdf.c: Fixed
	typos as per bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1139

2006-04-18  Frank Warmerdam  <<EMAIL>>

	* nmake.opt: use /EHsc for VS2005 compatibility.  Also define
	_CRT_SECURE_NO_DEPRECATE to avoid noise on VS2005.

2006-04-12  Joris Van Damme  <<EMAIL>>

	* libtiff/tif_getimage.c: Added support for planarconfig separate
	non-subsampled YCbCr (i.e. separate YCbCr with subsampling [1,1])

2006-04-11  Joris Van Damme  <<EMAIL>>

	* libtiff/tif_getimage.c: Revision of all RGB(A) put routines
	- Conversion of unassociated alpha to associated alpha now done with
	  more performant LUT, and calculation more correct
	- Conversion of 16bit data to 8bit data now done with
	  more performant LUT, and calculation more correct
	- Bugfix of handling of 16bit RGB with unassociated alpha

2006-04-11  Joris Van Damme  <<EMAIL>>

	* libtiff/tif_getimage.c:
	- When there is no alpha, gtTileSeparate and gtStripSeparate allocated
	  buffer for alpha strile and filled it, only to never read it back.
	  Removed allocation and fill.
	- Minor rename of vars in gtTileSeparate and gtStripSeparate
	  anticipating planned functionality extension

2006-04-08  Joris Van Damme  <<EMAIL>>

	* libtiff/tif_getimage.c: renamed pickTileContigCase to PickContigCase
	and pickTileSeparateCase to PickSeparateCase as both work on strips as
	well

	* libtiff/tif_getimage.c: moved img->get selection from
	TIFFRGBAImageBegin into PickContigCase and PickSeparateCase to create
	logical hook for planned functionality extension

2006-04-08  Joris Van Damme  <<EMAIL>>

	* libtiff/tif_ojpeg.c: resolved memory leak that was a consequence
	of inappropriate use of jpeg_abort instead of jpeg_destroy

2006-04-07  Joris Van Damme  <<EMAIL>>

	* libtiff/tif_getimage.c: replaced usage of TIFFScanlineSize in
	gtStripContig with TIFFNewScanlineSize so as to fix buggy behaviour
	on subsampled images - this ought to get sorted when we feel brave
	enough to replace TIFFScanlineSize altogether

	* libtiff/tif_ojpeg.c: fixed bug in OJPEGReadSkip

2006-04-04  Joris Van Damme  <<EMAIL>>

	* libtiff/tiffio.h: added new type tstrile_t

	* libtiff/tif_dir.h: changed types of td_stripsperimage and td_nstrips
	to new tstrile_t, types of td_stripoffset and td_stripbytecount to
	toff_t*

	* libtiff/tif_ojpeg.c: totally new implementation

	* libtiff/tif_dirread.c: added several hacks to suit new support of
	OJPEG

	* libtiff/tif_getimage.c: removed TIFFTAG_JPEGCOLORMODE handling
	of OJPEG images in favor of tif_getimage.c native handling of
	YCbCr and desubsampling

2006-03-29  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: JPEGVSetField() so that altering the photometric
	interpretation causes the "upsampled" flag to be recomputed.  Fixes
	peculiar bug where photometric flag had to be set before jpegcolormode
	flag.

2006-03-25  Joris Van Damme  <<EMAIL>>

	* libtiff/tif_jpeg.c: strip size related bugfix in encode raw

	* libtiff/tif_strip.c: temporarilly added two new versions of
	TIFFScanlineSize
	  - TIFFNewScanlineSize: proposed new version, after all related
	    issues and side-effects are sorted out
	  - TIFFOldScanlineSize: old version, from prior to 2006-03-21 change
	This needs further sorting out.

2006-03-25  Joris Van Damme  <<EMAIL>>

	* contrib/addtiffo/tif_ovrcache.c: bugfix to correctly pass size
	of last truncated strip data to TIFFWriteEncodedStrip

2006-03-25  Joris Van Damme  <<EMAIL>>

	* libtiff/{tif_jpeg.c, tif_strip.c}: bugfix of tif_jpeg decode raw

2006-03-25  Joris Van Damme  <<EMAIL>>

	* libtiff/tif_getimage.c: bugfix/rewrite of putcontig8bitYCbCr22tile

	* libtiff/tif_getimage.c: added putcontig8bitYCbCr12tile

	* libtiff/tif_read.c: added support for new TIFF_NOREADRAW flag to
	prepare	the path for new tif_ojpeg.c

2006-03-23  Andrey Kiselev  <<EMAIL>>

	* libtiff 3.8.2 released.

	* tools/Makefile.am: Use runtime paths linker flags when rpath
	option enabled.

2006-03-21  Andrey Kiselev  <<EMAIL>>

	* libtiff/libtiff.def: Added missed exports as per bug
	http://bugzilla.remotesensing.org/attachment.cgi?id=337

	* contrib/addtiffo/Makefile.vc, libtiff/Makefile.vc, port/Makefile.vc,
	tools/Makefile.vc: Makefiles improvements as per bug
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1128

	* nmake.opt libtiff/{tif_config.h.vc, tif_unix.c, tiffio.h},
	tools/{fax2ps.c, fax2tiff.c, tiff2pdf.c}: Fixed win32 I/O functions
	usage as per bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1127

	* libtiff/tif_strip.c: Take subsampling in account when calculating
	TIFFScanlineSize().

	* tools/tiffcp.c: Do not set RowsPerStrip bigger than image length.

2006-03-17  Andrey Kiselev  <<EMAIL>>

	* tools/fax2tiff.c: Fixed wrong TIFFerror() invocations as per bug
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1125

	* tools/fax2ps.c: Fixed reading the input stream from stdin as per bug
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1124

2006-03-16  Andrey Kiselev  <<EMAIL>>

	* libtiff/tiffiop.h: Added decalration for
	_TIFFSetDefaultCompressionState().

	* libtiff/{tif_jpeg.c, tif_fax3.c, tif_zip.c, tif_pixarlog.c,
	tif_lzw.c, tif_luv.c}: Use _TIFFSetDefaultCompressionState() in all
	codec cleanup methods. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1120

2006-03-15  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_jpeg.c: Do not cleanup codec state in TIFFInitJPEG(). As
	per bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1119

	* tools/raw2tiff.c: Do not set RowsPerStrip larger than ImageLength.
	As per bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1110

	* libtiff/tiffiop.h: dblparam_t typedef removed; GLOBALDATA macro
	removed; move here the STRIP_SIZE_DEFAULT macro definition.

	* libtiff/{tif_dirread.c, tif_strip.c}: Removed STRIP_SIZE_DEFAULT
	macro definition.

	* libtiff/tif_dir.c: Use double type instead of dblparam_t.

2006-03-14  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: Do not check the PlanarConfig tag presence
	in TIFFReadDirectory, because it is always set at the start of
	function and we allow TIFFs without that tag set.

2005-03-13  Andrey Kiselev  <<EMAIL>>

	* libtiff 3.8.1 released.

2006-03-07  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: Fixed error reporting in TIFFFetchAnyArray()
	function as per bug
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1102

	* libtiff/tif_dirread.c: More wise check for integer overflow
	condition as per bug
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1102

	* libtiff/{tif_jpeg.c, tif_pixarlog.c, tif_fax3.c, tif_zip.c}:
	Properly restore setfield/getfield methods in cleanup functions. As
	per bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1102

2006-03-03  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_predict.c, tif_predict.h}: Added new function
	TIFFPredictorCleanup() to restore parent decode/encode/field methods.

	* libtiff/{tif_lzw.c, tif_pixarlog.c, tif_zip.c}: Use
	TIFFPredictorCleanup() in codec cleanup methods. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1102

	* libtiff/tif_dirread.c: Fixed integer overflow condition in
	TIFFFetchData() function. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1102

2006-03-01  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_ojpeg.c: Set the ReferenceBlackWhite with the
	TIFFSetField() method, not directly. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1043

	* tools/ppm2tiff.c: Added support for PBM files as per bug
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1044

2006-02-27  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_write.c: Small code rearrangement in TIFFWriteScanline()
	to avoid crash as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1081.

2006-02-26  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2pdf.c: Functions t2p_sample_rgbaa_to_rgb() and
	t2p_sample_rgba_to_rgb() was used in place of each other, that was
	resulted in problems with RGBA images with associated alpha.
	As per bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1097

2006-02-23  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirwrite.c: Properly write TIFFTAG_DOTRANGE tag as per
	bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1088.

	* libtiff/tif_print.c: Properly read TIFFTAG_PAGENUMBER,
	TIFFTAG_HALFTONEHINTS, TIFFTAG_YCBCRSUBSAMPLING and TIFFTAG_DOTRANGE
	tags as per bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1088.

	* tools/tiff2ps.c: Properly scale all the pages when converting
	multipage TIFF with /width/height/center options set. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1080

2006-02-15  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2pdf.c: Do not create output file until all option checks
	will be done. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1072

	* tools/bmp2tiff.c: Added ability to create multipage TIFFs from the
	list of input files as per bug:

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1077

2006-02-09  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_tile.c: Fix error reporting in TIFFCheckTile() as per
	bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1063.

	* tools/tiffgt.c: Avoid crashing in case of image unsupported by
	TIFFRGBAImage interface.

	* libtiff/tif_color.c: Avoid overflow in case of wrong input as per
	bug http://bugzilla.remotesensing.org/show_bug.cgi?id=1065.

2006-02-07  Frank Warmerdam  <<EMAIL>>

	* tools/tiff2pdf.c: Fixed support for non-YCbCr encoded JPEG
	compressed TIFF files, per submission from Dan Cobra.

2006-02-07  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dirread.c, tif_packbits.c, tif_win32.c}: Properly
	cast values to avoid warnings. As per bug
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1033.

	* libtiff/tif_dirinfo.c: Use TIFF_NOTYPE instead of 0 when
	appropriate. As per bug
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1033.

	* libtiff/tif_aux.c: Fixed type of temporary variable in
	_TIFFCheckMalloc() as per bug
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1033.

2006-02-06  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_aux.c: Return static array when fetching default
	YCbCrCoefficients (another problem, reported a the
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1029 entry).

2006-02-03  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dir.c: Special handling for PageNumber, HalftoneHints,
	YCbCrSubsampling and DotRange tags as per bugs

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1029
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1034

	* libtiff/tif_dirread.c: Use _TIFFGetExifFieldInfo() instead of
	_TIFFGetFieldInfo() in TIFFReadEXIFDirectory() call as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1026.

2006-01-23  Andrey Kiselev  <<EMAIL>>

	* libtool related stuff updated from the 2.1a branch.

2006-01-11  Frank Warmerdam  <<EMAIL>>

	* tools/bmp2tiff,pal2rgb,ppm2tiff,ras2tiff,raw2tiff,sgi2tiff,
	tiff2bw,tiffcp: Fixed jpeg option processing so -c jpeg:r:50 works
	properly as per bug:
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1025

2006-01-09  Bob Friesenhahn  <<EMAIL>>

	* configure.ac: Fix with_default_strip_size comparison as reported
	by Norihiko Murase.

2006-01-08  Bob Friesenhahn  <<EMAIL>>

	* test/Makefile.am (LIBTIFF): Due to linking against libtiff
	incorrectly, tests were not actually testing the uninstalled
	libtiff.  Now they are.

2006-01-04  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirinfo.c: Change definitions for TIFFTAG_ICCPROFILE,
	TIFFTAG_PHOTOSHOP, TIFFTAG_RICHTIFFIPTC, TIFFTAG_XMLPACKET: readcount
	should be uint32 value.

2006-01-02  Bob Friesenhahn  <<EMAIL>>

	* html/man/Makefile.am (htmldoc): Fix htmldoc rule so that it can
	be used if build directory is not the same as source directory.
	* man/{TIFFGetField.3tiff, TIFFSetField.3tiff}: Documented
	TIFFTAG_PHOTOSHOP, TIFFTAG_RICHTIFFIPTC, and TIFFTAG_XMLPACKET,
	and re-sorted tag names in alphabetical order.

2005-12-29  Andrey Kiselev  <<EMAIL>>

	* libtiff 3.8.0 released.

2005-12-28  Bob Friesenhahn  <<EMAIL>>

	* tools/bmp2tiff.c (main): Fixed warning regarding returning
	inconsistent types from a condition.
	* tools/tiffcmp.c (CheckLongTag): Eliminate warning due to printf
	format.
	* tools/bmp2tiff.c: Reduce compilation warnings on big-endian CPUs.

2005-12-28  Joris Van Damme  <<EMAIL>>

	* html/{index.html, support.hml, libtiff.html}: Cleaned up HTML

2005-12-27  Andrey Kiselev  <<EMAIL>>

	* libtiff/tiffio.h: Added VC_EXTRALEAN definition before including
	windows.h, to reduce the compile time.

2005-12-26  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_jpeg.c: Improve compilation under MinGW.

2005-12-26  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dir.c, tif_dir.h, tif_dirread.c, tif_dirinfo.c}:
	tiffFieldInfo and exifFieldInfo arrays definitions moved back to
	tif_dirinfo.c; added _TIFFGetFieldInfo() and _TIFFGetExifFieldInfo()
	private functions to retrieve FieldInfo arrays.

2005-12-24  Bob Friesenhahn  <<EMAIL>>

	* html/build.html: Added some additional instructions for when
	building using MSVC under Windows.  Also fixed two HTML syntax
	errors and used HTML Tidy to tidy up the HTML syntax and
	formatting.

2005-12-24  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_aux.c, tif_dir.c, tif_dir.h, tif_dirwrite.c,
	tif_print.c, tif_getimage.c}: Make InkSet, NumberOfInks, DotRange and
	StoNits tags custom.

2005-12-23  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_aux.c, tif_dir.c, tif_dir.h, tif_print.c}: Make
	WhitePoint tag custom.

	* libtiff/{tif_dir.h, tiff.h}: More EXIF tags added.

2005-12-23  Joris Van Damme  <<EMAIL>>

	* libtiff/tiffio.h: fixed typo that potentially resulted in
	redefininition of USE_WIN32_FILEIO

	* libtiff/*: Added more 'dual-mode' error handling: Done TIFFWarning
	calls in core LibTiff.

2005-12-21  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dir.c, tif_dir.h, tif_print.c}: Make RichTIFFIPTC,
	Photoshop and ICCProfile tags custom.

2005-12-21  Joris Van Damme  <<EMAIL>>

	* libtiff/*, contrib/*: Added 'dual-mode' error handling, enabling
	newer code to get context indicator in error handler and still
	remain compatible with older code: Done TIFFError calls everywhere
	except in tools

2005-12-20  Andrey Kiselev  <<EMAIL>>

	* tools/tiffcp.c: Added many error reporting messages; fixed integer
	overflow as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=789

2005-12-16  Frank Warmerdam  <<EMAIL>>

	* contrib/addtiffo/*: Major upgrade by Joris to support subsampled
	YCbCr images in jpeg compressed TIFF files.

2005-12-14  Andrey Kiselev  <<EMAIL>>

	* tools/tiffcp.c: Return non-zero status when reading fails (again).

2005-12-13  Andrey Kiselev  <<EMAIL>>

	* tools/tiffcp.c: Return non-zero status when reading fails.

2005-12-12  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dir.h, tiff.h}: Added more EXIF tags.

2005-12-09  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dir.c, tif_dir.h, tif_print.c}: Make XMLPacket tag
	custom.

	* tools/tiffinfo.c: Print EXIF directory contents if exist.

	* libtiff/tiff.h: Few EXIF tag numbers added.

	* libtiff/{tif_dirinfo.c, tif_dirread.c, tif_dir.h, tif_dir.c,
	tiffio.h}: Preliminary support to read custom directories. New
	functions: TIFFReadCustomDirectory() and TIFFReadEXIFDirectory().

2005-12-07  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dirinfo.c, tif_dirread.c, tif_dir.h, tif_dir.c}:
	More work to implement custom directory read support.

	* libtiff/{tif_aux.c, tif_dirinfo.c, tif_dirread.c, tif_dir.h,
	tif_dir.c, tif_print.c}: Make YCbCrCoefficients and ReferenceBlackWhite
	tags custom.

2005-12-05  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: One more workaround for broken
	StripByteCounts tag. Handle the case when StripByteCounts array filled
	with completely wrong values.

2005-11-30  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirinfo.c: Release file descriptor in case of failure
	in the TIFFOpenW() function as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1003

	* libtiff/tif_dirinfo.c: Correctly yse bsearch() and lfind()
	functions as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=1008

2005-11-20  Frank Warmerdam  <<EMAIL>>

	* tif_open.c, tiff.h, tiffdump.c: Incorporate preliminary support
	for MS MDI format.
	http://bugzilla.remotesensing.org/show_bug.cgi?id=1002

	* .cvsignore: many files added, and a few update according
	to suggestion of Brad HArds on tiff mailing list.

2005-11-03  Frank Warmerdam  <<EMAIL>>

	* libtiff/libtiff.def, tiffiop.h, tiffio.h: Made TIFFFreeDirectory
	public.

2005-10-31  Andrey Kiselev  <<EMAIL>>

	* tools/fax2tiff.c: Properly calculate sizes of temporary arrays
	as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=943

	* tools/fax2tiff.c: Added option '-r' to set RowsPerStrip parameter
	as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=944

	* tools/tiffdump.c: Fixed typeshift and typemask arrays initialization
	problem as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=946

	* tools/bmp2tiff.c: Fixed possible integer overflow error as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=965

	* libtiff/tif_dirinfo.c: Make XResolution, YResolution and
	ResolutionUnit tags modifiable during write process. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=977

	* tools/tiffsplit.c: Copy fax related fields over splitted parts
	as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=983

2005-10-21  Frank Warmerdam  <<EMAIL>>

	* tif_dirread.c: Don't try and split single strips into "0" strips
	in ChopUpSingleUncompressedStrip.  This happens in some degenerate
	cases (like 1x1 files with stripbytecounts==0 (gtsmall.jp2 embed tiff)

2005-10-20  Joris Van Damme  <<EMAIL>>

	* tif_fax3.c: changed 'at scanline ...' style warning/errors
	with incorrect use of tif_row, to 'at line ... of
	strip/tile ...' style

2005-10-15  Frank Warmerdam  <<EMAIL>>

	* tif_write.c: fixed setting of planarconfig as per bug report
	on the mailing list from Joris.

2005-10-07  Andrey Kiselev  <<EMAIL>>

	* configure.ac, configure, nmake.opt, libtiff/{tif_config.h,
	tif_dirread.c}: Make the default strip size configurable via the
	--with-default-strip-size and STRIP_SIZE_DEFAULT options.

2005-09-30  Bob Friesenhahn  <<EMAIL>>

	* html/support.html: Fixed link to documentation on Greg Ward's
	LogLuv TIFF format.

2005-09-28  Andrey Kiselev  <<EMAIL>>

	* tools/tiffdump.c: Fixed crash when reading malformed tags.

2005-09-20  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2pdf.c: Added missed 'break' statement as per bug
	http://bugzilla.remotesensing.org/show_bug.cgi?id=932

2005-09-12  Andrey Kiselev  <<EMAIL>>

	* libtiff 3.7.4 released.

	* {configure, configure.ac, Makefile.am, autogen.sh}: Applied patch
	from Patrick Welche (all scripts moved in the 'config' and 'm4'
	directories).

2005-09-12  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_open.c: reintroduce seek to avoid problem on solaris.

2005-09-05  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dir.c: When prefreeing tv->value in TIFFSetFieldV
	also set it to NULL to avoid double free when re-setting custom
	string fields as per:

	http://bugzilla.remotesensing.org/show_bug.cgi?id=922

2005-08-12  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_print.c: avoid signed/unsigned warning.

	* libtiff/tif_dirread.c: removed unused variable.

2005-07-30  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dir.c: Fixed up support for swapping "double complex"
	values (128 bits as 2 64 bits doubles).  GDAL gcore tests now
	pass on bigendian (macosx) system.

2005-07-28  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_aux.c, tif_dirread.c, tif_fax3.c, tiffiop.h}: Rename
	CheckMalloc() function to _TIFFCheckMalloc() and make it available
	globally as an internal helper routine.

2005-07-27  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dir.c: More improvements in the "pass by value" part of
	the custom tags handling code.

2005-07-26  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dirread.c, tif_dirinfo.c}: Do not upcast BYTEs to
	SHORTs in the TIFFFetchByteArray(). Remove TIFFFetchExtraSamples()
	function, use TIFFFetchNormalTag() instead as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=831

	Remove TIFFFetchExtraSamples() function, use TIFFFetchNormalTag()
	instead.

	* libtiff/tiffconf.h.in: One more attempt to fix the AIX bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=39

2005-07-25  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_print.c: Fixed printing of the BYTE and SBYTE arrays.

	* tools/tiffdump.c: Added support for TIFF_IFD datatype.

2005-07-21  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_write.c: Do not check the PlanarConfiguration field in
	the TIFFWriteCheck() function in case of single band images (as per
	TIFF spec).

2005-07-12  Andrey Kiselev  <<EMAIL>>

	* SConstruct, libtiff/SConstruct: Added the first very preliminary
	support for SCons software building tool (http://www.scons.org/).
	This is experimental infrastructure and it will exist along with the
	autotools mechanics.

2005-07-07  Andrey Kiselev  <<EMAIL>>

	* port/{getopt.c, strcasecmp.c, strtoul.c}: Update modules from
	the NetBSD source tree (the old	4-clause BSD license changed to
	the new 3-clause one).

	* configure.ac, port/lfind.c, libtiff/tiffiop.h: Added lfind()
	replacement module.

	* port/dummy.c: Make the dummy function static.

2005-07-06  Andrey Kiselev  <<EMAIL>>

	* tools/tiffcp.c: Fixed WhitePoint tag copying.

	* libtiff/{tif_dir.c, tif_dir.h, tif_dirinfo.c, tif_print.c}:
	Make FieldOfViewCotangent, MatrixWorldToScreen, MatrixWorldToCamera,
	ImageFullWidth, ImageFullLength and PrimaryChromaticities tags custom.

2005-07-04  Andrey Kiselev  <<EMAIL>>

	* libtiff 3.7.3 released.

	* configure, configure.ac: Do not use empty -R option when linking
	with --enable-rpath.

2005-07-01  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tiffiop.h, tif_open.c}: Added open option 'h' to avoid
	reading the first IFD when needed. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=875

	* libtiff/tif_color.c: Better use of TIFFmin() macro to avoid side
	effects.

2005-06-23  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2pdf.c: Print two characters per loop in the
	t2p_write_pdf_trailer(). As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=594

	* tools/tiffgt.c: Use MacOS X OpenGL framework when appropriate. As
	per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=844

	* acinclude.m4: Updated to latest OpenGL test macros versions.

	* libtiff/tiff.h: Use correct int size on Sparc 64bit/Sun compiler
	platform. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=855

2005-06-14  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirinfo.c: Added support for ClipPath, XClipPathUnits
	and YClipPathUnits tags.

2005-06-07  Andrey Kiselev  <<EMAIL>>

	* contrib/addtiffo/tif_ovrcache.c: Properly extract tile/strip size;
	use pixel sized shift in contigous case.

2005-06-06  Andrey Kiselev  <<EMAIL>>

	* contrib/addtiffo/{tif_overview.c, tif_ovrcache.c, tif_ovrcache.h}:
	Make overviews working for contiguos images.

2005-06-03  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_open.c: Replace runtime endianness check with the compile
	time one.

	* libtiff/tif_predict.c: Floating point predictor now works on
	big-endian hosts.

2005-06-01  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dir.c: Use _TIFFsetString() function when read custom
	ASCII values.

	* libtiff/{tif_dirinfo.c, tif_dir.h, tif_dir.c, tif_print.c}: Make
	DocumentName, Artist, HostComputer, ImageDescription, Make, Model,
	Copyright, DateTime, PageName, TextureFormat, TextureWrapModes and
	TargetPrinter tags custom.

	* libtiff/tif_jpeg.c: Cleanup the codec state depending on
	TIFF_CODERSETUP flag (to fix memry leaks).

	* libtiff/tif_jpeg.c: Initialize JPEGTables array with zero after
	allocating.

2005-05-26  Andrey Kiselev  <<EMAIL>>

	* configure.ac, libtiff/Makefile.am: Added workaround for
	OpenBSD/MirOS soname problem as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=838

	* libtiff/tif_dirwrite.c: Use tdir_count when calling
	TIFFCvtNativeToIEEEDouble() in the TIFFWriteDoubleArray() function as
	per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=845

2005-05-25  Andrey Kiselev  <<EMAIL>>

	* tools/ppm2tiff.c: Fixed format string when read PPM file header with
	the fscanf() function. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=861

	* libtiff/{tif_dirinfo.c, tif_print.c}: TIFFFetchByteArray() returns
	uint16 array when fetching the BYTE and SBYTE filds, so we should
	consider result as pointer to uint16 array and not as array of chars.
	As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=831

	* libtiff/tif_dir.c: More efficient custom tags retrieval as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=830

	* libtiff/tif_win32.c: Use FILE_SHARE_READ | FILE_SHARE_WRITE share
	mode in CreateFile() call as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=829

	* libtiff/Makefile.am: Fixed parallel compilation of the libtiff and
	libtiffxx libraries as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=826

	* contrib/addtiffo/{tif_overview.c, tif_ovrcache.h}: Sinchronized with
	GDAL.

2005-05-23  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: Substantial fix for addtiffo problems with
	JPEG encoded TIFF files.  Pre-allocate lots of space for jpegtables
	in directory.

2005-05-22  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirread.c: Changed the code that computes
	stripbytecount[0] if it appears bogus to ignore if stripoffset[0] is
	zero. This is a common case with GDAL indicating a "null" tile/strip.

2005-05-17  Andrey Kiselev  <<EMAIL>>

	* tools/tiffsplit.c: Check for JPEGTables tag presence before copying.

2005-05-06  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirread.c: Applied similar change to
	TIFFFetchPerSampleLongs and TIFFFetchPerSampleAnys.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=843

	* libtiff/tif_jpeg.c: added LIB_JPEG_MK1 support in JPEGDecodeRaw().

2005-05-06  Andrey Kiselev  <<EMAIL>>
	* tools/tiff2pdfr.c, man/tiff2pdf.1: Calculate the tile width properly;
	added new option '-b' to use interpolation in output PDF files (Bruno
	Ledoux).

2005-05-05  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirread.c: Ensure that broken files with too many
	values in PerSampleShorts work ok instead of crashing.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=843

2005-04-27  Andrey Kiselev  <<EMAIL>>

	* tools/tiffdither.c: Copy the PhotometricInterpretation tag from the
	input file.

2005-04-15  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_predict.c: Added ability to encode floating point
	predictor, as per TIFF Technical Note 3.

2005-04-14  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_predict.h, tif_predict.c}: Added ability to decode
	floating point predictor, as per TIFF Technical Note 3.

2005-04-13  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tiffio.h, tiffiop.h, tif_dir.c, tif_read.c, tif_swab.c}:
	Added _TIFFSwab24BitData() and TIFFSwabArrayOfLong() functions used to
	swap 24-bit floating point values.

	* libtiff/tiff.h: Added predictor constants.

2005-04-08  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tiffiop.h, tif_dir.c}: Use uint32 type for appropriate
	values in _TIFFVSetField() function. Inspired by the bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=816

	* man/TIFFSetField.3tiff: Fixed definition of the TIFFTAG_INKNAMES tag
	as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=816

2005-03-30  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_open.c: Do not read header in case the output file
	should be truncated (Ron).

	* libtiff/{tif_dirinfo.c, tif_config.h.vc}: Use lfind() instead
	of bsearch() in _TIFFFindFieldInfoByName() function (Ron).

	* libtiff/{tiff.h, tif_dirinfo.c}: Fixes in EXIF tag ordering (Ron).

2005-03-22  Andrey Kiselev  <<EMAIL>>

	* configure.ac, libtiff/Makefile.am: Use libtool machinery to pass
	rpath option.

2005-03-21  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dir.c, tif_print.c}: Handle all data types in custom
	tags.

2005-03-18  Andrey Kiselev  <<EMAIL>>

	* libtiff/dirinfo.c: Added DNG tags.

	* libtiff/{tif_dir.c, tif_print.c}: More improvements in custom tag
	handling code.

	* libtiff/tiff.h: More comments; added missed DNG tag (LensInfo);
	added DNG 1.1.0.0 tags.

	* tools/tif2pdf.c: Fixed problem with alpha channel handling as per
	bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=794

	* man/TIFFGetField.3tiff: Add a note about autoregistered tags.

2005-03-17  Andrey Kiselev  <<EMAIL>>

	* nmake.opt: Build with Win32 CRT library by default.

	* tools/tiff2ps.c: Fixed typo in page size handling code.

	* libtiff/{tif_dir.c, tif_print.c}: Support for custom tags, passed
	by value.

	* libtiff/{tiff.h, tif_dirinfo.c, tiffiop.h}: Added EXIF related tags.

2005-03-15  Andrey Kiselev  <<EMAIL>>

	* libtiff 3.7.2 released.

2005-03-09  Andrey Kiselev  <<EMAIL>>

	* tools/tiffcmp.c: Added ability to compare the 32-bit integer and
	floating point data; complain on unsupported bit depths.

2005-03-05  Andrey Kiselev  <<EMAIL>>

	* tif_stream.cxx: Use ios namespace instead of ios_base to support
	GCC 2.95.

	* libtiff/{tiff.h, tif_fax3.tif, tif_jpeg.c}: Applied correct patch from
	Lee Howard for HylaFax DCS tag
	(see http://bugzilla.remotesensing.org/show_bug.cgi?id=771)

2005-03-04  Andrey Kiselev  <<EMAIL>>

	* configure, configure.ac: Use -rpath option instead of -R as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=732

	* libtiff/{tiff.h, tif_fax3.tif, tif_jpeg.c}: Applied patch from Lee
	Howard to support a new tag TIFFTAG_FAXDCS (34911) used in HylaFax
	software. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=771

	* nmake.opt, html/build.html: Add more comments, change the config
	file organization a bit as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=764

	* tools/tiffcmp.c: Use properly sized buffer in short arrays comparison
	as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=785

2005-03-03  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: More logic to guess missed strip size as per
	bug http://bugzilla.remotesensing.org/show_bug.cgi?id=705

	* tools/fax2ps.c: Replace insecure mktemp() function with the
	tmpfile() as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=786

2005-02-04  Andrey Kiselev  <<EMAIL>>

	* libtiff/tiff.h: Changed the int8 definition to be always signed char
	as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=727

	* libtiff/tiffio.h: Move TIFFOpenW() function into the extern "C"{}
	block as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=763

2005-02-03  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffgt.c: Fix problem on big-endian CPUs so that images
	display more correctly.  Images display brighter than they should
	on a Sun workstation.

2005-02-03  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: Estimate strip size in case of wrong or
	suspicious values in the tags. As per bugs

	http://bugzilla.remotesensing.org/show_bug.cgi?id=705

	and

	http://bugzilla.remotesensing.org/show_bug.cgi?id=320

	* tools/tiff2ps.c: Fixed problem with page sizes as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=742

2005-01-31  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tiff.h (TIFFTAG_TILEWIDTH): Corrected description.
	(TIFFTAG_TILELENGTH): Corrected description.

2005-01-30  Andrey Kiselev  <<EMAIL>>

	* configure.ac: Fixes for --with-docdir option as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=759

	* libtiff/tif_open.c: Remove unnesessary TIFFSeekFile() call as per
	bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=756

	* libtiff/tif_stream.cxx: Fixes for C++ stream interface from
	Michael Rinne and Edward Lam.

2005-01-15  Andrey Kiselev  <<EMAIL>>

	* configure.ac: Make the documentation directory location configurable
	via the --with-docdir option (as suggested by Jeremy C. Reed).

	* libtiff/tif_color.c: Use double as the second argument of pow()
	function in TIFFCIELabToRGBInit(). As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=741

	* libtiff/tif_pixarlog.c: Avoid warnings when converting float to
	integer as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=740

	* libtiff/tif_getimage.c: Always fill the error message buffer in
	TIFFRGBAImageBegin() as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=739

2005-01-12  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_jpeg.c: Added ability to read/write the fax specific
	TIFFTAG_FAXRECVPARAMS, TIFFTAG_FAXSUBADDRESS and TIFFTAG_FAXRECVTIME
	tags as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=736

	* libtiff/tif_win32.c: Fixed message formatting in functions
	Win32WarningHandler() and Win32ErrorHandler() as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=735

	* tools/tiff2ps.c: Interpret the -w and -h options independently. As
	per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=689

2005-01-11  Andrey Kiselev  <<EMAIL>>

	* libtiff/tiffio.h: Move the color conversion routines in the 'extern
	"C"' section as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=727

	* libtiff/tiff.h: Restore back the workaround for AIX Visual Age C
	compiler to avoid double definition of BSD types as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=39

	* libtiff/Makefile.am: Place the C++ stream API in the separate
	library called libtiffxx to avoid unneeded dependencies. Probably
	there will be more C++ API in the future. As per bugs

	http://bugzilla.remotesensing.org/show_bug.cgi?id=733

	and

	http://bugzilla.remotesensing.org/show_bug.cgi?id=730

2005-01-05  Andrey Kiselev  <<EMAIL>>

	* tools/tiffdump.c: Fixed problem when read broken TIFFs with the
	wrong tag counts (Dmitry V. Levin, Martin Pitt).

	* configure.ac: Replace --disable-c++ with the --disable-cxx option as
	per bug http://bugzilla.remotesensing.org/show_bug.cgi?id=730

2004-12-25  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_getimage.c: More fixes for multiple-alpha-channelled
	RGB-images as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=713


	* tools/tiffset.c: Convert character option to integer value as per
	bug http://bugzilla.remotesensing.org/show_bug.cgi?id=725

2004-12-20  Andrey Kiselev  <<EMAIL>>

	* libtiff 3.7.1 released.

	* html/tiffset.1.html: Add missed manual page as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=678

	* libtiff/tiff.h: Revert back libtiff data type definitions as per
	bug http://bugzilla.remotesensing.org/show_bug.cgi?id=687

2004-12-19  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: Do not forget about TIFF_VARIABLE2 when
	checking for tag count in TIFFReadDirectory() function. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=713

	* libtiff/{tif_dirread.c, tif_fax3.c}: More argument checking in
	CheckMallock() function.

	* libtiff/tif_getimage.c: Support for multiple-alpha-channelled
	RGB-images as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=718

2004-12-15  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_getimage.c: #define A1 bracketing for clean build on
	SunPro compiler.

2004-12-11  Bob Friesenhahn  <<EMAIL>>

	* autogen.sh: aclocal and autoheader should be executed after
	libtoolize.  Also add '-I .' to aclocal invocation to check
	current directory for macros.

2004-12-10  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirwrite.c: Always write TIFFTAG_SUBIFD using LONG type
	as per bugs

	http://bugzilla.remotesensing.org/show_bug.cgi?id=703

	and

	http://bugzilla.remotesensing.org/show_bug.cgi?id=704

2004-12-04  Andrey Kiselev  <<EMAIL>>

	* nmake.opt: Link with the user32.lib in windowed mode. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=697

	* libtiff/tif_win32.c: Use char* strings instead of TCHAR in windowed
	mode as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=697

	* libtiff/tif_config.in.vc: Removed unneded definitions for
	read/open/close/lseek functions to fix the

	http://bugzilla.remotesensing.org/show_bug.cgi?id=680

2004-12-03  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dir.c, tif_dirread.c}: Remove TIFFReassignTagToIgnore()
	call from the TIFFReadDirectory() function. TIFFReassignTagToIgnore
	must be removed in the future, as it was never used properly. As per
	bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=692

2004-11-30  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_jpeg.c: Added a work-around in order to allow
	compilation with the heavily modified version of libjpeg delivered
	with Cygwin.

2004-11-29  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dir.c: Properly handle tags, which have the uint32
	counts. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=693

	* tools/fax2ps.c: Be able to extract the first page (#0). As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=690

2004-11-28  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_unix.c: Make UNIX module compilable (and usable)
	on Windows.

	* nmake.opt: Add missed DLLNAME variable.

2004-11-26  Frank Warmerdam  <<EMAIL>>

	* libtiff/makefile.vc: make it easier to rename the libtiff DLL.

2004-11-24  Andrey Kiselev  <<EMAIL>>

	* man/libtiff.3tiff: Improvements in the "LIST OF ROUTINES" table as
	per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=545

	* man/tiffset.1: Added manual page for tiffset tool written by Jay
	Berkenbilt. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=678

2004-11-23  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_error.c: fixed TIFFerror call to be TIFFError.

2004-11-21  Frank Warmerdam  <<EMAIL>>

	* html/document.html: Updated Adobe web links as per email from Joris.

2004-11-21  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tiffio.hxx, tiffio.h}: C++ stream interface moved to new
	file tiffio.hxx. We don't have any C++ in tiffio.h, those who want to
	use C++ streams should #include <tiffio.hxx>.

2004-11-13  Andrey Kiselev  <<EMAIL>>

	* libtiff/tiff.h: Added Adobe DNG tags.

	* libtiff/tif_win32.c: Typo fixed.

	* libtiff/{tif_stream.cxx, tiffio.h}: C++ stream interface updated to
	be compliant with the latest standard. Appropriate additions in
	makefiles now completed.

2004-11-11  Andrey Kiselev  <<EMAIL>>

	* tools/tiffset.c, libtiff/tif_dirinfo.c: Properly handle the
	different tag types. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=600

2004-11-10  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_aux.c: Set the appropriate ReferenceBlackWhite array for
	YCbCr image which lacks that tag (noted by Hans Petter Selasky).

2004-11-09  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_color.c: Division by zero fixed (Hans Petter Selasky).

2004-11-07  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_stream.cxx, tiffio.h}: Added C++ stream interface
	contributed by Edward Lam (see
	http://bugzilla.remotesensing.org/show_bug.cgi?id=654 for details).
	Though no changes in any makefiles yet.

2004-11-05  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_open.c: Removed close() in TIFFClientOpen() if file
	is bad. This is the callers responsibility.
	http://bugzilla.remotesensing.org/show_bug.cgi?id=651

2004-11-05  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tiffio.h, tif_win32.c, libtiff.def}: Added TIFFOpenW()
	function to work with the double byte strings (used to represent
	filenames in some locales). As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=625

	* libtiff/tif_dirread.c: Fixed problem when fetching BitsPerSample and
	Compression tags of type LONG from broken TIFFS as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=662

	* libtiff/tif_dirinfo.c: Fixed definition for TIFFTAG_RICHTIFFIPTC,
	the writecount should have uint32 type. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=662

	* libtiff/tif_write.c: Fixed wrong if() statement in
	TIFFAppendToStrip() function as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=660

2004-11-04  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirinfo.c: Change definition for TIFFTAG_EXTRASAMPLES
	field. The caller should supply a count when setting this field. As
	per bug

	 http://bugzilla.remotesensing.org/show_bug.cgi?id=648

	* libtiff/{tif_jpeg.c, tif_ojpeg.c}: TIFFTAG_JPEGTABLES should have
	uint32 count. Use this type everywhere.

2004-11-03  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_next.c: avoid use of u_long and u_char types.  Bug 653.

2004-11-02  Frank Warmerdam  <<EMAIL>>

	* tools/tiff2rgba.c: removed extra newlines in usage message.

2004-10-30  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirwrite.c: Improvements in tag writing code.

	* tools/tiff2ps.c: Fixed wrong variable data type when read Position
	tags (Tristan Hill).

2004-10-30  Frank Warmerdam  <<EMAIL>>

	* libtiff/tiffiop.h: added fallback definition of assert() if we
	don't have assert.h.

2004-10-29  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_fax3.c: Fixed case with the wrong decode routines
	choosing when the incorrect Group4Options tag set. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=323

	* libtiff/tif_dirwrite.c: Fixed problem with passing count variable of
	wrong type when writing the TIFF_BYTE/TIFF_SBYTE tags in
	TIFFWriteNormalTag().

2004-10-28  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2ps.c: Fixed wrong variable data type when read Resolution
	tags (Peter Fales).

	* tools/{bmp2tiff.c, raw2tiff.c}: Get rid of stream I/O functions.

2004-10-28  Frank Warmerdam  <<EMAIL>>

	* tools/tiff2pdf.c: added casts to avoid warnings.

	* libtiff/libtiff.def: Added several more entry points required
	to link fax2tiff.c against the DLL on windows.

2004-10-27  Andrey Kiselev  <<EMAIL>>

	* configure, configure.ac: Added --enable-rpath option to embed linker
	paths into library binary.

2004-10-26  Andrey Kiselev  <<EMAIL>>

	* tools/tiffset.c: Check the malloc return value (Dmitry V. Levin).

	* libtiff/{tif_strip.c, tif_tile.c}: Zero division problem fixed
	(Vladimir Nadvornik, Dmitry V. Levin).

2004-10-16  Andrey Kiselev  <<EMAIL>>

	* libtiff 3.7.0 released.

2004-10-15  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tif_jpeg.c: There seems to be no need to include stdio.h
	in this file so its inclusion is removed.  Including stdio.h
	sometimes incurs an INT32 typedef conflict between MinGW's
	basetsd.h and libjpeg's jmorecfg.h.

2004-10-15  Andrey Kiselev  <<EMAIL>>

	* man/bmp2tiff.1: Added manual page for bmp2tiff utility.

2004-10-13  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffcmp.c (leof): Renamed from 'eof' in order to avoid
	conflict noticed under MinGW.
	* ltmain.sh: Fix for MinGW compilation.

2004-10-13  Frank Warmerdam  <<EMAIL>>

	* man/tiffsplit.1: Fixed to indicate using aaa-zzz, not aa-zz.
	http://bugzilla.remotesensing.org/show_bug.cgi?id=635

2004-10-12  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dirread.c, tif_jpeg.c, tif_luv.c, tif_ojpeg.c,
	tif_pixarlog.c, tif_write.c}: Handle the zero strip/tile sizes
	properly (Dmitry V. Levin, Marcus Meissner).

2004-10-11  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirinfo.c: Type of the TIFFTAG_SUBIFD field changed
	to TIFF_IFD.

2004-10-10  Andrey Kiselev  <<EMAIL>>

	* tools/bmp2tif.c: Check the space allocation results.

2004-10-09  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dir.c: Initialize td_tilewidth and td_tilelength fields
	of the TIFFDirectory structure with the 0 instead of -1 to avoid
	confusing integer overflows in TIFFTileRowSize() for striped images.

	* tools/tiff2pdf.c: Fixed TransferFunction tag handling reported
	by Ross A. Finlayson.

	* libtiff/tif_dir.c: Fixed custom tags handling as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=629

2004-10-08  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirinfo.c: Fix bug with tif_foundfield and reallocation
	of tif_fieldinfo.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=630

2004-10-04  Bob Friesenhahn  <<EMAIL>>

	* contrib/iptcutil/README: Added the missing README which goes
	along with iptcutil.

2004-10-03  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_compress.c: Improved error reporting in
	TIFFGetConfiguredCODECs() (Dmitry V. Levin).

2004-10-02  Andrey Kiselev  <<EMAIL>>

	* libtiff 3.7.0beta2 released.

	* libtiff/{tif_aux.c, tif_compress.c, tif_dirinfo.c, tif_dirwrite.c,
	tif_extension.c, tif_fax3.c, tif_luv.c, tif_packbits.c,
	tif_pixarlog.c, tif_write.c}: Added checks for failed memory
	allocations and	integer overflows (Dmitry V. Levin).

	* libtiff/tiff.h: Missed TIFF_BIGTIFF_VERSION constant added.

2004-10-01  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_open.c: added a more informative message if a BigTIFF
	file is opened.

2004-09-30  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirinfo.c: changed type of XMLPacket (tag 700) to
	TIFFTAG_BYTE instead of TIFFTAG_UNDEFINED to comply with the info
	in the Adobe XMP Specification.

2004-09-29  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_jpeg.c, tif_pixarlog.c}: Use _TIFFmemset() instead of
	memset().

	* libtiff/{tif_dirread.c, tif_strip.c, tif_tile.c}: Applied patches
	from Dmitry V. Levin to fix possible integer overflow problems.

2004-09-28  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_getimage.c: Check for allocated buffers before clearing
	(Dmitry V. Levin).

2004-09-26  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dir.h, tif_dir.c, tif_dirread.c, tif_write.c}:
	Optimize checking for the strip bounds.

	* libtiff/{tif_dirread.c, tif_strip.c}: TIFFScanlineSize() and
	TIFFRasterScanlineSize() functions report zero in the case of integer
	overflow now. Properly handle this case in TIFFReadDirectory()
	(patches from Dmitry V. Levin).

2004-09-25  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_dirinfo.c, tif_strip.c, tif_tile.c}: Use TIFFhowmany8()
	macro where appropriate.

	* tools/tiff2bw.c: Write ImageWidth/Height tags to output file, as
	noted by Gennady Khokhorin.

	* libtiff/tif_dirread.c: Always check the return values, returned
	by the _TIFFmalloc() (Dmitry V. Levin).

	* libtiff/tif_dir.c: Fixed possible integer overflow _TIFFset*Array()
	functions (Dmitry V. Levin).

	* libtiff/{tif_dirread.c, tif_dir.c, tif_write.c}:
	Potential memory leak fixed in TIFFReadDirectory(), _TIFFVSetField(),
	TIFFGrowStrips() (found by Dmitry V. Levin).

2004-09-24  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tiffio.h, tif_compress.c}: Added TIFFGetConfiguredCODECs()
	to get the list of configured codecs.

	* libtiff/{tiffiop.h, tif_dirread.c}: More overflow fixes from
	Dmitry V. Levin.

2004-09-23  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: Applied patch from Dmitry V. Levin to fix
	possible integer overflow in CheckMalloc() function.

2004-09-22  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tiffiop.h, tif_strip.c}: Use TIFFhowmany8() macro instead
	of plain TIFFhowmany() where appropriate.

2004-09-21  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_getimage.c: Initialize arrays after space allocation.

2004-09-19  Andrey Kiselev  <<EMAIL>>

	* libtiff 3.7.0beta released.

	* libtiff/{tif_luv.c, tif_next.c, tif_thunder.c}: Several buffer
	overruns fixed, as noted by Chris Evans.

2004-09-14  Bob Friesenhahn  <<EMAIL>>

	* commit: Added a script to make it more convenient to commit
	updates.  The CVS commit message is extracted from this ChangeLog
	file.

2004-09-14  Andrey Kiselev  <<EMAIL>>

	* configure.ac, configure, aclocal.m4, libtiff/{mkspans.c, tif_fax3.c,
	tif_getimage.c, tif_luv.c, tif_lzw.c, tif_ojpeg.c, tif_packbits.c,
	tif_predict.c, tif_read.c, tif_swab.c, tif_thunder.c, tif_write.c,
	tif_dir.c, tif_dirread.c, tif_dirwrite.c, tif_jpeg.c, tif_dirinfo.c,
	tif_vms.c, tif_print.c, tif_strip.c, tif_tile.c, tif_dir.h,
	tif_config.h.in, tiffiop.h}:
	Get rid of BSD data types (u_char, u_short, u_int, u_long).

2004-09-13  Bob Friesenhahn  <<EMAIL>>

	* libtiff/tiff.h: Fix column tagging. Reference current Adobe XMP
	specification. Reference libtiff bug tracking system to submit
	private tag additions.

2004-09-12  Bob Friesenhahn  <<EMAIL>>

	* tools/tiffgt.c: Include "tif_config.h".

	* configure.ac: Use AM_PROG_CC_C_O since it is now needed to build
	tiffgt.  This results in the 'compile' script being added to the
	project.

	* tools/Makefile.am (tiffgt_CFLAGS): Add extra build options
	required to find OpenGL headers necessary to build tiffgt.  Also
	ensure that the libtiff that we built is used rather than some other
	libtiff installed on the system.

2004-09-12  Andrey Kiselev  <<EMAIL>>

	* configure.ac, acinclude.m4, aclocal.m4: New macros to detect GLUT
	libraries.

2004-09-11  Bob Friesenhahn  <<EMAIL>>

	* configure.ac: Pass library configuration defines via
	tif_config.h rather than extending CPPFLAGS. Configure a
	libtiff/tiffconf.h in order to satisfy application requirements
	(not used by library build). Do not define _POSIX_C_SOURCE=2 since
	this causes failure to build on systems which properly respect
	this request.

	* libtiff/tiffconf.h.in: New file to act as the template for the
	configured tiffconf.h

	* libtiff/files.lst (HDRS): Install the configured tiffconf.h.

2004-09-10  Frank Warmerdam  <<EMAIL>>

	* html/internals.html: Split off a discussion of adding new tags
	into addingtags.html.

2004-09-10  Andrey Kiselev  <<EMAIL>>

	* test/{ascii_tag.c, long_tag.c}: Preliminary test suite added.

	* tools/tiff2pdf.c: Fixed reading TransferFunction tag as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=590

	* libtiff/tif_print.c: Fixes in InkNames and NumberOfInks reporting.

	* libtiff/tif_dirread.c: Don't reject to read tags of the
	SamplesPerPixel size when the tag count is greater than number of
	samples as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=576

	* libtiff/tiff.h: Use _TIFF_DATA_TYPEDEFS_ guardian to switch off
	defining int8/uint8/... etc. types. As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=607

2004-09-09  Frank Warmerdam  <<EMAIL>>

	* tools/tiff2ps.c, tools/tiffmedian.c: fiddle with include files
	to avoid compile warnings about getopt() and a few other things.

2004-09-02  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: Use memcpy() function instead of pointer
	assigning magic in TIFFFetchFloat().

2004-09-01  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tiffio.h, tif_open.c}: Applied patches from Joris Van Damme
	to avoid requirement for tiffiop.h inclusion in some applications. See
	here

	http://www.asmail.be/msg0054799560.html

	for details.

	* tools/fax2tiff.c: Use the new functions in the code.

2004-08-25  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2pdf.c: Initialize arrays properly.

	* tools/tiff2ps.c: Avoid zero division in setupPageState() function;
	properly initialize array in PSDataBW().

2004-08-24  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2pdf.c: More fixes for bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=590

	from Ross Finlayson.

2004-08-23  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2ps.c: Fixed problem with uninitialized values.

	* libtiff/tif_dir.c: Initialize tif_foundfield data member in the
	TIFFDefaultDirectory() (in addition to 2004-08-19 fix).

	* tools/tiff2pdf.c: Fixed a bunch of problems as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=590

2004-08-20  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2pdf.c: Applied patch from Ross Finlayson that checks
	that the input file has compression, photometric interpretation,
	etcetra, tags or if not than a more descriptive error is returned.

	* libtiff/tif_dirread.c: Fixed problem in TIFFReadDirectory() in the
	code, responsible for tag data type checking.

2004-08-19  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tiffiop.h, tif_dirinfo.c}: Fixed problem with the static
	variable as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=593

2004-08-16  Andrey Kiselev  <<EMAIL>>

	* tools/ras2tiff.c: Fixed issue with missed big-endian checks as per
	bug http://bugzilla.remotesensing.org/show_bug.cgi?id=586

2004-08-01  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_config.h.in, tif_config.h.vc}: config.h.in and
	config.h.vc files renamed in the tif_config.h.in and tif_config.h.vc.

2004-07-24  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_lzw.c: LZW compression code is merged back from the
	separate package. All libtiff tools are updated to not advertise an
	abcence of LZW support.

2004-07-12  Andrey Kiselev  <<EMAIL>>

	* libtiff/tiffio.h: Revert thandle_t back to void* type.

2004-07-11  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_read.c, tif_tile.c, tif_strip.c}: Fixes in error
	messages, as suggested by Bernd Herd.

2004-07-03  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dir.c: Call TIFFError() instead of producing warnings
	when setting custom tags by value. Reported by Eric Fieleke.

2004-06-14  Andrey Kiselev  <<EMAIL>>

	* tools/bmp2tiff.c: Add missed RawsPerStrip setting.

2004-06-08  Andrey Kiselev  <<EMAIL>>

	* tools/bmp2tiff.c: Added new utility to convert Windows BMP files
	into TIFFs.

2004-06-07  Andrey Kiselev  <<EMAIL>>

	* libtiff 3.7.0alpha released.

2004-06-06  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tiff.h, tif_dirwrite.c, tif_fax3.c, tif_packbits.c,}: Get rid
	of ugly 64-bit hacks, replace them with the clever (autoconf based )
	ones :-).

	* libtiff/tiffio.h: Define thandle_t as int, not void* (may cause
	problems in 64-bit environment).

2004-06-05  Andrey Kiselev  <<EMAIL>>

	* tools/tiffset.c: tiffset now can set any libtiff supported tags.
	Tags can be supplied by the mnemonic name or number.

	* libtiff/{tiffio.h, tif_dir.h, tif_dirinfo.c,}: Added two new
	functions TIFFFindFieldInfoByName() and TIFFFieldWithName().

2004-05-27  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_ojpeg.c: Fixed problem with duplicated SOI and SOF
	markers as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=581

2004-05-24  Andrey Kiselev  <<EMAIL>>

	* tools/tiffsplit.c: Don't forget to copy Photometric
	Interpretation tag.

2004-05-20  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_open.c, tiffio.h}: New function added:
	TIFFIsBigEndian(). Function returns nonzero if given was file written
	in big-endian order.

	* tools/tiffsplit.c: Fixed problem with unproperly written multibyte
	files. Now output files will be written using the same byte order
	flag as	in the input image. See

	http://bugzilla.remotesensing.org/show_bug.cgi?id=574

	for details.

2004-05-19  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_print.c: added (untested) support for printing
	SSHORT, SLONG and SRATIONAL fields.

	* tools/tiffcp.c: close output file on normal exit.

2004-05-17  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_fax3.c: Avoid reading CCITT compression options
	if compression type mismatches. See

	http://bugzilla.remotesensing.org/show_bug.cgi?id=565

2004-04-30  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_strip.c: Never return 0 from the
	TIFFNumberOfStrips().

2004-04-29  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: Workaround for broken TIFF writers which
	store single SampleFormat value for multisampled images. See

	http://bugzilla.remotesensing.org/show_bug.cgi?id=562

2004-04-25  Andrey Kiselev  <<EMAIL>>

	* configure.ac, libtiff/{tiff.h, config.h.in}: Added tests for int8,
	int16 and int32 types to avoid complains on some compilers. Details at

	http://bugzilla.remotesensing.org/show_bug.cgi?id=39

2004-04-20  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2pdf.c: Fixed problem with unaligned access as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=555

2004-04-14  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_write.c: Allow in-place updating of the compressed
	images (don't work properly with all codecs). For details see GDAL bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=534

2004-04-06  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_jpeg.c: Workaround for wrong sampling factors used
	in the Intergarph JPEG compressed TIFF images as per bug:

	http://bugzilla.remotesensing.org/show_bug.cgi?id=532

2004-04-04  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_open.c: close clientdata if TIFFClientOpen() fails
	via bad2.

2004-03-26  Andrey Kiselev  <<EMAIL>>

	* tools/tiffcp.c: Properly set Photometric Interpretation in case of
	JPEG compression of grayscale images.

	* tools/tiffcp.c: Don't emit warnings when Orientation tag does not
	present in the input image.

2004-03-19  Andrey Kiselev  <<EMAIL>>

	* {many}: The first attempt to switch to autotools.

2004-03-03  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_open.c: Use dummy mmap/munmap functions in
	TIFFClientOpen() when the appropriate client functions was not
	supplied by user.

2004-03-02  Frank Warmerdam  <<EMAIL>>

	* tools/ycbcr.c: fixed main() declaration as per:
	http://bugzilla.remotesensing.org/show_bug.cgi?id=513

2004-02-26  Andrey Kiselev  <<EMAIL>>

	* tools/tiffsplit.c: Copy JPEGTables tag contents for JPEG compressed
	images. Reported by Artem Mirolubov.

	* libtiff/tif_dirread.c: Fixed problem with handling TIFF_UNDEFINED
	tag type in TIFFFetchNormalTag() as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=508

2004-02-17  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_codec.c: Fixed typo in TIFFInitPackBits name as per:
	http://bugzilla.remotesensing.org/show_bug.cgi?id=494

2004-02-05  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_fax3.c: Fixed problem with CCITT encoding modes as per
	bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=483

	But we need more work on fax codec to support update mode.

2004-01-30  Frank Warmerdam  <<EMAIL>>

	* libtiff/libtiff.def: Added TIFFCurrentDirOffset, TIFFWriteCheck,
	TIFFRGBAImageOK, and TIFFNumberOfDirectories as suggested by
	Scott Reynolds.

2004-01-29  Andrey Kiselev  <<EMAIL>>

	* libtiff/tiff.h: Fixed tag definitions for TIFFTAG_YCLIPPATHUNITS
	and TIFFTAG_INDEXED as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=475

	* libtiff/{tif_win32.c, tif_unix.c}: Check whether the pointer is
	NULL before proceeding further as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=474

	Check results, returned by the TIFFFdOpen() before returning and close
	file if TIFFFdOpen() failed as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=468

	* libtiff/tif_open.c: More fixes for

	http://bugzilla.remotesensing.org/show_bug.cgi?id=468

2004-01-28  Andrey Kiselev  <<EMAIL>>

	* libtiff/{libtiff.def, tif_close.c, tiffio.h, tif_open.c}: Separate
	TIFFCleanup() from the TIFFClose() in order to fix the bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=468

	* tools/tiffcp.c: Fixed problem with wrong interpretation of the
	InkNames tag as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=466

	Memory leak fixed.

2004-01-21  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirwrite.c: Fixed handling of writable ASCII tags that
	are field_passcount=TRUE properly.  Arguably anonymous custom tags
	should be declared as passcount=FALSE, but I don't want to change
	that without a careful review.

2004-01-20  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_write.c: Fixed reporting size of the buffer in case of
	stripped image in TIFFWriteBufferSetup(). As per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=460

2004-01-11  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dir.c: Incomplete cleanup in TIFFFreeDirectory(),
	patch from Gerben Koopmans.

	* libtiff/tif_dirread.c: Check field_passcount value before setting
	the value of undefined type, patch from Gerben Koopmans.

2004-01-02  Andrey Kiselev  <<EMAIL>>

	* tools/tiffcp.c: Fixed problem with wrong Photometric setting for
	non-RGB images.

2003-12-31  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_win32.c: Fixed problem with _TIFFrealloc() when the NULL
	pointer passed. Patch supplied by Larry Grill.

	* libtiff/{tiff.h, tif_fax3.c}:Fixes for AMD 64 platform as
	suggested by Jeremy C. Reed.

2003-12-26  Andrey Kiselev  <<EMAIL>>

	* libtiff 3.6.1 released.

2003-12-24  Andrey Kiselev  <<EMAIL>>

	* config.guess, config.sub: Updated from the recent upstream.

2003-12-22  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_color, tif_getimage.c, tiffio.h}, man/TIFFcolor.3t:
	More cleanups in color conversion interface, added appropriate manual
	page.

2003-12-19  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_extension.c, tif_dirinfo.c, tiff.h}: Warnings fixed as
	per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=357

	* tools/tiff2ps.c: Added support for alpha channel. Fixes

	http://bugzilla.remotesensing.org/show_bug.cgi?id=428

	* libtiff/{libtiff.def, tif_color.c, tif_getimage.c, tiffio.h}:
	Interface for Lab->RGB color conversion is finally cleaned up.
	Added support for ReferenceBlackWhite tag handling when converted from
	YCbCr color space. The latter closes

	http://bugzilla.remotesensing.org/show_bug.cgi?id=120

2003-12-07  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_getimage.c, tiffio.h}: Avoid warnings.

	* libtiff/makefile.vc, tools/makefile.vc: Support for IJG JPEG
	library.

2003-12-06  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_getimage.c, tif_aux.c}: Read WhitePoint tag from the
	file and properly use it for CIE Lab->RGB transform.

2003-12-04  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_getimage.c, tif_color.c, tiffio.h}: YCbCr->RGB
	conversion routines now in the tif_color.c module. New function
	TIFFYCbCrtoRGB() available in TIFF API.

	* libtiff/tif_dirwrite.c: Handle TIFF_IFD tag type correctly.

2003-12-03  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_getimage.c, tif_color.c, tiffio.h}: Improvements in
	CIE Lab conversion code. Start moving YCbCr stuff to the tif_color.c
	module.

	* libtiff/{tif_getimage.c, tiffio.h}, man{TIFFReadRGBAImage.3t,
	TIFFReadRGBAStrip.3t, TIFFReadRGBATile.3t, TIFFRGBAImage.3t}:
	Finally resolved problems with orientation handling. TIFFRGBAImage
	interface now properly supports all possible orientations, i.e. images
	will be flipped both in horizontal and vertical directions if
	required. 'Known bugs' section now removed from the appropriate manual
	pages. Closed bug entry:

	http://bugzilla.remotesensing.org/show_bug.cgi?id=322

2003-12-02  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dir.c: Fixed order of the parameters in TIFFError()
	function calls as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=440

2003-11-28 Ross Finlayson  <<EMAIL>>

	* tools/tiff2pdf.c:  Some bugs fixed.

2003-11-27  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_luv.c: Fixed bug in 48-bit to 24-bit conversion routine,
	reported by Antonio Scuri.

	* man/tiff2pdf.1: Few improvements in page layout.

	* Makefile.in, /man/Makefile.in, /html/man/tiff2pdf.1.html:
	 Added support fpr tiff2pdf manual page.

2003-11-26 Ross Finlayson  <<EMAIL>>

	* /man/tiff2pdf.1:  File added to repository.

2003-11-26  Andrey Kiselev  <<EMAIL>>

	* Makefile.in, /tools/{Makefile.in, makefile.vc}:
	 Added support fpr tiff2pdf utility.

2003-11-25  Ross Finlayson  <<EMAIL>>

	* /tools/tiff2pdf.c:  File added to repository.

2003-11-22  Andrey Kiselev  <<EMAIL>>

	* /tools/raw2tiff.c: sqrtf() replaced with sqrt().

2003-11-21  Andrey Kiselev  <<EMAIL>>

	* /tools/raw2tiff.c: #include <getopt.h> removed.

	* tools/{Makefile.in, tiffgt.c}: Unmaintained and platform dependent
	sgigt utility removed and replaced with the completely rewritten
	portable tiffgt tool (depend on OpenGL and GLUT). Initial revision,
	there is a lot of things to improve.

	* libtiff/tif_ojpeg.c: TIFFVGetField() function now can properly
	extract the fields from the OJPEG files. Patch supplied by Ross
	Finlayson.

	* libtiff/{tiffio.h, tif_codec.c}, man/{libtiff.3t, TIFFcodec.3t}:
	Added new function TIFFIsCODECConfigured(), suggested by Ross
	Finlayson.

2003-11-18  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirinfo.c: Implemented binary search in
	_TIFFMergeFieldInfo(). Patch supplied by Ross Finlayson.

	* libtiff/tif_dir.h: _TIFFFindOrRegisterdInfo declaration replaced
	with _TIFFFindOrRegisterFieldInfo as reported by Ross Finlayson.

2003-11-17  Frank Warmerdam  <<EMAIL>>

	* tif_dirread.c: do not mark all anonymously defined tags to be
	IGNOREd.

2003-11-17  Andrey Kiselev  <<EMAIL>>

	* contrib/pds/{tif_pdsdirread.c, tif_pdsdirwrite.c}: Use
	TIFFDataWidth() function insted of tiffDataWidth array.

2003-11-16  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tiff.h, tif_dirinfo.c}: Added support for IFD (13)
	datatype, introduced in "Adobe PageMaker TIFF Tech. Notes".

2003-11-15  Frank Warmerdam  <<EMAIL>>

	* Makefile.in: fixed missing backslash for tif_color.c in list.

2003-11-13  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_color.c, tif_getimage.c, tiffio.h, Makefile.in}:
	New color space conversion code: CIE L*a*b* 1976 images now supported
	by the TIFFRGBAImage interface. All introduced routines go to new
	module tif_color.c. Eventually all color conversion functions should
	be moved there.

2003-11-12  Andrey Kiselev  <<EMAIL>>

	* tools/{ras2tiff.c, rasterfile.h}: Properly determine SUN Rasterfiles
	with the reverse byte order (it is reported by the magic header
	field). Problem reported by Andreas Wiesmann.

	* tools/raw2tiff.c, man/raw2tiff.1: Few improvements in correlation
	calculation function. Guessing mechanics now documented in manual page.

2003-11-11  Andrey Kiselev  <<EMAIL>>

	* tools/raw2tiff.c: Implemented image size guessing using
	correlation coefficient calculation between two neighbour lines.

2003-11-09  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_tile.c: remove spurious use of "s" (sample) in the
	planarconfig_contig case in TIFFComputeTile().

	http://bugzilla.remotesensing.org/show_bug.cgi?id=387

2003-11-09  Andrey Kiselev  <<EMAIL>>

	* libtiff/tiffiop.h: New macros: TIFFmax, TIFFmin and TIFFrint.

2003-11-07  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tiffio.h, tif_strip.c}, man/{TIFFstrip.3t, libtiff.3t}:
	Added TIFFRawStripSize() function as suggested by Chris Hanson.

2003-11-03  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_lzw.c, tif_fax3.c}: Proper support for update mode as
	per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=424

2003-10-29  Andrey Kiselev  <<EMAIL>>

	* libtiff/libtiff.def: Added TIFFReadRGBAImageOriented.

	* html/build.html: Added note about GNU make requirement.

2003-10-25  Andrey Kiselev  <<EMAIL>>

	* Makefile.in: Fixes in using MAKEFLAGS as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=418

	* port/install.sh.in: Option -p added to the mkdir command to create
	all directory tree structure before installing.

2003-10-18  Andrey Kiselev  <<EMAIL>>

	* /tools/tiff2ps.c: #include <strings.h> replaced with the
	#include <string.h>.

2003-10-16  Andrey Kiselev  <<EMAIL>>

	* Makefile.in: Add an absolute path to the test_pics.sh call.

2003-10-12  Andrey Kiselev  <<EMAIL>>

	* libtiff/tiffcomp.h: #define _BSDTYPES_DEFINED when defining BSD
	typedefs.

2003-10-09  Andrey Kiselev  <<EMAIL>>

	* configure, libtiff/{Makefile.in, mkversion.c}:
	Relative buildings fixed.

	* tools/Makefile.in: Added "-I../libtiff" to the tiffset building
	rule.

2003-10-07  Andrey Kiselev  <<EMAIL>>

	* Makefile.in: Added missed v3.6.0.html.

	* libtiff/tiffio.h: Typo fixed: ORIENTATION_BOTTOMLEFT replaced with
	ORIENTATION_BOTLEFT.

2003-10-04  Andrey Kiselev  <<EMAIL>>

	* 3.6.0 final release.

2003-10-03  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tif_getimage.c, tiffio.h}, man/TIFFReadRGBAImage.3t: New
	function TIFFReadRGBAImageOriented() implemented to retrieve raster
	array with user-specified origin position as suggested by Jason Frank.
	See

	http://bugzilla.remotesensing.org/show_bug.cgi?id=322

	for details.

	* tools/tiff2rgba.c: Switched to use TIFFReadRGBAImageOriented()
	instead of TIFFReadRGBAImage().

	* tools/tiff2ps.c: Fixed possible endless loop as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=404

2003-09-30  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: Check field counter against number of fields
	in order to fix

	http://bugzilla.remotesensing.org/show_bug.cgi?id=366

	* libtiff/tif_fax3.c: Fix wrong line numbering as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=342

2003-09-25  Andrey Kiselev  <<EMAIL>>

	* libtiff/{tiffiop.h, tif_dirread.c, tif_dir.c, tif_open.c,
	tif_close.c}: Store a list of opened IFD to prevent looping as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=383

2003-09-23  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: More fixes for	EstimateStripByteCounts(). See

	http://bugzilla.remotesensing.org/show_bug.cgi?id=358

2003-08-21  Andrey Kiselev  <<EMAIL>>

	* tools/tiffmedian.c: int declaration replaced with the uint32 to
	support large images as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=382

2003-08-12  Andrey Kiselev  <<EMAIL>>

 	* libtiff/Makefile.in: Fixed problem with building in different
	directory.

	* tools/tiff2ps.c: Added missing #include <strings.h>.

	* libtiff/tif_dirwrite.c: More fixes for custom tags code
	from Ashley Dreier.

2003-08-07  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2ps.c: Added page size setting when creating PS Level 2.
	Patch submitted by Balatoni Denes (with corrections from Tom
	Kacvinsky).

	* tools/tiff2ps.c: Fixed PS comment emitted when FlateDecode is
	being used. Reported by Tom Kacvinsky.

	* libtiff/tif_dirwrite.c: Fixed problem with custom tags writing,
	reported by Ashley Dreier.

	* libtiff/tif_print.c: Fixed problem with float tags reading, support
	for printing RATIONAL and BYTE tags added.

2003-08-05  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_lzw.c: Move LZW codec state block allocation back to
	TIFFInitLZW(), because its initialization in LZWSetupDecode() cause
	problems with predictor initialization. Remove O_RDONLY check during
	state block allocation to be able open LZW compressed files in update
	mode.

	Problem exist for libtiff version of the tif_lzw.c module. One from
	lzw-compression-kit hasn't such troubles.

2003-08-04  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_write.c: modified tif_write.c so that the various
	encoded write functions use tif_postdecode() to apply byte order
	swapping (swab) to the application passed data buffer if the same
	would be done when reading.  This allows us to write pixel data with
	more than 8 bits per sample to existing files of a non-native byte
	order.  One side effect of this change is the applications buffer
	itself is altered in this case by the act of writing.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=171

2003-07-25  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_open.c: avoid signed/unsigned casting warning
	initializing typemask as per patch from J.A. Strother.

	* tools/tiffcp.c: fixed signed/unsigned casting warning.

	* libtiff/tif_print.c: dos2unix conversion.

	* tools/tiffsplit.c: increased the maximum number of pages that
	can be split.  Patch provided by Andrew J. Montalenti.

2003-07-11  Andrey Kiselev  <<EMAIL>>

	* tools/raw2tiff.c: Added option `-p' to explicitly select color
	space of input image data. Closes

	http://bugzilla.remotesensing.org/show_bug.cgi?id=364

2003-07-08  Frank Warmerdam  <<EMAIL>>

	* tif_aux.c, tif_codec.c, tif_dir.c, tif_dirread.c, tif_extension.c,
	tif_fax3.c, tif_getimage.c, tif_luv.c, tif_lzw.c, tif_next.c,
	tif_packbits.c, tif_predict.c, tif_print.c, tif_swab.c, tif_thunder.c:
	avoid casting warning at /W4.

2003-07-03  Andrey Kiselev  <<EMAIL>>

	* tools/thumbnail.c: Memory leak fixed as reported by Robert S. Kissel.

2003-06-30  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_pixarlog.c: Unused variables removed.

	* libtiff/{tif_dirread.c, tif_dir.c}: Fixed problem with
	EstimateStripByteCounts() as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=358

	* libtiff/{tif_dirwrite.c, tif_packbits.c}: Fixed compilation on
	64-bit architectures as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=357

	* libtiff/tif_dirinfo.c: TIFFDataWidth() returns 0 in case of
	unknown data type.

2003-06-19  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_print.c: fixed some serious bugs when printing
	custom tags ... almost certain to crash.

	* libtiff/tif_dirread.c: Don't ignore custom fields that are
	autodefined.  Not sure how this got to be like this.

2003-06-18  Andrey Kiselev  <<EMAIL>>

	* 3.6.0 Beta2 released.

	* tools/tiffcmp.c, man/tiffcmp.1: Fixed problem with unused data
	comparing as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=349

	`-z' option now can be used to set the number of reported different
	bytes.

2003-06-09  Andrey Kiselev  <<EMAIL>>

	* tools/tiffcp.c, man/tiffcp.1: Added possibility to specify value -1
	to -r option to get the entire image as one strip. See

	http://bugzilla.remotesensing.org/show_bug.cgi?id=343

	for details.

2003-06-04  Andrey Kiselev  <<EMAIL>>

	* tools/tiffcp.c: Set the correct RowsPerStrip and PageNumber
	values as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=343

2003-05-27  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: modified segment_height calculation to always
	be a full height tile for tiled images.  Also changed error to just
	be a warning.

2003-05-25  Andrey Kiselev  <<EMAIL>>

	* tools/fax2tiff.c: Page numbering fixed, as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=341

2003-05-20  Andrey Kiselev  <<EMAIL>>

	* contrib/ojpeg/{Makefile.in, jdhuff.h, jinclude.h, ojpeg.c, README},
	configure, Makefile.in:	Switched back to the old behaviour. Likely
	better solution should be found for OJPEG support.

2003-05-11  Andrey Kiselev  <<EMAIL>>

	* libtiff/mkversion.c: Fixed problem with wrong string size when
	reading RELEASE-DATE file.

2003-05-07  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2ps.c: Fixed bug in Ascii85EncodeBlock() function: array
	index was out of range.

2003-05-06  Andrey Kiselev  <<EMAIL>>

	* contrib/ojpeg/{Makefile.in, jdhuff.h, jinclude.h, ojpeg.c, README},
	configure, Makefile.in:	Improved libtiff compilation with OJPEG
	support. Now no need for patching IJG JPEG library, hack required by
	libtiff will be compiled and used in-place. Implemented with
	suggestion and help from Bill Allombert, Debian's libjpeg maintainer.

	* libtiff/tif_aux.c: Properly handle TIFFTAG_PREDICTOR in
	TIFFVGetFieldDefaulted() function.

2003-05-05  Andrey Kiselev  <<EMAIL>>

	* tools/ppm2tiff.c: PPM header parser improved: now able to skip
	comments.

	* tools/tiffdither.c: Fixed problem with bit fill order tag setting:
	was not copied from source image.

	* libtiff/getimage.c: Workaround for some images without correct
	info about alpha channel as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=331

2003-04-29  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2ps.c, man/tiff2ps.1: Add ability to generate PS Level 3.
	It basically allows one to use the /flateDecode filter for ZIP
	compressed TIFF images. Patch supplied by Tom Kacvinsky. Fixes

	http://bugzilla.remotesensing.org/show_bug.cgi?id=328

	* tools/tiff2ps.c: Force deadzone printing when EPS output specified
	as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=325

2003-04-17  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: Removed additional check for StripByteCounts
	due to problems with multidirectory images. Quality of error messages
	improved.

2003-04-16  Andrey Kiselev  <<EMAIL>>

	* tools/tiffcp.c: Fixed problem with colorspace conversion for JPEG
	encoded images. See bug entries

	http://bugzilla.remotesensing.org/show_bug.cgi?id=275

	and

	http://bugzilla.remotesensing.org/show_bug.cgi?id=23

	* libtiff/tif_dirread.c: Additional check for StripByteCounts
	correctness. Fixes

	http://bugzilla.remotesensing.org/show_bug.cgi?id=320

2003-03-12  Andrey Kiselev  <<EMAIL>>

	* tools/{fax2ps.c, fax2tiff.c, gif2tiff.c, pal2rgb.c, ppm2tiff.c,
	ras2tiff.c, raw2tiff.c, rgb2ycbcr.c, thumbnail.c, tiff2bw.c,
	tiff2ps.c, tiff2rgba.c, tiffcp.c, tiffdither.c, tiffinfo.c,
	tiffmedian.c}: Added library version reporting facility to all tools.

2003-03-06  Frank Warmerdam  <<EMAIL>>

	* port/install.sh.in: Fixed problems with install producing paths
	like ///usr/local/lib on cygwin.

2003-02-27  Andrey Kiselev  <<EMAIL>>

	* tools/fax2tiff.c, man/fax2tiff.1: New switch (-X) to set width of
	raw input page. Patch supplied by Julien Gaulmin. See

	http://bugzilla.remotesensing.org/show_bug.cgi?id=293

	for details.

2003-02-26  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dir.c: fixed up the tif_postdecode settings
	responsible for byte swapping complex image data.

	* libtiff/tif_lzw.c: fixed so that decoder state isn't allocated till
	LZWSetupDecode().  Needed to read LZW files in "r+" mode.

2003-02-07  Andrey Kiselev  <<EMAIL>>

	* tools/ppm2tiff.c: Fixed problem with too many arguments.

2003-02-04  Andrey Kiselev  <<EMAIL>>

	* tools/raw2tiff.c: Memory leak fixed.

2003-02-03  Andrey Kiselev  <<EMAIL>>

	* tools/fax2tiff.c, man/fax2tiff.1: Applied patch from Julien Gaulmin
	(thanks, Julien!). More switches for fax2tiff tool for better control
	of input and output. Details at

	http://bugzilla.remotesensing.org/show_bug.cgi?id=272

2003-02-03  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: Modified to defer initialization of jpeg
	library so that we can check if there is already any tile/strip data
	before deciding between creating a compressor or a decompressor.

2003-01-31  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_write.c: TIFFWriteCheck() now fails if the image is
	a pre-existing compressed image.  That is, image writing to
	pre-existing compressed images is not allowed.

	* libtiff/tif_open.c: Removed error if opening a compressed file
	in update mode.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=198

2003-01-31  Andrey Kiselev  <<EMAIL>>

	* config.guess, config.sub: Updated to recent upstream versions.

2003-01-15  Frank Warmerdam  <<EMAIL>>

	* cut 3.6.0 Beta release.

2002-12-20  Andrey Kiselev  <<EMAIL>>

	* tools/fax2ps.c, man/fax2ps.1: Page size was determined
	in wrong way as per bug

	http://bugzilla.remotesensing.org/show_bug.cgi?id=239

2002-12-17  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirread.c: Allow wrong sized arrays in
	TIFFFetchStripThing().

	http://bugzilla.remotesensing.org/show_bug.cgi?id=49

2002-12-02  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dir.c: fix problem with test on td_customValueCount.
	Was using realloc even first time.  Fix by Igor Venevtsev.

2002-11-30  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dir.c: fixed bug with resetting an existing custom
	field value.

	* libtiff/tif_dir.c: Fixed potential problem with ascii "custom"
	tags in TIFFVGetField() ... added missing break.

2002-10-14  Frank Warmerdam  <<EMAIL>>

	* tools/tiff2ps.c: fixes a problem where "tiff2ps -1e" did not make
	the scanline buffer long enough when writing rgb triplets.
	The scanline needs to be 3 X the number of dots or else it will
	contain	an incomplete triplet and programs that try to separate
	the eps by redefining the colorimage operator will get messed up.
	Patch supplied by William Bader.

	* Makefile.in: added tif_extension.c to file list as per
	http://bugzilla.remotesensing.org/show_bug.cgi?id=218.

2002-10-11  Andrey Kiselev  <<EMAIL>>

	* configure, config.site, libtiff/{tif_unix.c, Makefile.in}: Fix for
	large files (>2GiB) supporting. New option in the config.site:
	LARGEFILE="yes". Should be enough for I/O of the large files.

2002-10-10  Frank Warmerdam  <<EMAIL>>

	* libtiff/html/v3.6.0.html: new release notes.

	* libtiff/index.html: removed faq, cvs snapshot cruft.  Added email
	link for Andrey.  Pointer to v3.6.0.html.

	* libtiff/Makefile.in: added direct rule for tiffvers.h for release.

2002-10-07  Andrey Kiselev  <<EMAIL>>
	* tools/tiff2ps.c, man/tiff2ps.1: Applied patch form Sebastian Eken
	(thanks, Sebastian!). New switches:
	-b # for a bottom margin of # inches
	-c   center image
	-l # for a left margin of # inches
	-r   rotate the image by 180 degrees
	New features merged with code for shrinking/overlapping.
	Previously added -c and -n switches (for overriding PS units) renamed
	in -x and -y respectively.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=200

	* html/man/*.html: Updated from actual manual pages.

2002-10-06  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: fixed problem with boolean defined with wrong
	size on windows.  Use #define boolean hack.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=188

	* libtiff/tiff.h: Don't do special type handling in tiff.h unless
	USING_VISUALAGE is defined.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=39

2002-10-03  Frank Warmerdam  <<EMAIL>>

	* libtiff/tiff.h: added COMPRESSION_JP2000.

2002-10-02  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_dirread.c: Another fix for the fetching SBYTE arrays
	by the TIFFFetchByteArray() function. Should finally resolve

	http://bugzilla.remotesensing.org/show_bug.cgi?id=52

	* configure: Set -DPIXARLOG_SUPPORT option along with -DZIP_SUPPORT

	* html/Makefile.in: New targets added: html and groffhtml for
	producing HTML representations of the manual pages automatically.
	html target uses man2html tool, groffhtml uses groff tool.

2002-09-29  Frank Warmerdam  <<EMAIL>>

	* configure, libtiff/Makefile.in: Added SCO OpenServer 5.0.6 support
	from John H. DuBois III.

2002-09-15  Andrey Kiselev  <<EMAIL>>

	* Makefile.in, /man/{raw2tiff.1, Makefile.in, libtiff.3}: Added
	manual page for raw2tiff(1) tool.

2002-09-12  Andrey Kiselev  <<EMAIL>>

	* /libtiff/{tiffio.h, tif_dir.h}: TIFFDataWidth() declaration moved to
	the tiffio.h header file.

	* Makefile.in, /man/{TIFFDataWidth.3t, Makefile.in, libtiff.3}: Added
	manual page for TIFFDataWidth() function

2002-09-08  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirread.c: Expand v[2] to v[4] in TIFFFetchShortPair()
	as per http://bugzilla.remotesensing.org/show_bug.cgi?id=196.

	* tools/tiff2ps.c: Don't emit BeginData/EndData DSC comments
	since we are unable to properly include the amount to skip.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=80

2002-09-02  Andrey Kiselev  <<EMAIL>>

	* /libtiff/tif_dirread.c: Fixed problem with SBYTE type data fetching
	in TIFFFetchByteArray(). Problem described at
	http://bugzilla.remotesensing.org/show_bug.cgi?id=52

2002-08-22  Andrey Kiselev  <<EMAIL>>

	* /libtiff/tif_dirinfo.c: Further additions to free custom fields
	in _TIFFSetupFieldInfo() function.
	See http://bugzilla.remotesensing.org/show_bug.cgi?id=169 for details.

	* /libtiff/tif_lzw.c: Additional consistency checking added in
	LZWDecode() and LZWDecodeCompat().
	Fixes http://bugzilla.remotesensing.org/show_bug.cgi?id=190
	and http://bugzilla.remotesensing.org/show_bug.cgi?id=100

	* /libtiff/tif_lzw.c:
	Added check for valid code lengths in LZWDecode() and
	LZWDecodeCompat(). Fixes
	http://bugzilla.remotesensing.org/show_bug.cgi?id=115

2002-08-16  Andrey Kiselev  <<EMAIL>>

	* /libtiff/{Makefile.vc, libtiff.def}:
	Missed declarations added.

2002-08-15  Frank Warmerdam  <<EMAIL>>

	* tif_getimage.c: Ensure that TIFFRGBAImageBegin() returns the
	return code from the underlying pick function.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=177

	* tif_dir.h: changed FIELD_CODEC to 66 from 64 to avoid overlap
	with FIELD_CUSTOM as mentioned in bug 169.

	* tif_close.c: added logic to free dynamically created anonymous
	field definitions to correct a small memory leak.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=169

2002-08-10  Andrey Kiselev  <<EMAIL>>

	* /tools/{raw2tiff.c, Makefile.in, Makefile.lcc, Makefile.vc}:
	New tool: raw2tiff --- raw images to TIFF converter. No manual page yet.

2002-07-31  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: Fixed problem with setting of nrows in
	JPEGDecode() as per bugzilla bug (issue 1):

	http://bugzilla.remotesensing.org/show_bug.cgi?id=129

	* libtiff/{tif_jpeg.c,tif_strip.c,tif_print.c}: Hacked tif_jpeg.c to
	fetch TIFFTAG_YCBCRSUBSAMPLING from the jpeg data stream if it isn't
	present in the tiff tags.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=168

	* libtiff/tif_read.c, libtiff/tif_write.c: TIFFReadScanline() and
	TIFFWriteScanline() now set tif_row explicitly in case the codec has
	fooled with the value.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=129

2002-06-22  Andrey Kiselev  <<EMAIL>>

	* /tools/tiff2ps.c: Added workaround for some software that may crash
	when last strip of image contains fewer number of scanlines than
	specified by the `/Height' variable. See
	http://bugzilla.remotesensing.org/show_bug.cgi?id=164
	for explanation.

2002-06-21  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2ps, man/tiff2ps.1: New functionality for tiff2ps utility:
	splitting long images in several pages. See
	http://bugzilla.remotesensing.org/show_bug.cgi?id=142 for explanation.
	Patch granted by John Williams <<EMAIL>>.

2002-06-11  Frank Warmerdam  <<EMAIL>>

	* libtiff/contrib/win95: renamed to contrib/win_dib.  Added new
	Tiffile.cpp example of converting TIFF files into a DIB on Win32.
	This one is described in:

	http://bugzilla.remotesensing.org/show_bug.cgi?id=143

	* libtiff/tif_ojpeg.c: Major upgrade from Scott.  See details at:

	http://bugzilla.remotesensing.org/show_bug.cgi?id=156

2002-05-10  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2ps: New commandline switches to override resolution
	units obtained from the input file. Closes
	http://bugzilla.remotesensing.org/show_bug.cgi?id=131

2002-04-26  Andrey Kiselev  <<EMAIL>>

	* libtiff/libtiff.def: Added missed declaration.

2002-04-22  Andrey Kiselev  <<EMAIL>>

	* tools/fax2tiff.c: Updated to reflect latest changes in libtiff.
	Closes http://bugzilla.remotesensing.org/show_bug.cgi?id=125

2002-04-20  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_open.c: Pointers to custom procedures
	in TIFFClientOpen() are checked to be not NULL-pointers.

2002-04-18  Andrey Kiselev  <<EMAIL>>

	* libtiff/libtiff.def: Added missed declarations.

	* libtiff/tif_pixarlog.c: Updated for using tif_tagmethods structure.

2002-04-16  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_lzw.c: Additional checks for data integrity introduced.
	Should finally close
	http://bugzilla.remotesensing.org/show_bug.cgi?id=100

2002-04-10  Andrey Kiselev  <<EMAIL>>

	* tools/tiff2ps: Division by zero fixed.
	Closes http://bugzilla.remotesensing.org/show_bug.cgi?id=88

2002-04-09  Andrey Kiselev  <<EMAIL>>

	* libtiff/: tif_dirwrite.c, tif_write.c, tiffio.h:
	TIFFCheckpointDirectory() routine added.
	Closes http://bugzilla.remotesensing.org/show_bug.cgi?id=124

	* man/: TIFFWriteDirectory.3t,  Makefile.in: Added description
	for the new function.

2002-04-08  Andrey Kiselev  <<EMAIL>>

	* libtiff/: tif_codec.c, tif_compress.c, tiffiop.h: Introduced
	additional members tif->tif_decodestatus and tif->tif_encodestatus
	for correct handling of unconfigured codecs (we should not try to read
	data or to define data size without correct codecs).

	* libtiff/tif_getimage.c: The way of codecs checking in TIFFRGBAImageOK
	changed. Now it has used tif->tif_decodestatus and
	tif->tif_encodestatus.
	Should fix http://bugzilla.remotesensing.org/show_bug.cgi?id=119 (in
	case of __cvs_8.tif test image).

	* libtiff/: tif_dirinfo.c, tif_dirread.c: Somebody makes a bug in
	tif_dirread.c when TIFFCreateAnonFieldInfo was introduced.
	Closes http://bugzilla.remotesensing.org/show_bug.cgi?id=119 in case
	of _cvs_00000-00.tif, _cvs_00000-01.tif and _cvs_00000-02.tif.

2002-04-04  Andrey Kiselev  <<EMAIL>>

	* libtiff/: tif_lzw.c: Assertions in LZWDecode and LZWDecodeCompat
	replaced by warnings. Now libtiff should read corrupted LZW-compressed
	files by skipping bad strips.
	Closes http://bugzilla.remotesensing.org/show_bug.cgi?id=100

2002-04-03  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirwrite.c: Removed some dead code.

	* libtiff/*: Cleanup some warnings.

	* libtiff/tif_dir.c: Fixed bug with count returned by TIFFGetField()
	for variable length FIELD_CUSTOM values.  Was int * but should be
	u_short *.

2002-04-01  Andrey Kiselev  <<EMAIL>>

	* tools/: tifcp.c: Added support for 'Orientation' tag in tiffcp
	utility (at cpStripToTile routine).

2002-03-27  Frank Warmerdam  <<EMAIL>>

	* tif_dirread.c: avoid div-by-zero if rowbytes is zero in chop func.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=111

	* tif_print.c: Fixed so that ASCII FIELD_CUSTOM values with
	passcount set FALSE can be printed (such as TIFFTAG_SOFTWARE).

	* libtiff/tif_dir.c,tif_dirinfo.c,tif_dir.h,tif_ojpeg.c: modified so
	that TIFFTAG_SOFTWARE uses FIELD_CUSTOM as an example.

2002-03-26  Dwight Kelly  <<EMAIL>>

	* libtiff/: tiff.h, tif_dir.c, tif_dir.h, tif_dirinfo.c, tif_dirread.c,
	tif_dirwrite.c: Added get/put code for new tag XMLPACKET as defined
	in Adobe XMP Technote. Added missing INKSET tag value from TIFF 6.0 spec
	INKSET_MULTIINK (=2). Added missing tags from Adobe TIFF technotes:
	CLIPPATH, XCLIPPATHUNITS, YCLIPPATHUNITS, OPIIMAGEID, OPIPROXY and
	INDEXED. Added PHOTOMETRIC tag value from TIFF technote 4 ICCLAB (=9).

2002-03-26  Andrey Kiselev  <<EMAIL>>

	* libtiff/: tif_getimage.c: TIFFReadRGBAStrip and TIFFReadRGBATile
	now also uses TIFFRGBAImageOK before reading. This is additional fix
	for http://bugzilla.remotesensing.org/show_bug.cgi?id=110

2002-03-25  Andrey Kiselev  <<EMAIL>>

	* libtiff/: tif_getimage.c: Additional check for supported
	codecs added in TIFFRGBAImageOK and TIFFReadRGBAImage now uses
	TIFFRGBAImageOK before reading.
	Closes http://bugzilla.remotesensing.org/show_bug.cgi?id=110

2002-03-15  Andrey Kiselev  <<EMAIL>>

	* libtiff/: tif_dir.c, tif_dir.h, tif_dirinfo.c, tif_dirread.c,
	tif_dirwrite.c: Added routine TIFFDataWidth for detrmining
	TIFFDataType sizes instead of working with tiffDataWidth array
	directly. Should prevent out-of-borders bugs in case of unknown or
	broken data types.  EstimateStripByteCounts routine modified, so it
	won't work when tags with uknown sizes founded.
	Closes http://bugzilla.remotesensing.org/show_bug.cgi?id=109

2002-03-13  Andrey Kiselev  <<EMAIL>>

	* libtiff/tif_getimage.c: Added support for correct handling
	`Orientation' tag in gtTileContig. Should be added in other gt*
	functions as well, but I have not images for testing yet. Partially
	resolves http://bugzilla.remotesensing.org/show_bug.cgi?id=23

2002-03-10  Andrey Kiselev  <<EMAIL>>

	* libtiff/: tif_dirinfo.c, tif_dirwrite.c: Added possibility to
	read broken TIFFs with LONG type used for TIFFTAG_COMPRESSION,
	TIFFTAG_BITSPERSAMPLE, TIFFTAG_PHOTOMETRIC.  Closes
	http://bugzilla.remotesensing.org/show_bug.cgi?id=99

2002-03-08  Andrey Kiselev  <<EMAIL>>

	* libtiff/Makefile.in, tools/Makefile.in: Shared library will not
	be stripped when installing, utility binaries will do.	Closes
	http://bugzilla.remotesensing.org/show_bug.cgi?id=93

2002-02-28  Frank Warmerdam  <<EMAIL>>

	* man/TIFFGetField: fixed type of TIFFTAG_COPYRIGHT.

	* man/libtiff.3t: added copyright tag info.

2002-02-11  Frank Warmerdam  <<EMAIL>>

	* libtiff/{tiff.h,tif_fax3.c}: Add support for __arch64__.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=94

	* man/Makefile.in: Patch DESTDIR handling

	http://bugzilla.remotesensing.org/show_bug.cgi?id=95

	* configure: OpenBSD changes for Sparc64 and DSO version.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=96

2002-02-05  Frank Warmerdam  <<EMAIL>>

	* config.site/configure: added support for OJPEG=yes option to enable
	OJPEG support from config.site.

2002-01-27  Frank Warmerdam  <<EMAIL>>

	* html/document.html: fixed links for TIFf 6 docs.

2002-01-18  Frank Warmerdam  <<EMAIL>>

	* config.guess, config.sub: Updated from ftp.gnu.org/pub/config.

	* libtiff/tif_read.c: Fixed TIFFReadEncodedStrip() to fail if the
	decodestrip function returns anything not greater than zero as per
	http://bugzilla.remotesensing.org/show_bug.cgi?id=97

	* configure: Modify CheckForBigEndian so it can work in a cross
	compiled situation.

2002-01-16  Frank Warmerdam  <<EMAIL>>

	* tools/tiffdump.c: include TIFFTAG_JPEGTABLES in tag list.

	* tools/tiffset.c: fix bug in error reporting.

	* tools/tiffcp.c: fix several warnings that show up with -Wall.

2002-01-04  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: fixed computation of segment_width for
	tiles files to avoid error about it not matching the
	cinfo.d.image_width values ("JPEGPreDecode: Improper JPEG strip/tile
	size.") for ITIFF files.  Apparently the problem was incorporated since
	3.5.5, presumably during the OJPEG/JPEG work recently.

2001-12-15  Frank Warmerdam  <<EMAIL>>

	* configure, libtiff/Makefile.in: Changes for building on MacOS 10.1.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=94

	* libtiff/tif_getimage.c: If DEFAULT_EXTRASAMPLE_AS_ALPHA is 1
	(defined in tiffconf.h - 1 by default) then the RGBA interface
	will assume that a fourth extra sample is ASSOCALPHA if the
	EXTRASAMPLE value isn't set for it.  This changes the behaviour of
	the library, but makes it work better with RGBA files produced by
	lots of applications that don't mark the alpha values properly.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=93
	http://bugzilla.remotesensing.org/show_bug.cgi?id=65

2001-12-12  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: allow jpeg data stream sampling values to
	override those from tiff directory.  This makes this work with
	ImageGear generated files.

2001-12-07  Frank Warmerdam  <<EMAIL>>

	* html/Makefile.in: added missing images per bug 92.

	* port/Makefile.in: fixed clean target per bug 92.

2001-11-28  Frank Warmerdam  <<EMAIL>>

	* Reissue 3.5.7 release.

	* libtiff/mkversion.c: Fix output of TIFF_VERSION to be
	YYYYMMDD so that it is increasing over time.

	* Makefile.in: Ensure that tiffvers.h is regenerated in the
	make release target.

	* Makefile.in: added libtiff/tiffvers.h to the release file list.

2001-11-23  Frank Warmerdam  <<EMAIL>>

	* added html/v3.5.7.html, updated html/index.html.

	* Makefile.in: added contrib/addtiffo/tif_ovrcache.{c,h}.

2001-11-15  Frank Warmerdam  <<EMAIL>>

	* configure: fixed test for -lm.

2001-11-02  Frank Warmerdam  <<EMAIL>>

	* Added PHOTOMETRIC_ITULAB as per bug 90.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=90

2001-10-10  Frank Warmerdam  <<EMAIL>>

	* libtiff/tiff.h: I have created COMPRESSION_CCITT_T4,
	COMPRESSION_CCITT_T6, TIFFTAG_T4OPTIONS and TIFFTAG_T6OPTIONS aliases
	in keeping with TIFF 6.0 standard in tiff.h

	http://bugzilla.remotesensing.org/show_bug.cgi?id=83

2001-09-26  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirwrite.c: added TIFFRewriteDirectory() function.
	Updated TIFFWriteDirectory man page to include TIFFRewriteDirectory.

2001-09-24  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_lzw.c: Avoid MS VC++ 5.0 optimization bug.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=78

	* libtiff/tif_lzw.c: added dummy LZWSetupEncode() to report an
	error about LZW not being available.

	* libtiff/tif_dir.c: propagate failure to initialize compression
	back from TIFFSetField() as an error status, so applications can
	detect failure.

	* libtiff/tif_dir.c: removed the auto replacement of
	COMPRESSION_LZW with COMPRESSION_NONE in _TIFFVSetField().

	* Removed Makefile, tools/Makefile, port/install.sh, man/Makefile
	from CVS as they are all supposed to be auto-generated by configure.

2001-09-22  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_ojpeg.c: new update from Scott.

2001-09-09  Frank Warmerdam  <<EMAIL>>

	* libtif/tif_fax3.c: Removed #ifdef PURIFY logic, and modified to
	always use the "safe" version, even if there is a very slight
	cost in performance.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=54

	* libtiff/Makefile.in: Fixed @DSOSUB_VERSION to be @DSOSUF_VERSION@
	in two places.

	* libtiff/tif_getimage.c: Fixed problem with reading strips or
	tiles that don't start on a tile boundary.  Fix contributed by
	Josep Vallverdu (from HP), and further described in bug 47.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=47

	* tools/tiff2ps.c: added OJPEG YCbCr to RGB support.

	* libtiff/tif_ojpeg.c: Applied substantial patch from Scott.

2001-09-06  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_packbits.c: fixed memory overrun error.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=77

2001-08-31  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_getimage.c: relax handling of contig case where
	there are extra samples that are supposed to be ignored.  This
	should now work for 8bit greyscale or palletted images.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=75

2001-08-28  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_getimage.c: Don't complain for CMYK (separated)
	images with more than four samples per pixel.  See:

	http://bugzilla.remotesensing.org/show_bug.cgi?id=73

2001-08-10  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_getimage.c: Use memmove() instead of TIFFmemcpy()
	in TIFFReadRGBATile() to avoid issues in cases of overlapping
	buffers.  See Bug 69 in Bugzilla.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=69

	* tools/tiff2rgba.c: fixed getopt() call so that -b works again.

2001-08-09  Frank Warmerdam  <<EMAIL>>

	* libtiff/tiff.h, libtiff/tif_fax3.c: added check for __LP64__
	when checking for 64 bit architectures as per bugzilla bug 67.

2001-07-27  Frank Warmerdam  <<EMAIL>>

	* man/Makefile.in: add TIFFClientOpen link as per debian submitted
	bug 66.

2001-07-20  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_jpeg.c: Define HAVE_BOOLEAN on windows if RPCNDR.H
	has been included.

2001-07-19  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_open.c: Seek back to zero after failed read,
	before writing header.

2001-07-18  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_ojpeg.c: updates from Scott.  Handles colors
	much better.  Now depends on having patched libjpeg as per
	patch in contrib/ojpeg/*.

2001-07-17  Frank Warmerdam  <<EMAIL>>

	* */Makefile.in: added DESTDIR support.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=60

2001-07-16  Frank Warmerdam  <<EMAIL>>

	* configure, libtiff/Makefile.in: applied OpenBSD patches
	as per:

	http://bugzilla.remotesensing.org/show_bug.cgi?id=61

2001-06-28  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_getimage.c: Fixed so that failure is properly
	reported by gtTileContig, gtStripContig, gtTileSeparate and
	gtStripSeparate.

	See http://bugzilla.remotesensing.org/show_bug.cgi?id=51

	* tiffcmp.c: Fixed multi samples per pixel support for ContigCompare.
	Updated bug section of tiffcmp.1 to note tiled file issues.

	See http://bugzilla.remotesensing.org/show_bug.cgi?id=53

2001-06-22  Frank Warmerdam  <<EMAIL>>

	* configure: Changes for DSO generation on AIX provided by
	John Marquart <<EMAIL>>.

	* configure, libtiff/Makeifle.in: Modified to build DSOs properly
	on Darwin thanks to Robert Krajewski (<EMAIL>) and
	Keisuke Fujii (<EMAIL>).

2001-06-13  Frank Warmerdam  <<EMAIL>>

	* tools/tiff2rgba.c: added -n flag to avoid emitting alpha component.

	* man/tiff2rgba.1: new

2001-05-22  Frank Warmerdam  <<EMAIL>>

	* Added tiffset and tif_ojpeg to the dist lists in Makefile.in.

2001-05-13  Frank Warmerdam  <<EMAIL>>

	* libtiff/tools/thumbnail.c: changed default output compression
	to packbits from LZW since LZW isn't generally available.

2001-05-12  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_ojpeg.c: New.
	libtiff/tif_jpeg.c, tiffconf.h, tif_getimage.c: changes related
	to OJPEG support.

	Scott Marovich <<EMAIL>> supplied OJPEG support.

2001-05-11  Frank Warmerdam  <<EMAIL>>

	* tiff.h: removed, it duplicates libtiff/tiff.h.

2001-05-08  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirinfo.c: moved pixar and copyright flags to
	ensure everything is in order.

	* libtiff/libtiff.def: added TIFFCreateDirectory and
	TIFFDefaultStripSize as per:

	  http://bugzilla.remotesensing.org/show_bug.cgi?id=46

2001-05-02  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirinfo.c: Modified the TIFF_BYTE definition for
	TIFFTAG_PHOTOSHOP to use a writecount of TIFF_VARIABLE2 (-3) to
	force use of uint32 counts instead of short counts.

	* libtiff/tif_dirwrite.c: Added support for TIFF_VARIABLE2 in the
	case of writing TIFF_BYTE/TIFF_SBYTE fields.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=43

2001-05-01  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirinfo.c: removed duplicate TIFFTAG_PHOTOSHOP as per
	bug report http://bugzilla.remotesensing.org/show_bug.cgi?id=44

2001-04-05  Frank Warmerdam  <<EMAIL>>

	* tiffio.h: removed C++ style comment.

	* configure: fixed up SCRIPT_SH/SHELL handling.

	* Makefile.in: Fixed SCRIPT_SH/SHELL handling.

	* config.guess: documented more variables as per bug 40.

2001-04-03  Frank Warmerdam  <<EMAIL>>

	* configure, *Makefile.in: Various changes to improve configuration
	for HP/UX specifically, and also in general.  They include:
	 - Try to handle /usr/bin/sh instead of /bin/sh where necessary.
	 - Upgrade to HP/UX 10.x+ compiler, linker and dso options.
	 - Fixed mmap() test to avoid MMAP_FIXED ... it isn't available on HP
	 - Use -${MAKEFLAGS} in sub makes from makefiles.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=40

2001-04-02  Frank Warmerdam  <<EMAIL>>

	* libtiff/tiff.h: Applied hac to try and resolve the problem
	with the inttypes.h include file on AIX.

	See http://bugzilla.remotesensing.org/show_bug.cgi?id=39

	* VERSION: update to 3.5.7 beta in preparation for release.

	* configure/config.site: modified to check if -lm is needed for
	MACHDEPLIBS if not supplied by config.site.  Needed for Darwin.

	* config.guess: updated wholesale to an FSF version apparently
	from 1998 (as opposed to 1994).  This is mainly inspired by
	providing for MacOS X support.

2001-03-29  Frank Warmerdam  <<EMAIL>>

	* configure, Makefile.in, etc: added support for OPTIMIZER being
	set from config.site.

2001-03-28  Frank Warmerdam  <<EMAIL>>

	* fax2ps.c: Helge (libtiff at oldach.net) submitted fix:

	Here's a fix for fax2ps that corrects behaviour for non-Letter paper
	sizes. It fixes two problems:

	Without	scaling (-S) the fax is now centered on the page size specified
	with -H	and/or -W. Before, fax2ps was using an obscure and practically
	useless algorithm to allocate the image relative to Letter sized paper
	which sometime sled to useless whitespace on the paper, while at the
	same time cutting of the faxes printable area at the opposite border.

	Second, scaling now preserves aspect ratio, which makes unusual faxes
	(in particular short ones) print properly.

	See http://bugzilla.remotesensing.org/show_bug.cgi?id=35

	* tiff2ps.c/tiff2ps.1: Substantial changes to tiff2ps by
	Bruce A. Mallett.  See check message for detailed information
	on all the changes, including a faster encoder, fixes for level
	2 PostScript, and support for the imagemask operator.

2001-03-27  Frank Warmerdam  <<EMAIL>>

	* libtiff/tiffio.h: Changed "#if LOGLUV_PUBLIC" to
	"#ifdef LOGLUV_PUBLIC" so it will work with VisualAge on AIX.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=39

2001-03-16  Frank Warmerdam  <<EMAIL>>

	* tif_dirinfo.c: moved definition of copyright tag in field list.
	Apparently they have to be in sorted order by tag id.

2001-03-13  Frank Warmerdam  <<EMAIL>>

	* tif_getimage.c: Added support for 16bit minisblack/miniswhite
	images in RGBA interface.

2001-03-02  Frank Warmerdam  <<EMAIL>>

	* Added TIFFTAG_COPYRIGHT support.

2001-02-19  Frank Warmerdam  <<EMAIL>>

	* Brent Roman contributed updated tiffcp utility (and tiffcp.1)
	with support for extracting subimages with the ,n syntax, and also
	adding the -b bias removal flag.

2001-02-16  Frank Warmerdam  <<EMAIL>>

	* libtiff/libtiff.def: Brent Roman submitted new version adding
	serveral missing entry points.

	* libtiff/tif_dirinfo.c: don't declare tiffFieldInfo static on VMS.
	Some sort of weird VMS thing.

	http://bugzilla.remotesensing.org/show_bug.cgi?id=31

	* tif_luv.c/tiff.h/tiffio.h:
	New version of TIFF LogLuv (SGILOG) modules contributed by Greg Ward
	(<EMAIL>).  He writes:

	1) I improved the gamut-mapping function in tif_luv.c for imaginary
	colors, because some images were being super-saturated on the input
	side and this resulted in some strange color shifts in the output.

	2) I added a psuedotag in tiff.h to control random dithering during
	LogLuv encoding.  This is turned off by default for 32-bit LogLuv and
	on for 24-bit LogLuv output.  Dithering improves the average color
	accuracy over the image.

	3) I added a #define for LOG_LUV_PUBLIC, which is enabled by default in
	tiffio.h, to expose internal routines for converting between LogLuv and
	XYZ coordinates.  This is helpful for writing more efficient,
	specialized conversion routines, especially for reading LogLuv files.

	Changes applied with minor edits.

2001-01-23  Frank Warmerdam  <<EMAIL>>

	* tif_fax3.c: keep rw_mode flag internal to fax3 state to remember
	whether we are encoding or decoding.  This is to ensure graceful
	recovery if TIFFClientOpen() discovers an attempt to open a compressed
	file for "r+" access, and subsequently close it, as it resets the
	tif_mode flag to O_RDONLY in this case to avoid writes, confusing the
	compressor's concept of whether it is in encode or decode mode.

2001-01-08  Mike Welles <<EMAIL>>

	* Makefile.in:  Now cleaning up after itself after creating the .tar.gz and .zip

2001-01-07  Frank Warmerdam  <<EMAIL>>

	* html/libtiff.html: Fixed arguments in example for TIFFRGBAImageGet()
	as per bug report by Patrick Connor.

2000-12-28  Frank Warmerdam  <<EMAIL>>

	* Added RELEASE-DATE file to release file list.

	* Fixed libtiff/makefile.vc to make tiffvers.h not version.h.

2000-12-22  Mike Welles <<EMAIL>>
        * added link to CVS mirror from index.html

	* updated html/internals.html to note that LZW compression is
	  not supported by default.

2000-12-22  Frank Warmerdam  <<EMAIL>>

	* updated html/libtiff.html to not point at Niles' old JPL web site
	for the man pages, point at www.libtiff.org.

2000-12-21  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_apple.c: Applied "Carbon" support patches supplied by
	Leonard Rosenthol <<EMAIL>>.  May interfere
	with correct building on older systems.  If so, please let me know.

2000-12-19 Mike Welles <<EMAIL>>

	* Took out LZW Encoding from tif_lzw.c

	* Created HOWTO-RELEASE

	* Created html/v3.5.6.html

	* updated index.html

2000-12-01  Frank Warmerdam  <<EMAIL>>

	* Added patches for EOFB support in tif_fax3.c and tif_fax3.h.
	Patches supplied by Frank Cringle <<EMAIL>>
	Example file at: ftp://ftp.remotesensing.org/pub/libtiff/eofb_396.tif

2000-11-24  Frank Warmerdam  <<EMAIL>>

	* libtiff/Makefile.in: Added an installPrivateHdrs and install-private
	target so that the private headers required by libgeotiff can be
	installed with the others.  They are not installed by default.

	* libtiff/Makefile.in: Added @MACHLIBDEPS@ to LINUXdso and GNULDdso
	targets so libtiff.so will be built with an explicit dependency
	on libm.so.

	* libtiff/Makefile.in: Use softlinks to link libtiff.so.3 to
	libtiff.so.3.5.5.

	* libtiff/Makefile.in & configure: Remove all references to the ALPHA
	file, or ALPHA version logic.  Added stuff about DIST_POINT in
	place of DIST_TYPE and the alpha release number stuff.

2000-11-22  Frank Warmerdam  <<EMAIL>>

	* I have applied a patch from Steffen Moeller <<EMAIL>> to
	the configure script so that it now accepts the --prefix, and
	--exec-prefix directives.

2000-11-13  Frank Warmerdam  <warmerda@cs46980-c>

	* I have made a variety of modifications in an effort to ensure the
	TIFFLIB_VERSION macro is automatically generated from the RELEASE-DATE
	file which seems to be updated regularly.

	 o mkversion.c now reads RELEASE-DATE and emits TIFFLIB_VERSION in
	   version include file.
	 o renamed version.h to tiffvers.h because we now have to install it
	   with the public libtiff include files.
	 o include tiffvers.h in tiffio.h.
	 o updated tif_version.c to use tiffvers.h.
	 o Updated Makefile.in accordingly.

	* As per http://bugzilla.remotesensing.org/show_bug.cgi?id=25
	I have updated the win32 detection rules in tiffcomp.h.

2000-10-20  Frank Warmerdam  <warmerda@cs46980-c>

	* tif_getimage.c: Fixed RGBA translation for YCbCr images for which
	the strip/tile width and height aren't multiples of the sampling size.
	See http://bugzilla.remotesensing.org/show_bug.cgi?id=20
	Some patches from Rick LaMont of Dot C Software.

	* Modified tif_packbits.c encoder to avoid compressing more
	data than provided if rowsize doesn't factor into provided data
	(such as occurs for YCbCr).

2000-10-19  Frank Warmerdam  <warmerda@cs46980-c>

	* tools/rgb2ycbcr.c: fixed output strip size to account for vertical
	roundup if rows_per_strip not a multiple of vertical sample size.

2000-10-16  Frank Warmerdam  <warmerda@cs46980-c>

	* tif_dir.c: Clear TIFF_ISTILED flag in TIFFDefaultDirectory
	as per http://bugzilla.remotesensing.org/show_bug.cgi?id=18
	from <EMAIL>.

	* Modified tif_packbits.c decoding to avoid overrunning the
	output buffer, and to issue a warning if data needs to be
	discarded.  See http://bugzilla.remotesensing.org/show_bug.cgi?id=18

2000-10-12  Frank Warmerdam  <warmerda@cs46980-c>

	* Modified tiff2bw to ensure portions add to 100%, and that
	white is properly recovered.

	See bug http://bugzilla.remotesensing.org/show_bug.cgi?id=15
	Patch c/o Stanislav Brabec <<EMAIL>>

2000-09-30  Frank Warmerdam  <warmerda@cs46980-c>

	* Modified TIFFClientOpen() to emit an error on an attempt to
	open a comperessed file for update (O_RDWR/r+) access.  This is
	because the compressor/decompressor code gets very confused when
	the mode is O_RDWR, assuming this means writing only.  See
	bug http://bugzilla.remotesensing.org/show_bug.cgi?id=13

2000-09-27  Frank Warmerdam  <warmerda@cs46980-c>

	* Added GNULDdso target an`d switched linux and freebsd to use it.

2000-09-26  Frank Warmerdam  <warmerda@cs46980-c>

	* Applied patch for 0x0000 sequences in tif_fax3.h's definition
	of EXPAND1D() as per bug 11 (from Roman).

2000-09-25  Frank Warmerdam  <warmerda@cs46980-c>
	* Fixed tiffcomp.h to avoid win32 stuff if unix #defined, to improve
	cygwin compatibility.

	* Applied patch from Roman Shpount to tif_fax3.c.  This seems to
	be a proper fix to the buffer sizing problem.  See
	http://bugzilla.remotesensing.org/show_bug.cgi?id=11

	* Fixed tif_getimage.c to fix overrun bug with YCbCr images without
	downsampling.  http://bugzilla.remotesensing.org/show_bug.cgi?id=10
	Thanks to Nick Lamb <<EMAIL>> for reporting the
	bug and proving the patch.

2000-09-18  Frank Warmerdam  <warmerda@cs46980-c>

	* Fixed tif_jpeg.c so avoid destroying the decompressor before
	we are done access data thanks to bug report from:
	Michael Eckstein <<EMAIL>>.

	* Reverted tif_flush change.

2000-09-14  Frank Warmerdam  <warmerda@cs46980-c>

	* tif_flush.c: Changed so that TIFFFlushData() doesn't return an
	error when TIFF_BEENWRITING is not set.  This ensures that the
	directory contents can still be flushed by TIFFFlush().

2000-08-14  Frank Warmerdam  <<EMAIL>>

	* tif_open.c: Don't set MMAP for O_RDWR files.

	* tif_open.c: Set STRIPCHOP_DEFAULT for O_RDWR as well as O_RDONLY
	so that files opened for update can be strip chopped too.

	* tif_read.c: fixed up bug with files missing rowsperstrip and
	the strips per separation fix done a few weeks ago.

2000-07-17  Frank Warmerdam  <warmerda@cs46980-c>

	* Tentatively added support for SAMPLEFORMAT_COMPLEXIEEEFP, and
	SAMPLEFORMAT_COMPLEXINT.

2000-07-13  Mike Welles <<EMAIL>>

	* index.html, bugs.html: added bugzilla info.

2000-07-12  Frank Warmerdam  <<EMAIL>>

	* tif_read.c: fix subtle bug with determining the number of
	rows for strips that are the last strip in a separation but
	not the last strip of all in TIFFReadEncodedStrip().

	* Applied 16/32 bit fix to tif_fax3.c.  Fix supplied by
	Peter Skarpetis <<EMAIL>>

2000-06-15  Frank Warmerdam  <<EMAIL>>

	* Modified tiffio.h logic with regard to including windows.h.  It
	won't include it when building with __CYGWIN__.

2000-05-11  Frank Warmerdam  <warmerda@cs46980-c>

	* README: update to mention www.libtiff.org, don't list Sam's old
	email address.

	* configure: Fixed DSO test for Linux as per patch from
	  Jan Van Buggenhout <<EMAIL>>.

2000-04-21  Frank Warmerdam  <<EMAIL>>

	* libtiff/tif_dirread.c: Don't use estimate strip byte count for
	one tile/strip images with an offset, and byte count of zero. These
	could be "unpopulated" images.

2000-04-18  Frank Warmerdam  <<EMAIL>>

	* contrib/addtiffo: Added "averaging" resampling option.

	* tools/tiffsplit.c: Copy TIFFTAG_SAMPLEFORMAT.

Tue Apr 18 16:18:08 2000  Frank Warmerdam  <<EMAIL>>

	* tools/Makefile.in: Modified to install properly on SGI.

2000-04-12  Mike Welles	     <<EMAIL>>
	* configure:  Fixed stupid mistake in libc6 test on Linux

2000-04-04  Mike Welles	     <<EMAIL>>
	* tif_win32.c:  Applied patch to fix overreads and ovverwrites
	  caught by BoundsChecker.  From Arvan Pritchard
	  <<EMAIL>>  (untested).

	* tif_getimage.c:  Applied patch to silence VC6 warnings.  From
	  Arvan Pritchard <<EMAIL>>

	* tif_lzw.c:  Applied patch to silence VC6 warnings.  From
	  Arvan Pritchard <<EMAIL>>

2000-03-28  Frank Warmerdam  <warmerda@cs46980-c>

	* Added contrib/stream (stream io) code submitted by Avi Bleiweiss.

2000-03-28  Frank Warmerdam  <warmerda@cs46980-c>    *** 3.5.5 release ***

	* fax2ps: Fixed mixup of width and height in bounding box statement
	as per submission by Nalin Dahyabhai <<EMAIL>>.

2000-03-27  Mike Welles	     <<EMAIL>>

	* fax2ps:  Modified printruns to take uint32 instead of uint16.
	Patch courtesy of Bernt Herd <<EMAIL>>

2000-03-20  Mike Welles	     <<EMAIL>>

	* configure: added test for libc6 for linux targets.  Bug reported by
        Stanislav Brabec <<EMAIL>>

	* Added 3.5 docs to html/Makefile.in.
	Thanks to  Stanislav Brabec <<EMAIL>>

	* configure: fixed bugs in sed scripts
	(applied sed script s:/@:s;@:;s:/s;;:;: to configure).
	fix submitted to Stanislav Brabec <<EMAIL>>

	* tools/iptcutil was not in files list, and wasn't being
	added to tar archive.  Updated Makefile.in.

2000-03-17  Frank Warmerdam  <warmerda@cs46980-c>

	* tif_fax3.c: Fixed serious bug introduced during the uint16->uint32
	conversion for the run arrays.

2000-03-03  Frank Warmerdam  <<EMAIL>>

	* Set td_sampleformat default to SAMPLEFORMAT_UINT instead of
	SAMPLEFORMAT_VOID in TIFFDefaultDirectory() in tif_dir.c.

2000-03-02  Frank Warmerdam  <<EMAIL>>

	* Added "GetDefaulted" support for TIFFTAG_SAMPLEFORMAT in tif_aux.c.

	* Patched tif_fax3.c so that dsp->runs is allocated a bit bigger
	to avoid overruns encountered with frle_bug.tif.

Tue Feb 15 22:01:05 2000  Frank Warmerdam  <<EMAIL>>

	* Fixed tools/tiffcmp so that stopondiff testing works.
	  Patch care of Joseph Orost <<EMAIL>>.

2000-01-28    <warmerda@CS46980-B>

	* Modified tif_unix.c to support 2-4GB seeks if USE_64BIT_API is
	  set to 1, and added default (off) setting in tiffconf.h.  This
	  should eventually be set by the configure script somehow.

	  The original work on all these 2-4GB changes was done by
	  Peter Smith (<EMAIL>).

	* Modified tif_win32.c to support 2-4GB seeks.

	* tentatively changed toff_t to be unsigned instead of signed to
	  facilitate support for 2-4GB files.

	* Updated a variety of files to use toff_t.  Fixed some mixups
	  between toff_t and tsize_t.

Fri Jan 28 10:13:49 2000  Frank Warmerdam  <<EMAIL>>

	* Largely reimplemented contrib/addtiffo to avoid temp files,
	updating the TIFF file in place.  Fixed a few other bugs to.

	* Set tif_rawdatasize to zero when freeing raw data buffer in
	TIFFWriteDirectory().

	* Enabled "REWRITE_HACK" in tif_write.c by default.

	* Fix bug in tif_write.c when switching between reading one directory
	and writing to another.

	* Made TIFFWriteCheck() public, and added TIFFCreateDirectory()

Wed Jan  5 12:37:48 2000  Frank Warmerdam  <<EMAIL>>

	* Added TIFFmemory(3t) functions to libtiff.def.

Tue Jan  4 13:39:00 2000  Frank Warmerdam  <<EMAIL>>

	* Added libtiff/libtiff.def to TIFFILES distribution list.

Mon Dec 27 12:13:39 EST 1999  Mike Welles <<EMAIL>>

	* Created lzw compression kit, as a new module (libtiff-lzw-compression-kit).

	* Altered descriptions in tools to reflect "by default" lzw not supported

	* Updated index.html to note lzw compression kit.

Tue Dec 21 14:01:51 1999  Frank Warmerdam  <<EMAIL>>

	* Added fax3sm_winnt.c to distribution list in Makefile.in.

Tue Dec 21 11:04:45 EST 1999  Mike Welles <<EMAIL>> *** 3.5.4 release ***

	* Aadded Pixar tag support.  Contributed by Phil Beffery <<EMAIL>>

	* Made one more change to tif_dir.c for removal of LZW compression. Also added notice
	  when LZW compression invoked.

	* Changed default compression in tools to TIFF_PACKBITS, and changed usage descriptions
	  in tools to reflect removal of LZW compression

Mon Dec 20 18:39:02 EST 1999  Mike Welles  <<EMAIL>>

        * Fixed bug that caused LZW (non) compression to segfault. Added
	  warning about LZW compression removed being removed, and why.

	* Added nostrip to install in tools/Makefile.in so that debugging
	  symbols are kept.

Tue Dec  7 12:04:47 EST 1999  Mike Welles  <<EMAIL>>

	* Added patch from Ivo Penzar <<EMAIL>>,
	  supporting Adobe ZIP deflate.  Untested.

Sat Dec  4 15:47:11 1999  Frank Warmerdam  <<EMAIL>>

	* Made Packbits the default compression in tools/tiff2rgba.c instead
	of LZW.

Tue Nov 30 14:41:43 1999  Frank Warmerdam  <<EMAIL>>    *** 3.5.3. release ***

	* Added tif_luv to contrib/djgpp/Makefile.lib.

Tue Nov 30 14:15:32 EST 1999   Mike Welles <<EMAIL>>

        * Added zip creation to relase makefile target

	* Added html for TIFFWriteTile.3t man page.

Tue Nov 30 09:20:16 1999  Frank Warmerdam  <<EMAIL>>

	* Added some changes to tif_write.c to support rewriting existing
	fixed sized tiles and strips.  Code mods disabled by default, only
	enabled if REWRITE_HACK is defined for now.

Mon Nov 29 11:43:42 1999  Frank Warmerdam  <<EMAIL>>

	* Added TIFFWriteTile.3t man page.

Sun Nov 28 20:36:18 1999  Frank Warmerdam  <<EMAIL>>

	* Added notes on use of makefile.vc in build.html, and fixed
	email subscription address.

199-11-28  Mike Welles <<EMAIL>>

	*  Fixed apocalypse-inducing y2k bug in contrib/ras/ras2tiff.c

	*  Did some casts cleaning up to reduce compiler warnings in tif_fax3.c,
	   from Bruce Carmeron <<EMAIL>> -- modifications of
	   changes made by Frank (sun cc still complained on cast).

	*  Added tiffconf.h to install target per request from Bill
	   Radcliffe <<EMAIL>>: "We need a way for ImageMagick to
 	   know features have been compiled into the TIFF library in order to
	   handle things properly".

Sat Nov 27 16:49:21 1999  Frank Warmerdam  <<EMAIL>>

	* fixed various VC++ warnings as suggested by Gilles Vollant
	<<EMAIL>>.

Wed Nov 24 12:08:16 1999  Frank Warmerdam  <<EMAIL>>

	* Modified TIFFquery.3t man pages info on TIFFIsByteSwapped() to
	not imply applications are responsible for image data swapping.

1999-11-22  Mike Welles <<EMAIL>>
	*  HTML-ized the man pages, added to html/man

	*  Removed LZW Compression to comply with Unisys patent extortion.

1999-09-29  Mike Welles		<<EMAIL>>
	*  Corrected one remaining 16 -> 32 bit value in tif_fax3.c,
	   From Ivo Penzar <<EMAIL>.

	*  Added patch from Ivo Penzar to have TiffAdvanceDirectory handle
	   memory mapped files. <<EMAIL>>

1999-09-26  Mike Welles 	<<EMAIL>>  *** 3.5.2 release ***
	* Corrected alpha versioning.

	* Removed distinction between  alpha and release targets in Makefile.in.

	* added release.stamp target, which tags cvs tree, and updates
	  "RELEASE-DATE"

	* added releasediff target, which diffs tree with source as of
	  date in "RELEASE-DATE"

	* Ticked up version to 3.5.2 (alpha 01 -- but I think we'll moving
	  away from alpha/non-alpha distinctions).

	* updated html to reflect release

1999-09-23    <warmerda@CS46980-B>

	* Set O_BINARY for tif_unix.c open() ... used on cygwin for instance.

	* Added CYGWIN case in configure.

Fri Sep 17 00:13:51 CEST 1999  Mike Welles <<EMAIL>>

	* Applied Francois Dagand's patch to handle fax decompression bug.
	  (sizes >= 65536 were failing)

Tue Sep 14 21:31:43 1999  Frank Warmerdam  <<EMAIL>>

	* Applied "a" mode fix to tif_win32.c/TIFFOpen() as suggested
	  by Christopher Lawton <<EMAIL>>

Wed Sep  8 08:19:18 1999  Frank Warmerdam  <<EMAIL>>

	* Added IRIX/gcc, and OSF/1 4.x support on behalf of
	  Albert Chin-A-Young <<EMAIL>>

	* Added TIFFReassignTagToIgnore() API on behalf of
	  Bruce Cameron <<EMAIL>>.  Man page still pending.

Wed Aug 25 11:39:07 1999  Frank Warmerdam  <<EMAIL>>

	* Added test target in Makefile, test_pics.sh script and pics/*.rpt
	files to provide for a rudimentary testsuite.

	* Added contrib/tags back from old distribution ... fixed up a bit.

1999-08-16    <warmerda@CS46980-B>

	* Added simple makefile.vc makefiles for building with MS VC++
	on Windows NT/98/95 in console mode.  Stuff in contrib/win* make give
	better solutions for some users.

Mon Aug 16 21:52:11 1999  Frank Warmerdam  <<EMAIL>>

	* Added addtiffo (add overviews to a TIFF file) in contrib.  Didn't
	put it in tools since part of it is in C++.

1999-08-16  Michael L. Welles  <<EMAIL>>

	* Updated html/index.html with anon CVS instructions.

Mon Aug 16 13:18:41 1999  Frank Warmerdam  <<EMAIL>>

	* pre-remove so link before softlink in LINUXdso action in
	libtiff/Makefile.in to avoid failure on LINUXdso builds other than
	the first.

	* Fixed problem with cvtcmap() in tif_getimage.c modifying the
	colormaps owned by the TIFF handle itself when trying to fixup wrong
	(eight bit) colormaps.  Corrected by maintaining a private copy of
	the colormap.

	* Added TIFFReadRGBATile()/TIFFReadRGBAStrip() support in
	tif_getimage.c.

	* CVS Repository placed at remotesensing.org.  ChangeLog added.
