.\"
.\" Copyright (c) 1995 <PERSON>
.\" Copyright (c) 1995 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFBUFFER 3TIFF "November 1, 2005" "libtiff"
.SH NAME
TIFFReadBufferSetup, TIFFWriteBufferSetup \- I/O buffering control routines
.SH SYNOPSIS
.nf
.B "#include <tiffio.h>"
.sp
.BI "int TIFFReadBufferSetup(TIFF *" tif ", tdata_t " buffer ", tsize_t " size ");"
.BI "int TIFFWriteBufferSetup(TIFF *" tif ", tdata_t " buffer ", tsize_t " size ");"
.fi
.SH DESCRIPTION
The following routines are provided for client-control of the I/O buffers used
by the library. Applications need never use these routines; they are provided
only for ``intelligent clients'' that wish to optimize memory usage and/or
eliminate potential copy operations that can occur when working with images
that have data stored without compression.
.PP
.I TIFFReadBufferSetup
sets up the data buffer used to read raw (encoded) data from a file. If the
specified pointer is
.SM NULL
(zero), then a buffer of the appropriate size is allocated. Otherwise the
caller must guarantee that the buffer is large enough to hold any individual
strip of raw data.
.I TIFFReadBufferSetup
returns a non-zero value if the setup was successful and zero otherwise.
.PP
.I TIFFWriteBufferSetup
sets up the data buffer used to write raw (encoded) data to a file. If the
specified
.I size
is \-1 then the buffer size is selected to hold a complete tile or strip, or
at least 8 kilobytes, whichever is greater. If the specified
.I buffer
is 
.SM NULL
(zero), then a buffer of the appropriate size is dynamically allocated.
.I TIFFWriteBufferSetup
returns a non-zero value if the setup was successful and zero otherwise.
.SH DIAGNOSTICS
.BR "%s: No space for data buffer at scanline %ld" .
.I TIFFReadBufferSetup
was unable to dynamically allocate space for a data buffer.
.PP
.BR "%s: No space for output buffer" .
.I TIFFWriteBufferSetup
was unable to dynamically allocate space for a data buffer.
.SH "SEE ALSO"
.BR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
