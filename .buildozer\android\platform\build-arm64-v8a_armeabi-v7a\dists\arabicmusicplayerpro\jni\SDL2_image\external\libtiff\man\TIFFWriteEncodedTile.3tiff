.\"
.\" Copyright (c) 1988-1997 <PERSON>
.\" Copyright (c) 1991-1997 Silicon Graphics, Inc.
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFWriteEncodedTile 3TIFF "December 16, 1991" "libtiff"
.SH NAME
TIFFWritedEncodedTile \- compress and write a tile of data to an open
.SM TIFF
file
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.BI "tsize_t TIFFWriteEncodedTile(TIFF *" tif ", ttile_t " tile ", tdata_t " buf ", tsize_t " size ")"
.SH DESCRIPTION
Compress
.I size
bytes of raw data from
.I buf
and
.B append
the result to the end of the specified tile. Note that the value of
.I tile
is a ``raw tile number.'' That is, the caller must take into account whether
or not the data are organized in separate places (\c
.IR PlanarConfiguration =2).
.IR TIFFComputeTile
automatically does this when converting an (x,y,z,sample) coordinate quadruple
to a tile number.
.SH NOTES
The library writes encoded data using the native machine byte order. Correctly
implemented
.SM TIFF
readers are expected to do any necessary byte-swapping to correctly process
image data with BitsPerSample greater than 8.
.SH "RETURN VALUES"
\-1 is returned if an error was encountered. Otherwise, the value of
.IR size 
is returned.
.SH DIAGNOSTICS
All error messages are directed to the
.BR TIFFError (3TIFF)
routine.
.PP
\fB%s: File not open for writing\fP.
The file was opened for reading, not writing.
.PP
\fBCan not write tiles to a stripped image\fP.
The image is assumed to be organized in strips because neither of the
.I TileWidth
or
.I TileLength
tags have been set with
.BR TIFFSetField (3TIFF).
.PP
\fB%s: Must set "ImageWidth" before writing data\fP. The image's width has not
be set before the first write. See
.BR TIFFSetField (3TIFF)
for information on how to do this.
.PP
\fB%s: Must set "PlanarConfiguration" before writing data\fP. The organization
of data has not be defined before the first write. See
.BR TIFFSetField (3TIFF)
for information on how to do this.
.PP
\fB%s: No space for tile arrays"\fP.
There was not enough space for the arrays that hold tile offsets and byte
counts.
.SH "SEE ALSO"
.BR TIFFOpen (3TIFF),
.BR TIFFWriteTile (3TIFF),
.BR TIFFWriteRawTile (3TIFF),
.BR libtiff (3TIFF)
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
