HOWTO-RELEASE:

Notes on releasing.

0. Make sure that you have current FSF releases of autoconf, automake,
   and libtool packages installed under a common installation prefix
   and that these tools are in your executable search path prior to
   any other installed versions.  Versions delivered with Linux may be
   altered so it is best to install official FSF releases. GNU 'm4'
   1.4.16 is needed in order to avoid bugs in m4. These packages may
   be downloaded from the following ftp locations:

     m4       - ftp://ftp.gnu.org/pub/gnu/m4
     autoconf - ftp://ftp.gnu.org/pub/gnu/autoconf
     automake - ftp://ftp.gnu.org/pub/gnu/automake
     libtool  - ftp://ftp.gnu.org/pub/gnu/libtool

   It is recommended to install m4 before autoconf, since the later
   requires the former.

   Release builds should only be done on a system with a functioning
   and correctly set system clock and on a filesystem which accurately
   records file update times.  Use of GNU make is recommended.

1. Commit any unsaved changes.

2. Use gnulib's gitlog-to-changelog script to add new (since the last
   release) entries to the traditional ChangeLog file.  Take note of
   the most recent change note time stamp and use that as the starting
   point.  Concatenate the old logs to the new logs.  The added logs
   may be used to help produce the release notes (in next step).  For
   example:

     gitlog-to-changelog --since=`head -n 1 ChangeLog | sed -e 's/ .*//g'` --append-dot > ChangeLog.added
     printf "\n" >> ChangeLog.added
     cat ChangeLog.added ChangeLog > ChangeLog.new
     mv ChangeLog.new ChangeLog
     rm ChangeLog.added

3. Create html/vX.X.html and add it to git with 'git add html/vX.X.html'.
   Take ChangeLog entries and html-ify in there.
   Easist thing to do is take html/vX.(X-1).html and use it as a template.

4. Add vX.X.html file to the list of 'docfiles' files in the html/Makefile.am.

5. Update html/index.html to refer to this new page as the current release.

6. Increment the release version in configure.ac.  Put 'alpha' or
   'beta' after the version, if applicable.  For example:

     4.0.0
      or
     4.0.0beta7

   Version should be updated in two places: in the second argument of the
   AC_INIT macro and in LIBTIFF_xxx_VERSION variables.

7. Update library ELF versioning in configure.ac (LIBTIFF_CURRENT,
   LIBTIFF_REVISION, and LIBTIFF_AGE).  These numbers have nothing to
   do with the libtiff release version numbers.

   Note that as of libtiff 4.X, proper ELF versioning is used so
   please follow the rules listed in configure.ac.  At a bare minimum,
   you should increment LIBTIFF_REVISION for each release so that
   installed library files don't overwrite existing files.  If APIs
   have been added, removed, or interface structures have changed,
   then more care is required.

8. Add an entry to Changelog similar to:

     * libtiff 4.0.0 released.

9. In the source tree do

     ./autogen.sh

   This step may be skipped if you have already been using a
   maintainer build with current autoconf, automake, and libtool
   packages.  It is only needed when updating tool versions.

10. It is recommended (but not required) to build outside of the source
   tree so that the source tree is kept in a pristine state.  This
   also allows sharing the source directory on several networked
   systems.  For example:

     mkdir libtiff-build
     cd libtiff-build
     /path/to/libtiff/configure --enable-maintainer-mode

   otherwise do

     ./configure --enable-maintainer-mode

11. In the build tree do

     make release

   This will update "RELEASE-DATE", "VERSION", and libtiff/tiffvers.h
   in the source tree.

12. In the source tree, verify that the version info in RELEASE-DATE,
   VERSION and libtiff/tiffvers.h is right.

13. In the build tree do

      make
      make distcheck

    If 'make distcheck' fails, then correct any issues until it
    succeeds.

    Two files with names tiff-version.tar.gz and tiff-version.zip will
    be created in the top level build directory.

14. In the source tree do

      'git status', 'git add .', 'git commit' and 'git push'

15. In the source tree, create a signed tag

      git tag -s v4.0.0 -m "Create tag for v4.0.0"

    (or the appropriate name for the release)

    and push it to "origin" (if "origin" points to the official repository)

      git push origin v4.0.0

16. Sign the release files in the build tree using your private key

      export GPG_TTY=$(tty)
      for file in tiff-*.tar.gz tiff-*.zip ; do \
        gpg2 --output ${file}.sig --detach-sig $file ; \
      done

      for file in tiff-*.tar.gz tiff-*.zip ; do \
        gpg2 --verify ${file}.sig $file ; \
      done

17. Copy release packages from the build tree to the
    ftp.remotesensing.org ftp site.

      scp tiff-*.tar.gz tiff-*.tar.gz.sig tiff-*.zip tiff-*.zip.sig \
         <EMAIL>:/osgeo/download/libtiff

18. Announce to list, <EMAIL>
