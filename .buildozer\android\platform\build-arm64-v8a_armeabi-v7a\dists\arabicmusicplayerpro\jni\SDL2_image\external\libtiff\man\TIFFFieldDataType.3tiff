.\"
.\" Copyright (c) 2012, <PERSON> <<EMAIL>>
.\"
.\" Permission to use, copy, modify, distribute, and sell this software and 
.\" its documentation for any purpose is hereby granted without fee, provided
.\" that (i) the above copyright notices and this permission notice appear in
.\" all copies of the software and related documentation, and (ii) the names of
.\" Sam Leffler and Silicon Graphics may not be used in any advertising or
.\" publicity relating to the software without the specific, prior written
.\" permission of <PERSON> and Silicon Graphics.
.\" 
.\" THE SOFTWARE IS PROVIDED "AS-IS" AND WITHOUT WARRANTY OF ANY KIND, 
.\" EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY 
.\" WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.  
.\" 
.\" IN NO EVENT SHALL SAM LEFFLER OR SILICON GRAPHICS BE LIABLE FOR
.\" ANY SPECIAL, INCIDENTAL, INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND,
.\" OR ANY DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS,
.\" WHETHER OR NOT ADVISED OF THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF 
.\" LIABILITY, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE 
.\" OF THIS SOFTWARE.
.\"
.if n .po 0
.TH TIFFFieldDataType 3TIFF "July 26, 2012" "libtiff"
.SH NAME
TIFFFieldDataType \- Get TIFF data type from field information
.SH SYNOPSIS
.B "#include <tiffio.h>"
.sp
.BI "TIFFDataType TIFFFieldDataType(const TIFFField* " fip ")"
.SH DESCRIPTION
.BR TIFFFieldDataType
returns the data type stored in a TIFF field.
.P
.I fip
is a field information pointer previously returned by
.BR TIFFFindField ,
.BR TIFFFieldWithTag ,
or
.BR TIFFFieldWithName .
.br
.SH "RETURN VALUES"
.br
.BR TIFFFieldDataType
returns a member of the enum type
.BR TIFFDataType .
.br
.SH "SEE ALSO"
.BR libtiff (3TIFF),
.PP
Libtiff library home page:
.BR http://www.simplesystems.org/libtiff/
